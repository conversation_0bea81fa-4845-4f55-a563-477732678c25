import path from 'path';
import process from 'process';

import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import tsconfigPaths from 'vite-tsconfig-paths';

// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  esbuild: {
    // drop: ['console', 'debugger'],
  },
  css: {
    // Enable CSS sourcemap for easier CSS debugging
    devSourcemap: true,
  },
  plugins: [
    react(),
    // Synchronize the path settings alias in tsconfig.json
    tsconfigPaths(),
    createSvgIconsPlugin({
      // Specify the folder for icons that need to be cached
      iconDirs: [path.resolve(process.cwd(), 'public/icons')],
      // Specify symbolId format
      symbolId: 'icon-[dir]-[name]',
    }),
  ],
  server: {
    // Automatically open the browser
    open: true,
    host: true,
    port: 5174,
    // proxy: {
    //   '/api': {
    //     target: 'http://localhost:8001', //for use the original backend server change the target url
    //     changeOrigin: true,
    //     // rewrite: (path) => path.replace(/^\/api/, ''),
    //   },
    // },
  },
  build: {
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        // Remove console in the production environment
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
});
