<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      <%- isValid ? 'Successful Email Update' : 'Failed Email Update' %>
    </title>

    <link rel="icon" href="/favicon.png" type="image/png" />

    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <style>
      body {
        height: 100vh;
        margin: 0;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        min-height: 100vh;
        background-color: #B4B4B4;
        font-family: 'Exo', serif;
        font-optical-sizing: auto;
      }
      header {
        width: 100%;
        background-color: #007AFF;
        padding: 10px 0;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
        margin-bottom: 5rem;
      }
      header .logo {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      header .logo img {
        height: 50px;
        width: 190px;
      }
      .card {
        width: 90%;
        max-width: 400px;
        border-radius: 10px;
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
        background: #f5c08a;
        text-align: center;
        padding: 20px;
        border: 1px solid #f5c2c7;
        display: flex;

        flex-direction: column;
        justify-content: space-between;
        align-items: center;
      }
      .card-title-success {
        font-size: 1.8rem;
        margin-bottom: 10px;
      }

      .card-title-failed {
        font-size: 1.8rem;
        margin-bottom: 10px;
      }
      .card-text {
        font-size: 1rem;
        color: #555;
        margin-bottom: 20px;
      }
      .card svg {
        width: 60px;
        height: 60px;
      }
      .cross-icon {
        width: 80px;
        height: 80px;
        border-radius: 100%;
        background-color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
        margin-bottom: 10px;
      }
      .checkmark-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #28a745;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
        margin-bottom: 10px;
      }
      .checkmark-circle svg {
        width: 60px;
        height: 60px;
        fill: white;
      }
      .success {
        color: #28a745;
      }
      .failed {
        color: red;
      }
    </style>

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Exo:ital,wght@0,100..900;1,100..900&display=swap"
      rel="stylesheet"
    />
  </head>

  <body>
    <!-- Header Section -->
    <header>
      <div class="logo">
        <img src="/logo.png" alt="nestjs-starter" />
      </div>
    </header>

    <!-- Card Section -->
    <div class="card">
      <div class="cross-icon">
        <%- isValid ? `<svg
          class="w-6 h-6 text-gray-800 dark:text-white"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          fill="green"
          viewBox="0 0 24 24"
        >
          <path
            fill-rule="evenodd"
            d="M12 2c-.791 0-1.55.314-2.11.874l-.893.893a.985.985 0 0 1-.696.288H7.04A2.984 2.984 0 0 0 4.055 7.04v1.262a.986.986 0 0 1-.288.696l-.893.893a2.984 2.984 0 0 0 0 4.22l.893.893a.985.985 0 0 1 .288.696v1.262a2.984 2.984 0 0 0 2.984 2.984h1.262c.261 0 .512.104.696.288l.893.893a2.984 2.984 0 0 0 4.22 0l.893-.893a.985.985 0 0 1 .696-.288h1.262a2.984 2.984 0 0 0 2.984-2.984V15.7c0-.261.104-.512.288-.696l.893-.893a2.984 2.984 0 0 0 0-4.22l-.893-.893a.985.985 0 0 1-.288-.696V7.04a2.984 2.984 0 0 0-2.984-2.984h-1.262a.985.985 0 0 1-.696-.288l-.893-.893A2.984 2.984 0 0 0 12 2Zm3.683 7.73a1 1 0 1 0-1.414-1.413l-4.253 4.253-1.277-1.277a1 1 0 0 0-1.415 1.414l1.985 1.984a1 1 0 0 0 1.414 0l4.96-4.96Z"
            clip-rule="evenodd"
          /></svg
        >` : `<svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          fill="red"
          viewBox="0 0 24 24"
        >
          <path
            fill-rule="evenodd"
            d="M12 10.586l4.95-4.95a1 1 0 0 1 1.415 1.415L13.414 12l4.95 4.95a1 1 0 0 1-1.415 1.415L12 13.414l-4.95 4.95a1 1 0 0 1-1.415-1.415L10.586 12 5.636 7.05a1 1 0 0 1 1.415-1.415L12 10.586z"
            clip-rule="evenodd"
          /></svg
        >` %>
      </div>

      <h1 class="card-title <%= isValid ? 'success' : 'failed' %>">
        <%- isValid ? 'Verification Successful' : 'Verification Failed' %>
      </h1>
      <p class="card-text">
        <%- isValid ? `Your email has been updated successfully.` :
        `Unfortunately, we couldn\'t update your email. <br />
        You email might have already updated, or your token may have expired.
        <br />
        Please try updating your email again.` %>
      </p>
    </div>
    <!-- Bootstrap JavaScript for better mobile interaction -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
