
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { CartridgeModal } from './cartridgeModal.schema';
import { User } from 'models/user';

@Schema({ timestamps: true })
export class UserCartridgeStockModal extends Document<Types.ObjectId>  {
  @Prop({ type: Types.ObjectId, required: true, ref:User.name })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: CartridgeModal.name })
  cartridgeId: Types.ObjectId;

  @Prop({ type: Number, required: true,min:0 })
  quantity: number;
}

export const UserCartridgeStockSchema = SchemaFactory.createForClass(UserCartridgeStockModal);

UserCartridgeStockSchema.index({ userId: 1, cartridgeId: 1 }, { unique: true });
