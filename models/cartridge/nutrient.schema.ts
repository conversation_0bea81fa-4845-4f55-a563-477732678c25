import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Types } from "mongoose";

export enum NutrientType {
  MACRO = 'macro',
  MICRO = 'micro',
  OTHER = 'other',
}

// Nutrient schema for individual nutrients in a cartridge
@Schema()
export class Nutrient extends Document<Types.ObjectId> {
  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: String, required: true, enum: NutrientType })
  type: NutrientType;
}

export const NutrientSchema = SchemaFactory.createForClass(Nutrient);

NutrientSchema.index({ name: 1 }, { collation: { locale: "en", strength: 2 } });

