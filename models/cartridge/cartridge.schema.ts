
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { CartridgeModal } from './cartridgeModal.schema';
import { User } from 'models/user';

@Schema({ timestamps: true })
export class Cartridge extends Document<Types.ObjectId> {
    @Prop({ type: String, required: true, index: true })
    cartridgeId: string;

    @Prop({ type: Types.ObjectId, required: true,ref: CartridgeModal.name })
    cartridgeModalId: Types.ObjectId;

    @Prop({ type: Number, required: true })
    tablets: number;

    @Prop({ type: Types.ObjectId, required: true,ref:User.name})
    addedBy: Types.ObjectId;

    @Prop({ type: Boolean, required: false,default: false })
    isDeleted: boolean;

    @Prop({ type: Number, required: false, default: 5 })
    currentCount: number;
}

export const CartridgeSchema = SchemaFactory.createForClass(Cartridge);

CartridgeSchema.index({ cartridgeId: 1 ,isDeleted:1 }, { unique: true });
