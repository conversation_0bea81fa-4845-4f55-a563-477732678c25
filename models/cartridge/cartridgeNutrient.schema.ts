import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Nutrient } from "./nutrient.schema";
import { Document, Types } from "mongoose";

// Nutrient schema for individual nutrients in a cartridge
@Schema()
export class CartridgeNutrient extends Document<Types.ObjectId> {
  @Prop({ type: Types.ObjectId, required: true, ref: Nutrient.name })
  nutrientId: Types.ObjectId;

  @Prop({ type: Object, required: false })
  rda?: {
    quantity: number;
    unit: string;
    percentage: number | null;
  };
}

export const CartridgeNutrientSchema = SchemaFactory.createForClass(CartridgeNutrient);
