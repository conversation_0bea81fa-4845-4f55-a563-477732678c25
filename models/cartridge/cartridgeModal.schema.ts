import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { CartridgeNutrient, CartridgeNutrientSchema } from './cartridgeNutrient.schema';

@Schema({ _id: false })
export class CartridgeColor {
  @Prop({ type: String, required: true })
  cartridge: string;

  @Prop({ type: String, required: true })
  tablet: string;
}

export const CartridgeColorSchema = SchemaFactory.createForClass(CartridgeColor);

@Schema({ timestamps: true })
export class CartridgeModal extends Document<Types.ObjectId> {
  @Prop({ type: String, required: true, index: true })
  healthArea: string;

  @Prop({ type: String, required: true, unique: true, index: true })
  healthName: string;

  @Prop({ type: [CartridgeNutrientSchema], required: true })
  nutrients: CartridgeNutrient[];

  @Prop({ type: String, required: true })
  flavour: string;

  @Prop({ type: CartridgeColorSchema, required: true })
  colour: CartridgeColor;
}

export const CartridgeModalSchema = SchemaFactory.createForClass(CartridgeModal);
