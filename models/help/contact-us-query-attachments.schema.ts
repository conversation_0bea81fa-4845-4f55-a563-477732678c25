import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { UserFileUploads } from 'models/file-upload/file-upload.schema';
import { Document, Types } from 'mongoose';

export enum CONTACTUS_ATTACHMENTS_TYPES {
  VIDEO = 'video',
  IMAGE = 'image',
}

@Schema({ timestamps: false, _id: false })
export class ContactUs_Query_Attachments extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: UserFileUploads.name })
  fileId: Types.ObjectId;
  
  @Prop({ type: String, enum: CONTACTUS_ATTACHMENTS_TYPES, required: true })
  type: CONTACTUS_ATTACHMENTS_TYPES;
}

export const ContactUs_Query_Attachments_Schema = SchemaFactory.createForClass(
  ContactUs_Query_Attachments,
);
