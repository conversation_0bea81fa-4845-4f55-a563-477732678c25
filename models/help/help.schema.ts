import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class Help extends Document<Types.ObjectId> {
  @Prop({ type: String, required: true })
  title: string;

  @Prop({ type: String, required: true })
  description: string;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;
  
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  createdBy: Types.ObjectId;
}

export const HelpSchema = SchemaFactory.createForClass(Help);

HelpSchema.index(
  { title: 1, isDeleted: 1 },
  {
    unique: true,
  }
);