import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'models/user';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class Faq extends Document<Types.ObjectId> {
  @Prop({ type: String, required: true })
  question: string;

  @Prop({ type: String, required: true })
  answer: string;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;
  
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  createdBy: Types.ObjectId;
}

export const FaqSchema = SchemaFactory.createForClass(Faq);

FaqSchema.index(
  { question: 1, isDeleted: 1 },
  {
    unique: true,
  }
);
