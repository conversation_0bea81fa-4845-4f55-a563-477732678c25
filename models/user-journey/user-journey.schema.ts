import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class UserJourneyPerDay extends Document<Types.ObjectId> {
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  userId: Types.ObjectId;

  @Prop({ required: true })
  age: number;

  @Prop({ required: true })
  time: Date;

  createdAt: Date;
  updatedAt: Date;
}

export const UserJourneyPerDaySchema =
  SchemaFactory.createForClass(UserJourneyPerDay);

UserJourneyPerDaySchema.index({ userId: 1, time: 1 }, { unique: true });
