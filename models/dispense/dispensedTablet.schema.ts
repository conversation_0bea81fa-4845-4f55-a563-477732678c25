
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Cartridge } from 'models/cartridge/cartridge.schema';
import { CartridgeModal } from 'models/cartridge/cartridgeModal.schema';
import { User } from 'models/user';

import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class DispensedTablet extends Document<Types.ObjectId> {
  @Prop({ type: Types.ObjectId, required: true, ref: Cartridge.name })
  cartridgeId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true, ref: CartridgeModal.name })
  cartridgeModalId: Types.ObjectId;

  @Prop({ type: Date, required: true })
  dispensedAt: Date;

  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  dispensedBy: Types.ObjectId;

  @Prop({ type: Boolean, required: false, default: false })
  isDeleted: boolean;
}

export const DispensedTabletSchema = SchemaFactory.createForClass(DispensedTablet);
