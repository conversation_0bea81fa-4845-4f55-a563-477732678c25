import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { User } from 'models/user';

@Schema({ timestamps: true })
export class DailyDose extends Document<Types.ObjectId> {
  @Prop({ type: Types.ObjectId, required: true, ref: User.name })
  userId: Types.ObjectId;

  @Prop({ type: Date, required: true })
  dateUTC: Date; // exact timestamp of last intake or creation

  @Prop({ type: String, required: true })
  dateLocal: string; // "YYYY-MM-DD" in user's timezone

  @Prop({ type: Number, required: true, default: 0 })
  tabletsTaken: number; // number of tablets taken today

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;
}

export const DailyDoseSchema = SchemaFactory.createForClass(DailyDose);
