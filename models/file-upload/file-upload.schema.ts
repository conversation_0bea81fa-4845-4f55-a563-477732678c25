import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class UserFileUploads extends Document<Types.ObjectId> {
  @Prop({ required: true })
  fileName: string;

  @Prop({ required: true })
  fileType: string;

  @Prop({ required: true })
  size: number;

  @Prop({ required: true })
  s3Key: string;

  @Prop({ required: true })
  s3Url: string;

  @Prop({ required: true })
  expiryAt: Date;

  @Prop({ default: false })
  isExpired: boolean;

  createdAt: Date;
  updatedAt: Date;
}

export const UserFileUploadsSchema =
  SchemaFactory.createForClass(UserFileUploads);
