import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import * as argon2 from 'argon2';
import { EncryptionService } from 'src/common/services';
import { ConfigService } from '@nestjs/config';
import * as moment from 'moment-timezone';

export enum ROLE_VALUES {
  ADMIN = 'admin',
  USER = 'user',
}

export enum GENDER_TYPES {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

export enum ActivityLevelEnum {
  SEDENTARY = 'sedentary',
  LIGHTLY_ACTIVE = 'lightlyActive',
  MODERATELY_ACTIVE = 'moderatelyActive',
  VERY_ACTIVE = 'veryActive',
  EXTREMELY_ACTIVE = 'extremelyActive'
}

@Schema({ timestamps: true })
export class User extends Document<Types.ObjectId> {
  @Prop({ type: String, required: true, unique: true })
  email: string;

  @Prop({
    type: String,
    required: true,
    minlength: [8, 'password must be of atleast 8 letters !!'],
    select: false,
  })
  password: string;

  @Prop({ type: String, enum: ROLE_VALUES, default: ROLE_VALUES.USER })
  role: ROLE_VALUES;

  @Prop({
    type: String,
    required: true,
    minlength: [3, 'First name must be of atleast 3 letters !!'],
  })
  firstName: string;

  @Prop({
    type: String,
    required: true,
    minlength: [3, 'Last name must be of atleast 3 letters !!'],
  })
  lastName: string;

  @Prop({ 
    type: Types.ObjectId,
    ref: 'UserFileUploads',
    required: false,
  })
  
  profilePic: Types.ObjectId;

  @Prop({ 
    type: String,
    required: false,
    validate: {
      validator: (v: string) =>
        moment(v, "DD-MM-YYYY", true).isValid(),
      message: (props: any) =>
        `${props.value} is not a valid date! Must be in DD-MM-YYYY format.`,
    },
  })
  dob: string;

  @Prop({ 
    type: String,
    required: false,
    enum: GENDER_TYPES
  })
  gender: GENDER_TYPES;

  @Prop({ 
    type: Number,
    required: false,
    min: [100, 'Height must be greater than and equal to 100cm !!'],
    max: [200, 'Height must be less than or equal to 200cm !!']
  })
  height: number;

  @Prop({ 
    type: Number,
    required: false,
    min: [30, 'Weight must be greater than and equal to 30 !!'],
    max: [300, 'Weight must be less than or equal to 300 !!']
  })
  weight: number;

  @Prop({ 
    type: String,
    required: false,
  })
  city: string;

  @Prop({ 
    type: String,
    required: false,
  })
  state: string;

  @Prop({ 
    type: String,
    required: false,
    default: null 
  })
  country: string | null;

  @Prop({ type: Boolean, default: false })
  isEmailVerified: boolean;

  @Prop({ type: Boolean, default: false })
  isAccountCompleted: boolean;

  @Prop({ type: Boolean, default: false })
  isDeleted: boolean;

  @Prop({ type: Boolean, required: true })
  acceptTerms: boolean;

  @Prop({ type: String, required: true, enum: moment.tz.names() })
  timeZone: string;

  @Prop({ type: String, required: false, enum: ActivityLevelEnum })
  activityLevel: ActivityLevelEnum;
}

export const UserSchema = SchemaFactory.createForClass(User);

UserSchema.pre('save', async function (next) {
  const user = this as any;

  const encryptionService = new EncryptionService(new ConfigService());

  if (user.email) {
    user.email = encryptionService.encrypt(user.email);
  }

  if (user.password) {
    user.password = await argon2.hash(user.password);
  }

  next();
});
