SERVER_PORT= 4000
NODE_ENV= development
FRONTEND_URL= http://localhost:3000

MONGO_USERNAME= mayank<PERSON>hi
MONGO_PASSWORD= nARkDrNdC4rRTtBT
MONGO_HOST= snb.yf20arq.mongodb.net
MONGO_PORT= 27017
MONGO_DATABASE= snb_db

REDIS_TYPE = 'single'
REDIS_URL = 'redis://localhost:6379'

VERIFICATION_TOKEN_EXPIRY = 86400000 # 24 hrs

JWT_ACCESS_TOKEN_SECRET = 'kjbiyfiohnvwhewhewhouw094unwnkwhehrwhjvowp'
JWT_ACCESS_TOKEN_LIFETIME = '1h'
ACCESS_TOKEN_EXPIRY = 86400000  # 24 hrs

JWT_REFRESH_TOKEN_SECRET = 'owiinskdnvlwe0oir3nkljsdjflkdsnf'
JWT_REFRESH_TOKEN_LIFETIME = '30d'
REFRESH_TOKEN_EXPIRY = 2592000000  # 30 days

AWS_SES_ACCESS_KEY_ID = ********************
AWS_SES_SECRET_ACCESS_KEY = FxdygWGLoQ5kjow+87qO2oyCpg7AqHEkcZHqKuQU
AWS_SES_REGION = us-east-1
AWS_SES_EMAIL = <EMAIL>

AWS_S3_ACCESS_KEY_ID = ********************
AWS_S3_SECRET_ACCESS_KEY = GghV9vIOXS28oUcVX0Z+o9LOPl/g0VRwJvlOijb+
AWS_S3_REGION = ap-south-1
AWS_S3_BUCKET_NAME = dev-snb

APP_ADMIN_MAIL= <EMAIL>
APP_ADMIN_FIRST_NAME= Mayank
APP_ADMIN_LAST_NAME= Joshi
APP_ADMIN_PASSWORD= admin@snb
APP_ADMIN_TIMEZONE= Asia/Kolkata

ENCRYPTION_KEY = 'c1b96d4$@$@#@#$#$%#^$&T^%&%^%^&%&&$%$$#$#@#'
ENCRYPTION_IV = 'a9ebc6af9d8f9a2f367bed5b7b3896b7'
ENCRYPTION_SALT = '10'


# ClamAV Config
CLAMAV_HOST=localhost
CLAMAV_PORT=3310
FILE_EXPIRY_DAYS=7
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/plain