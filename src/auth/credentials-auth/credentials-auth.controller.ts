import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Put,
  Render,
  Req,
  UseGuards,
} from '@nestjs/common';
import { CredentialsAuthService } from './credentials-auth.service';
import {
  LoginReqDto,
  LoginResDto,
  RegisterReqDto,
  RegisterResDto,
  forgotPassReqDTO,
  forgotPassResDTO,
  ResetPassReqDTO,
  ResetPassResDTO,
  ResendVerificationEmailResDTO,
  ResendVerificationEmailReqDTO,
} from './credentials-auth-dto';
import { ApiBearerAuth, ApiResponse, ApiTags, ApiParam } from '@nestjs/swagger';
import { Request } from 'express';
import { ErrorResponse } from 'src/utils/responses';
import { AuthGuard } from 'src/middlewares';
import { UserRepoService } from 'src/repo/user-repo.service';
import { RefreshAccessTokenReqDTO } from './credentials-auth-dto/refreshAccessToken-req.dto';

@ApiTags('Authentication')
@Controller()
export class CredentialsAuthController {
  constructor(
    private readonly credentialsAuthService: CredentialsAuthService,
    private readonly userRepo: UserRepoService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Registers a new user.',
    type: RegisterResDto,
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @Post('/register')
  async register(@Body() registerData: RegisterReqDto, @Req() req: Request) {
    return this.credentialsAuthService.register(registerData, req);
  }

  @ApiResponse({
    status: 200,
    description: 'Verifies the email confirmation token.',
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @ApiParam({
    name: 'token',
    description: 'Verification token sent to the user email.',
  })
  @Get('/verify_user/:token')
  @Render('verification-result')
  async verify(@Param('token') token: string) {
    const isValid =
      await this.credentialsAuthService.validateVerificationToken(token);
    return {
      isValid,
    };
  }

  @ApiResponse({
    status: 200,
    description: 'Verifies the email update token.',
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @ApiParam({
    name: 'token',
    description: 'Email update token sent to the user email.',
  })
  @Get('/email_update/:emailUpdateToken')
  @Render('email-update-result')
  async updateUserEmail(@Param('emailUpdateToken') emailUpdateToken: string) {
    const isValid =
      await this.credentialsAuthService.validateEmailUpdateToken(
        emailUpdateToken,
      );
    return {
      isValid,
    };
  }

  @ApiResponse({
    status: 200,
    description: 'Authenticates and logs in a user.',
    type: LoginResDto,
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @Post('login')
  async login(@Body() loginData: LoginReqDto) {
    return this.credentialsAuthService.login(loginData);
  }
  
  @ApiResponse({
    status: 200,
    description: 'Logs out the user by invalidating the token.',
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @ApiBearerAuth()
  @Get('logout/:deviceId')
  @UseGuards(AuthGuard)
  async logout(@Req() req: Request, @Param('deviceId') deviceId: string) {
    const authHeader = req.headers.authorization;
    return this.credentialsAuthService.logout(authHeader, deviceId);
  }
  @ApiResponse({
    status: 200,
    description: 'Sends a forgot password email with a reset link.',
    type: forgotPassResDTO,
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @Put('forget_password')
  async ForgotPassword(
    @Body() forgotPassData: forgotPassReqDTO,
    @Req() req: Request,
  ) {
    return this.credentialsAuthService.sendForgotPassEmail(forgotPassData, req);
  }
  @ApiResponse({
    status: 200,
    description: 'Validates the forgot password reset token.',
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @ApiParam({
    name: 'token',
    description: 'Password reset token sent in the email.',
  })
  @Get('/forget_password/:token')
  @Render('forgot-pass-result')
  async forgotPass(@Param('token') token: string) {
    const isValid =
      await this.credentialsAuthService.validateForgotPassToken(token);
    return {
      isValid,
    };
  }
  @ApiResponse({
    status: 200,
    description: 'Resets the user password.',
    type: ResetPassResDTO,
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @Put('reset_password')
  async ResetPassword(@Body() resetPassData: ResetPassReqDTO) {
    return this.credentialsAuthService.resetPassword(resetPassData);
  }
  @ApiResponse({
    status: 200,
    description: 'Resend user verification email link.',
    type: ResendVerificationEmailResDTO,
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @Put('/resend_verification_email')
  async resendVerificationEmail(
    @Body() body: ResendVerificationEmailReqDTO,
    @Req() req: Request,
  ) {
    const { email } = body;
    const user = await this.userRepo.findUserByEmail(email, false);

    if (!user) {
      throw new NotFoundException(
        "This account doesn't seem to exist. Please sign up to get started!",
      );
    }

    return this.credentialsAuthService.resendVerificationEmail(user, req);
  }

  @ApiResponse({
    status: 200,
    description: 'Refreshes the access token.',
    type: ResendVerificationEmailResDTO,
  })
  @ApiResponse({
    status: 404,
    type: ErrorResponse,
    description: 'Error Response',
  })
  @Put('/token/refresh')
  async refreshAccessToken(@Body() body: RefreshAccessTokenReqDTO) {
    return this.credentialsAuthService.refreshAccessToken(body.refreshToken);
  }
}
