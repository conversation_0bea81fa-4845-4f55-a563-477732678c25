import { ApiProperty } from '@nestjs/swagger';
import { UserPopulatedProfileDTO } from 'src/user/user/user-dto/userPopulateProfile.dto';
import { BaseResponse } from 'src/utils/responses';

export class LoginResDto extends BaseResponse {
  @ApiProperty({
    example: 'Logged In successfully !!',
  })
  msg: string;

  @ApiProperty({
    example: 'Random value',
  })
  accessToken: string;

  @ApiProperty({
    example: 'Random value',
  })
  refreshToken: string;
  
  @ApiProperty({
    example: 'timeStamp',
  })
  expiry: Date;

  @ApiProperty({
    type: UserPopulatedProfileDTO,
  })
  user: UserPopulatedProfileDTO;
}
