import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export class RefreshAccessTokenResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Message indicating the status of the refresh token operation',
    example: 'Access token refreshed successfully',
  })
  msg: string;

  @ApiProperty({
    description: 'The new access token',
    example: 'newAccessToken',
  })
  accessToken: string;

  @ApiProperty({
    description: 'The expiry time of the new access token',
    example: 'expiryTime',
  })
  expiry: Date;

  static transform(
    accessToken: string,
    expiry: Date,
  ): RefreshAccessTokenResDTO {
    const transformedObj = new RefreshAccessTokenResDTO();
    transformedObj.msg = 'Access token refreshed successfully';
    transformedObj.accessToken = accessToken;
    transformedObj.expiry = expiry;

    return transformedObj;
  }
}
