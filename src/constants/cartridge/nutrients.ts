import { NutrientType } from "models/cartridge/nutrient.schema";

const NutrientsData = [
  { name: "<PERSON><PERSON><PERSON>", type: NutrientType.MACRO },
  { name: "Triphala extract", type: NutrientType.MACRO},
  { name: "Licorice", type: NutrientType.MACRO },
  { name: "Piperine extract (10%)", type: NutrientType.MACRO },
  { name: "Probiotic blend (billion)", type: NutrientType.MACRO },
  { name: "Senna leaves", type: NutrientType.MACRO },

  { name: "Elderberry Extract", type: NutrientType.MICRO },
  { name: "Echinacea purpurea Extract", type: NutrientType.MICRO },
  { name: "Curcuma longa / Curcuvail", type: NutrientType.MICRO },
  { name: "Vitamin C", type: NutrientType.MICRO },
  { name: "Zin<PERSON>", type: NutrientType.MICRO },
  { name: "Piper Nigrum Extract", type: NutrientType.MICRO},

  { name: "<PERSON><PERSON><PERSON>", type: NutrientType.OTHER },
  { name: "Chamo<PERSON>", type: NutrientType.OTHER },
  { name: "Valerian Root Extract", type: NutrientType.OTHER},
  { name: "L-Theanine", type: NutrientType.OTHER },
  { name: "Tryptophan", type: NutrientType.OTHER},
  { name: "L Threonine", type: NutrientType.OTHER },

  { name: "Taurine", type: NutrientType.MACRO },
  { name: "Ginseng root extract", type: NutrientType.MICRO },
  { name: "L-Carnitine", type: NutrientType.MACRO },
  { name: "B complex", type: NutrientType.MICRO},
  { name: "Vitamin B1", type: NutrientType.MICRO},
  { name: "Vitamin B2", type: NutrientType.MICRO},
  { name: "Vitamin B3", type: NutrientType.MICRO},
  { name: "Vitamin B5", type: NutrientType.MICRO,},
  { name: "Vitamin B6", type: NutrientType.MICRO},
  { name: "Vitamin B12", type: NutrientType.MICRO},

  { name: "Sodium", type: NutrientType.MACRO },
  { name: "Potassium", type: NutrientType.MACRO },
  { name: "Magnesium", type: NutrientType.MACRO},
  { name: "Calcium", type: NutrientType.MACRO },
  { name: "Chloride", type: NutrientType.MACRO },
  { name: "Vitamin D2", type: NutrientType.MICRO }, // IU, keep units in mind

  { name: "Spinach", type: NutrientType.MICRO },
  { name: "Arjuna", type: NutrientType.MICRO },
  { name: "Spirulina", type: NutrientType.MICRO },
  { name: "Fenugreek", type: NutrientType.MICRO },
  { name: "Aloe vera", type: NutrientType.MICRO },
  { name: "Barley Grass", type: NutrientType.MICRO },
  { name: "Moringa", type: NutrientType.MICRO },
  { name: "Alfa Alfa", type: NutrientType.MICRO },
  { name: "Blueberry", type: NutrientType.MICRO },
  { name: "Blackberry", type: NutrientType.MICRO },

  { name: "Vitamin A", type: NutrientType.MICRO }, // mcg
  { name: "Iron", type: NutrientType.MICRO },
  { name: "Manganese", type: NutrientType.MICRO},
  { name: "Selenium", type: NutrientType.MICRO},
  { name: "Iodine", type: NutrientType.MICRO },

  { name: "Bilberry extract", type: NutrientType.MICRO },
  { name: "Lutein - Lutemax 2020", type: NutrientType.MICRO },
  { name: "Astaxanthin", type: NutrientType.MICRO},
  { name: "DHA", type: NutrientType.MICRO },
];

export default NutrientsData;
