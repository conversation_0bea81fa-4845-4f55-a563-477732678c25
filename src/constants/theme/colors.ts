export interface Theme {
    background: {
        primary: string,
        secondary: string,
        tertiary: string,
        quaternary: string,
        error: string,
        trasparent:string,
    },
    text: {
        primary: string,
        secondary: string,
        tertiary: string,
        quaternary: string,
        quinary: string,
        senary: string,
        septenary: string,
        error: string,
    },
    button: {
        primary: string,
        secondary: string,
        tertiary: string,
        quaternary: string,
        quinary: string,
        trasparent:string,
    },
    border: {
        primary: string,
        secondary: string,
        tertiary: string,
        quaternary: string,
        quinary: string,
        senary: string,
        error: string,
        trasparent:string,
    },
    toast: {
        success: string,
        error: string,
        warning: string,
        info: string,
    },
    icon: {
        primary: string,
        secondary: string,
        tertiary: string,
        quaternary: string,
        quinary: string,
    },
    switch: {
        primary: string,
        secondary: string,
        tertiary: string,
    },
    graph: {
        success: string,
        error: string,
        warning: string,
    }
}

export const lightTheme:Theme = {
    background: {
        primary: '#FFFFFF',
        secondary: '#EAEAEA',
        tertiary: '#797979',
        quaternary: '#C5FBC7',
        error: '#e0bdbdff',
        trasparent: 'transparent',
    },
    text: {
        primary: '#000000',
        secondary: '#FFFFFF',
        tertiary: '#797979',
        quaternary: '#595959',
        quinary: '#868686',
        senary: '#05AFF2',
        septenary: '#A3A3A3',
        error: '#BE0000',
    },
    button: {
        primary: '#05AFF2',
        secondary: '#FFFFFF',
        tertiary: '#595959',
        quaternary: '#B6B6B6',
        quinary: '#EAEAEA',
        trasparent: 'transparent',
    },
    border: {
        primary: '#B6B6B6',
        secondary: '#EAEAEA',
        tertiary: '#484848',
        quaternary: '#B6B6B6',
        quinary: '#FFFFFF',
        senary: '#C0C0C0',
        error: '#BE0000',
        trasparent: 'transparent',
    },
    toast: {
        success: '#4CAF50',
        error: '#BE0000',
        warning: '#FFC107',
        info: '#2196F3',
    },
    icon: {
        primary: '#05AFF2',
        secondary: '#333333',
        tertiary: '#FFFFFF',
        quaternary: '#A8A8A8',
        quinary: '#E0E0E0',
    },
    switch: {
        primary: '#12705F',
        secondary: '#FFFFFF',
        tertiary: '#707070',
    },
    graph:{
        success: '#9EC248',
        error: '#F16522',
        warning: '#C2C2C2',
    }
}

export const darkTheme: Theme = {
  background: {
    primary: '#1E293B',   // slate-800
    secondary: '#334155', // slate-700
    tertiary: '#94A3B8',  // slate-400
    quaternary: '#475569',// slate-600
    error: '#7F1D1D',     // red-900
    trasparent: 'transparent',
  },
  text: {
    primary: '#F8FAFC',   // slate-50
    secondary: '#CBD5E1', // slate-300
    tertiary: '#94A3B8',  // slate-400
    quaternary: '#E2E8F0',// slate-200
    quinary: '#64748B',   // slate-500
    senary: '#38BDF8',    // sky-400 accent
    septenary: '#475569', // slate-600
    error: '#F87171',     // red-400
  },
  button: {
    primary: '#38BDF8',   // sky-400
    secondary: '#1E293B', // slate-700
    tertiary: '#E2E8F0',  // slate-200
    quaternary: '#64748B',// slate-500
    quinary: '#475569',   // slate-600
    trasparent: 'transparent',
  },
  border: {
    primary: '#475569',   // slate-600
    secondary: '#334155', // slate-700
    tertiary: '#94A3B8',  // slate-400
    quaternary: '#64748B',// slate-500
    quinary: '#1E293B',   // slate-800
        senary: '#C0C0C0',
    error: '#F87171',     // red-400
    trasparent: 'transparent',
  },
  toast: {
    success: '#22C55E',   // green-500
    error: '#EF4444',     // red-500
    warning: '#FACC15',   // yellow-400
    info: '#3B82F6',      // blue-500
  },
  icon: {
    primary: '#38BDF8',   // sky-400
    secondary: '#CBD5E1', // slate-300
    tertiary: '#1E293B',  // slate-800
    quaternary: '#94A3B8',// slate-400
    quinary: '#E0E0E0',
  },
  switch: {
    primary: '#38BDF8',   // sky-400
    secondary: '#1E293B', // slate-800
    tertiary: '#64748B',  // slate-500
  },
    graph:{
        success: '#000000',
        error: '#FFFFFF',
        warning: '#FFFFFF',
    }
};

