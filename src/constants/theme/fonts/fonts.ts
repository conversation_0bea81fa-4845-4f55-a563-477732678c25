import { useFonts } from "expo-font";


const useAppFonts = () => {
  const [fontsLoaded,fontError] = useFonts({
    "Helixa-Black": require("../../../../assets/fonts/helixa/Helixa-Black.ttf"),
    "Helixa-BlackItalic": require("../../../../assets/fonts/helixa/Helixa-BlackItalic.ttf"),
    "Helixa-Bold": require("../../../../assets/fonts/helixa/Helixa-Bold.ttf"),
    "Helixa-BoldItalic": require("../../../../assets/fonts/helixa/Helixa-BoldItalic.ttf"),
    "Helixa-Book": require("../../../../assets/fonts/helixa/Helixa-Book.ttf"),
    "Helixa-BookItalic": require("../../../../assets/fonts/helixa/Helixa-BookItalic.ttf"),
    "Helixa-Italic": require("../../../../assets/fonts/helixa/Helixa-Italic.ttf"),
    "Helixa-Light": require("../../../../assets/fonts/helixa/Helixa-Light.ttf"),
    "Helixa-LightItalic": require("../../../../assets/fonts/helixa/Helixa-LightItalic.ttf"),
    "Helixa-Regular": require("../../../../assets/fonts/helixa/Helixa-Regular.ttf"),
    "Helixa-Thin": require("../../../../assets/fonts/helixa/Helixa-Thin.ttf"),
    "Helixa-ThinItalic": require("../../../../assets/fonts/helixa/Helixa-ThinItalic.ttf"),
    "Chemian-Italic": require("../../../../assets/fonts/chemina/Chemian-Italic.ttf"),
    "Chemian-Regular": require("../../../../assets/fonts/chemina/Chemian-Regular.ttf"),
  });

  return {
    fontsLoaded,
    fontError
  };
};

export const AppFonts = {
    "HelixaBlackItalic": "Helixa-BlackItalic",
    "HelixaBlack": "Helixa-Black",
    "HelixaBold": "Helixa-Bold",
    "HelixaBoldItalic": "Helixa-BoldItalic",
    "HelixaBook": "Helixa-Book",
    "HelixaBookItalic": "Helixa-BookItalic",
    "HelixaItalic": "Helixa-Italic",
    "HelixaLight": "Helixa-Light",
    "HelixaLightItalic": "Helixa-LightItalic",
    "HelixaRegular": "Helixa-Regular",
    "HelixaThin": "Helixa-Thin",
    "HelixaThinItalic": "Helixa-ThinItalic",
    "ChemianItalic": "Chemian-Italic",
    "ChemianRegular": "Chemian-Regular",
}

export default useAppFonts;