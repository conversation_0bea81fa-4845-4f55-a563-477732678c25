import { create } from 'zustand';

type AppStateStoreType = {
    appReady:boolean,
    setAppReady:(appReady:boolean)=>void,
    splashDone:boolean,
    setSplashDone:(splashDone:boolean)=>void,
    showMenu:boolean,
    setShowMenu:(showMenu:boolean)=>void,
}

const useAppStateStore = create<AppStateStoreType>((set)=>({
    appReady:false,
    splashDone:false,
    setAppReady:(appReady:boolean)=>{
        set({appReady});
    },
    setSplashDone:(splashDone:boolean)=>{
        set({splashDone});
    },

    showMenu:false,
    setShowMenu:(showMenu:boolean)=>{
        set({showMenu});
    },
}))

export default useAppStateStore;