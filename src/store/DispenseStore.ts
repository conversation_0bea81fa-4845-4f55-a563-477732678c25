
import { create } from 'zustand';
import dispenseService from '../apis/dispense/dispenseService';

type DispenseStoreType = {
    tabletsTakenToday: number,
    hasTakenToday: boolean,

    isFetchingTodayDoseStatus: boolean,
    getTodayDoseStatus: () => Promise<void>,
}

const useDispenseStore = create<DispenseStoreType>((set,get)=>({
    tabletsTakenToday: 0,
    hasTakenToday: false,
    isFetchingTodayDoseStatus: false,
    getTodayDoseStatus: async () => {
        set({isFetchingTodayDoseStatus: true});
        const response = await dispenseService.getTodayDoseStatus();
        if(response.success){
            set({tabletsTakenToday: response.data.tabletsTakenToday, hasTakenToday: response.data.hasTakenToday});
        }
        set({isFetchingTodayDoseStatus: false});
    }
}))

export default useDispenseStore;