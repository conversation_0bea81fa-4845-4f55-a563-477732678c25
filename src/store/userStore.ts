import { create } from 'zustand';

import { getItem, removeItem, setItem } from '@/utils/storage';

import { Permission, UserInfo, UserToken } from '#/entity';
import { StorageEnum } from '#/enum';

type UserStore = {
  userInfo: Partial<UserInfo>;
  userToken: UserToken;
  permissions: Permission[];
  roles: string;

  // Use the actions namespace to store all actions
  actions: {
    setRole: (roles: string) => void;
    setUserInfo: (userInfo: UserInfo) => void;
    setUserToken: (token: UserToken) => void;
    setPermissions: (permissions: Permission[]) => void;
    clearUserInfoAndToken: () => void;
  };
};

const useUserStore = create<UserStore>((set) => ({
  userInfo: getItem<UserInfo>(StorageEnum.User) || {},
  userToken: getItem<UserToken>(StorageEnum.Token) || {},
  permissions: getItem<Permission[]>(StorageEnum.Permissions) || [],
  roles: getItem<string>(StorageEnum.Roles) || '',

  actions: {
    setUserInfo: (userInfo) => {
      set({ userInfo });
      setItem(StorageEnum.User, userInfo);
    },
    setUserToken: (userToken) => {
      set({ userToken });
      setItem(StorageEnum.Token, userToken);
    },
    setPermissions: (permissions) => {
      set({ permissions });
      setItem(StorageEnum.Permissions, permissions);
    },
    setRole: (roles) => {
      set({ roles });
      setItem(StorageEnum.Roles, roles);
    },
    clearUserInfoAndToken() {
      set({ userInfo: {}, userToken: {} });
      removeItem(StorageEnum.User);
      removeItem(StorageEnum.Token);
      removeItem(StorageEnum.Permissions);
      removeItem(StorageEnum.Roles);
      removeItem(StorageEnum.OTPAccessToken);
    },
  },
}));

export const useUserInfo = () => useUserStore((state) => state.userInfo);
export const useUserToken = () => useUserStore((state) => state.userToken);
export const useUserPermission = () => useUserStore((state) => state.permissions);
export const useUserRoles = () => useUserStore((state) => state.roles);
export const useUserActions = () => useUserStore((state) => state.actions);

export default useUserStore;
