
import { create } from 'zustand';
import { createJSONStorage, persist, StateStorage } from 'zustand/middleware';
import * as SecureStore from 'expo-secure-store';
import { userSchemaType } from '../screens/Auth/schemas/loginResponseSchema';
import authService from '../apis/auth/authService';
import apiClient, { createErrorResponse, ErrorResponse } from '../apis/axios/axiosInstance';
import DeviceInfo from 'react-native-device-info';
import { promise } from 'zod';
import { CompleteUserDetailsReqType } from '../screens/Onboarding/schemas/CompleteUserDetailsReqSchema';
import Toast from 'react-native-toast-message';
import { USER_STORAGE_KEY } from './constants/store_keys';
import { UpdateUserDetailsReqType } from '../apis/user/schemas/UpdateUserDetailsReqSchema';
import userService from '../apis/user/userService';
import { UserDetailsResType } from '../apis/user/schemas/UserDetailsSchema';

type UserStoreType = {
    isLoggingUser: boolean,
    isAuthenticated: boolean,
    isAccountCompleted: boolean,
    accessToken?: string | null,
    refreshToken?: string | null,
    user?: userSchemaType | null,
    userTokenExpiry?: Date | null,
    userError: ErrorResponse | null,

    isLoggingOut: boolean,
    isCompletingOnboarding: boolean,

    isUpdatingUserProfile: boolean,

    signIn:(email:string, password:string)=>Promise<void>,
    signOut:()=>Promise<void>,
    completeOnboarding:(userData:CompleteUserDetailsReqType)=>Promise<boolean>,
    updateUserProfile:(userData:UpdateUserDetailsReqType)=>Promise<boolean>,

    isLoadingUserProfile: boolean,
    getUserProfile:()=>Promise<void>,
    
    clearUserError:()=>void,
    clearUserData:()=>void,
}

export const secureStoreStorage: StateStorage = {
  getItem: async (name: string) => {
    const value = await SecureStore.getItemAsync(name);
    return value ?? null;
  },
  setItem: async (name: string, value: string) => {
    await SecureStore.setItemAsync(name, value);
  },
  removeItem: async (name: string) => {
    await SecureStore.deleteItemAsync(name);
  },
};

const initialState = {
    isLoggingUser: false,
    isAuthenticated: false,
    isAccountCompleted: false,
    accessToken: null,
    refreshToken: null,
    user: null,
    userTokenExpiry: null,
    userError: null,
    isLoggingOut: false,
    isCompletingOnboarding: false,
    isUpdatingUserProfile: false,
}      


const useUserStore = create<UserStoreType>()(
    persist(
        (set) => ({
            ...initialState,
            signIn:async(email,password)=>{
                set({isLoggingUser:true});
                const response = await authService.login({email,password});
                
                if(response.success){
                    set({
                        isLoggingUser:false,
                        isAuthenticated: true,
                        isAccountCompleted: response.data.user.isAccountCompleted,
                        accessToken: response.data.accessToken,
                        refreshToken: response.data.refreshToken,
                        user: response.data.user,
                        userTokenExpiry: response.data.expiry,
                    });
                }
                else {
                    set({isLoggingUser:false, userError:response.error});
                }
            },

            signOut: async()=>{
                set({isLoggingOut:true});
                try{
                    const deviceId = await DeviceInfo.getUniqueId();

                    await authService.logout({ deviceId });

                    delete apiClient.defaults.headers.common['Authorization'];

                    set({
                        isLoggingOut:false,
                        isAuthenticated: false,
                        isAccountCompleted: false,
                        accessToken: null,
                        refreshToken: null,
                        user: null,
                        userTokenExpiry: null,
                    });
                }
                catch(error){
                    set({isLoggingOut:false, userError:createErrorResponse(error)});
                }
            },

            completeOnboarding:async(userData:CompleteUserDetailsReqType)=>{
                set({isCompletingOnboarding:true});
                const response = await authService.completeUserProfile(userData);

                if(response.success){
                    set(state=>({
                        ...state,
                        isCompletingOnboarding:false,
                        isAccountCompleted: true,
                        user: response.data.user,
                    }));

                    return true;
                }
                Toast.show({
                    type: 'error',
                    text1: 'Error',
                    text2: response.error.message,
                    position: 'bottom'
                });
                set({isCompletingOnboarding:false});
                return false;
            },

            updateUserProfile:async(userData:UpdateUserDetailsReqType)=>{
                set({isUpdatingUserProfile:true});
                const response = await userService.updateUserProfile(userData);
                
                if(response.success){
                    set(state=>({
                        ...state,
                        isUpdatingUserProfile:false,
                        user: response.data,
                    }));

                    return true;
                }
                Toast.show({
                    type: 'error',
                    text1: 'Error',
                    text2: response.error.message,
                    position: 'bottom'
                });
                set({isUpdatingUserProfile:false});
                return false;
            },

            isLoadingUserProfile: false,

            getUserProfile: async()=>{
                set({isLoadingUserProfile:true});
                const response = await userService.getUserProfile();
                if(response.success){
                    set(state=>({
                        ...state,
                        user: response.data,
                        isLoadingUserProfile:false,
                    }));
                }
                else {
                    Toast.show({
                        type: 'error',
                        text1: 'Error',
                        text2: response.error.message,
                        position: 'bottom'
                    });
                }
                set({isLoadingUserProfile:false});
            },

            clearUserError:()=>{
                set({userError:null});
            },

            clearUserData:()=>{
                delete apiClient.defaults.headers.common['Authorization'];        
                set(initialState);
            },

        }),
        {
            name: USER_STORAGE_KEY,
            version: 1,
            storage: createJSONStorage(() => secureStoreStorage),
        }
    )
)

export default useUserStore;