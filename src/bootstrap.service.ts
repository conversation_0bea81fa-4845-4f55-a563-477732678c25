import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { User } from 'models/user';
import { Model } from 'mongoose';
import { CustomLogger, EncryptionService } from './common/services';
import { ErrorResponse } from './utils/responses';
import { ROLE_VALUES } from 'models/user/user.schema';
import * as argon2 from 'argon2';

interface AdminData {
  EMAIL: string;
  FIRST_NAME: string;
  LAST_NAME: string;
  PASSWORD: string;
  TIMEZONE: string;
}

@Injectable()
export class BootstrapService {
  constructor(
    private readonly logger: CustomLogger,
    private readonly encryptionService: EncryptionService,

    @InjectModel(User.name)
    private readonly userModel: Model<User>,
  ) {}

  private getAdminData(): AdminData {
    return {
      EMAIL: String(process.env.APP_ADMIN_MAIL || '').toLowerCase(),
      FIRST_NAME: process.env.APP_ADMIN_FIRST_NAME || '',
      LAST_NAME: process.env.APP_ADMIN_LAST_NAME || '',
      PASSWORD: process.env.APP_ADMIN_PASSWORD || '',
      TIMEZONE: process.env.APP_ADMIN_TIMEZONE || 'UTC',
    };
  }

  async createAdmin() {
    // await this.resetAppPermissions();

    const { EMAIL, FIRST_NAME, LAST_NAME, PASSWORD, TIMEZONE } = this.getAdminData();

    if (!EMAIL || !FIRST_NAME || !PASSWORD) {
      const CustomError: ErrorResponse = {
        error: true,
        statusCode: 400,
        message: 'Admin credentials are not fully provided...',
        path: 'bootstrap-service-createAdmin',
        errorId: 1,
        timestamp: new Date(),
      };

      this.logger.error(CustomError);
      return;
    }

    try {
      const encryptedEmail = this.encryptionService.encrypt(EMAIL);
      const user = await this.userModel.findOne({ email: encryptedEmail });

      if (user == null) {
        await this.userModel.create({
          email: EMAIL,
          firstName: FIRST_NAME,
          lastName: LAST_NAME,
          isEmailVerified: true,
          isAccountCompleted: false,
          password: PASSWORD,
          role: ROLE_VALUES.ADMIN,
          acceptTerms: true,
          timeZone: TIMEZONE,
        });
      } else {
        const encryptedPassword = await argon2.hash(PASSWORD);
        await this.userModel.findOneAndUpdate(
          { email: encryptedEmail },
          {
            password: encryptedPassword,
            role: ROLE_VALUES.ADMIN,
          },
        );
      }
    } catch (error) {
      console.error('Error saving admin user:', error);
      throw error;
    }
  }
}
