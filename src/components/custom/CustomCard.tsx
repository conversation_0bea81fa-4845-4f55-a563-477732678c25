import React, { ReactNode } from 'react';

import { useSettings } from '@/store/settingStore';

import { ThemeMode } from '#/enum';

interface CustomCardProps {
  children: ReactNode;
  className?: string;
}

const CustomCard: React.FC<CustomCardProps> = ({ children, className = '' }) => {
  const { themeMode } = useSettings();

  return (
    <div
      className={`rounded-xl border-2 ${themeMode === ThemeMode.Dark ? 'border-gray-800 bg-[#212b36]' : 'border-gray-300 bg-white'} p-8 shadow-sm ${className}`}
    >
      {children}
    </div>
  );
};

export default CustomCard;
