import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useRef, useState, useEffect } from 'react'
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { Entypo } from '@expo/vector-icons';

interface HorizontalSelectorProps {
    options:{
        label:string,
        value:string
    }[],
    selectedOption:string,
    setSelectedOption:(value:string) => void
}

const HorizontalSelector = ({options,selectedOption,setSelectedOption}:HorizontalSelectorProps) => {
    const { theme } = useTheme();
    const flatListRef = useRef<FlatList>(null);
    const [canScrollLeft, setCanScrollLeft] = useState(false);
    const [canScrollRight, setCanScrollRight] = useState(false);

    const currentIndex = options.findIndex(option => option.value === selectedOption);

    useEffect(() => {
        // Check if we can scroll left or right based on current selection
        setCanScrollLeft(currentIndex > 0);
        setCanScrollRight(currentIndex < options.length - 1);
    }, [currentIndex, options.length]);

    const scrollToPrevious = () => {
        if (currentIndex > 0) {
            const prevOption = options[currentIndex - 1];
            setSelectedOption(prevOption.value);
            flatListRef.current?.scrollToIndex({
                index: currentIndex - 1,
                animated: true,
                viewPosition: 0.5
            });
        }
    };

    const scrollToNext = () => {
        if (currentIndex < options.length - 1) {
            const nextOption = options[currentIndex + 1];
            setSelectedOption(nextOption.value);
            flatListRef.current?.scrollToIndex({
                index: currentIndex + 1,
                animated: true,
                viewPosition: 0.5
            });
        }
    };

    return (
        <View style={styles.container}>
            <View style={styles.selectorWrapper}>
                {canScrollLeft && (
                    <TouchableOpacity
                        style={[styles.arrowButton, styles.leftArrow]}
                        onPress={scrollToPrevious}
                    >
                        <Entypo name="chevron-left" size={24} color={theme.text.quinary} style={styles.arrowText} />
                    </TouchableOpacity>
                )}

                <FlatList
                    ref={flatListRef}
                    data={options}
                    keyExtractor={(item) => item.value}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.listContainer}
                    renderItem={({ item,index }) => (
                        <TouchableOpacity
                            key={item.value}
                            style={[styles.button,{
                                marginHorizontal:index === 0 ? 0 : 12,
                                marginRight:index===options.length-1 ? 0 : 12,
                            }]}
                            onPress={() => setSelectedOption(item.value)}
                        >
                            <Text
                                style={[
                                    styles.buttonText, {
                                        color: selectedOption === item.value ? theme.text.primary : theme.text.septenary,
                                        fontWeight: selectedOption === item.value ? '600' : '400'
                                    }
                                ]}
                            >
                                {item.label}
                            </Text>
                            {selectedOption === item.value && (
                                <View style={[styles.underline, { backgroundColor: theme.text.primary }]} />
                            )}
                        </TouchableOpacity>
                    )}
                />

                {canScrollRight && (
                    <TouchableOpacity
                        style={[styles.arrowButton, styles.rightArrow]}
                        onPress={scrollToNext}
                    >
                        <Entypo name="chevron-right" size={24} color={theme.text.quinary} style={styles.arrowText} />
                    </TouchableOpacity>
                )}
            </View>
        </View>
    );
}

export default HorizontalSelector

const styles = StyleSheet.create({
    container: {
        paddingVertical: 8,
    },
    selectorWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
    },
    listContainer: {
        alignItems: 'center',
    },
    button: {
        // backgroundColor:'red',
        paddingBottom:2,
        alignItems: "center",
        justifyContent: "center",
        position: 'relative',
    },
    buttonText: {
        fontSize: 12,
        fontFamily: AppFonts.HelixaBoldItalic,
        textAlign: 'center',
    },
    underline: {
        position: 'absolute',
        bottom: 0,
        left: 1,
        right: 1,
        height: 2,
        borderRadius: 1,
    },
    arrowButton: {
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        zIndex: 1,
    },
    leftArrow: {
        left: 0,
        top:"50%",
        transform: [{ translateX: -16 },{ translateY: '-50%' }],
    },
    rightArrow: {
        right: 0,
        top:"50%",
        transform: [{ translateX: 16 },{ translateY: '-50%' }],
    },
    arrowText: {
        fontWeight: 'bold',
    },
})