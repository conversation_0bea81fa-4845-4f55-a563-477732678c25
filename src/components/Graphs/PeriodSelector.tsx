import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { useTheme } from '../../context/ThemeContext';

const  periods:('Week' | 'Month' | '6 Month')[] = ['Week', 'Month', '6 Month'];

interface PeriodSelectorProps {
    selectedPeriod: 'Week' | 'Month' | '6 Month';
    setSelectedPeriod: (period: 'Week' | 'Month' | '6 Month') => void;
}

const PeriodSelector = ({
    selectedPeriod,
    setSelectedPeriod,
}:PeriodSelectorProps) => {
    const { theme } = useTheme();
    return (
        <View style={[styles.container,{borderColor:theme.border.primary,backgroundColor:theme.background.primary}]}>
        {periods.map((period) => (
            <TouchableOpacity
            key={period}
            style={[
                styles.button,
                selectedPeriod === period && {
                    backgroundColor:theme.graph.success,
                }
            ]}
            onPress={() => setSelectedPeriod(period)}
            >
            <Text
                style={[
                styles.buttonText, {
                    color:selectedPeriod === period ? theme.text.secondary : theme.text.quinary
                }
                ]}
            >
                {period.split(" ").map((word) => word[0]).join("")}
            </Text>
            </TouchableOpacity>
        ))}
        </View>
    );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderRadius: 100,
    padding: 1,
    alignSelf: 'center',
    borderWidth:1.5,
    overflow:"hidden",
    elevation:1
  },
  button: {
    width:36,
    alignItems:"center",
    borderRadius: 100,
    paddingVertical:2
  },
  buttonText: {
    fontSize: 11,
    fontFamily:AppFonts.HelixaBold,
    color: '#555',
  },
  selectedButtonText: {
    color: '#fff', // selected text color
  },
});

export default PeriodSelector;
