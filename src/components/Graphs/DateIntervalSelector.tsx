import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { Entypo } from '@expo/vector-icons';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { useTheme } from '../../context/ThemeContext';

interface DateIntervalSelectorProps {
    startDate: Date;
    setStartDate: (date: Date) => void;
    selectedPeriod: 'Week' | 'Month' | '6 Month';
}

const DateIntervalSelector = ({
    startDate,
    setStartDate,
    selectedPeriod,
}:DateIntervalSelectorProps) => {
    const {theme} = useTheme();

    const formatDate = (date: Date) => {
        const day = date.getDate();
        const month = date.toLocaleString('en', { month: 'short' }).toLowerCase();
        return `${day}${getDaySuffix(day)} ${month}`;
    };

    const getDaySuffix = (day: number) => {
        if (day > 3 && day < 21) return 'th';
        switch (day % 10) {
            case 1: return 'st';
            case 2: return 'nd';
            case 3: return 'rd';
            default: return 'th';
        }
    };

    const getDateRange = () => {
        const start = new Date(startDate);
        const end = new Date(startDate);
        const today = new Date();

        switch (selectedPeriod) {
            case 'Week':
                end.setDate(end.getDate() + 6);
                break;
            case 'Month':
                end.setDate(end.getDate() + 29);
                break;
            case '6 Month':
                end.setMonth(end.getMonth() + 6);
                end.setDate(end.getDate() - 1);
                break;
        }

        // If end date is in the future, cap it at today
        if (end > today) {
            return `${formatDate(start)} - ${formatDate(today)}`;
        }

        return `${formatDate(start)} - ${formatDate(end)}`;
    };

    const navigateDate = (direction: 'next' | 'prev') => {
        const newDate = new Date(startDate);

        switch (selectedPeriod) {
            case 'Week':
                newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
                break;
            case 'Month':
                newDate.setDate(newDate.getDate() + (direction === 'next' ? 30 : -30));
                break;
            case '6 Month':
                newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 6 : -6));
                break;
        }

        // Don't allow navigation beyond today
        if (direction === 'next') {
            const today = new Date();
            const endDate = new Date(newDate);

            switch (selectedPeriod) {
                case 'Week':
                    endDate.setDate(endDate.getDate() + 6);
                    break;
                case 'Month':
                    endDate.setDate(endDate.getDate() + 29);
                    break;
                case '6 Month':
                    endDate.setMonth(endDate.getMonth() + 6);
                    break;
            }

            if (endDate > today) {
                return;
            }
        }

        setStartDate(newDate);
    };

    const isNextDisabled = () => {
        const futureStart = new Date(startDate);
        const today = new Date();

        switch (selectedPeriod) {
            case 'Week':
                futureStart.setDate(futureStart.getDate() + 7);
                break;
            case 'Month':
                futureStart.setDate(futureStart.getDate() + 30);
                break;
            case '6 Month':
                futureStart.setMonth(futureStart.getMonth() + 6);
                break;
        }

        const futureEnd = new Date(futureStart);
        switch (selectedPeriod) {
            case 'Week':
                futureEnd.setDate(futureEnd.getDate() + 6);
                break;
            case 'Month':
                futureEnd.setDate(futureEnd.getDate() + 29);
                break;
            case '6 Month':
                futureEnd.setMonth(futureEnd.getMonth() + 6);
                break;
        }

        return futureEnd > today;
    };

    return (
        <View style={styles.currTimeIntervalWrapper}>
            <TouchableOpacity 
                onPress={() => navigateDate('prev')}
            >
                <Entypo name="chevron-left" size={16} color={theme.text.quinary} />
            </TouchableOpacity>
            <Text style={styles.currTimeText}>{getDateRange()}</Text>
            <TouchableOpacity 
                onPress={() => navigateDate('next')}
                disabled={isNextDisabled()}
                style={{opacity:isNextDisabled() ? .5 : 1}}
            >
                <Entypo name="chevron-right" size={16} color={theme.text.quinary} />
            </TouchableOpacity>
        </View>
    )
}

export default DateIntervalSelector

const styles = StyleSheet.create({
    currTimeIntervalWrapper:{
        flex:1,
        flexDirection:"row",
        alignItems:"center",
        justifyContent:"flex-end",
        // gap:2
    },
    currTimeText:{
        fontSize: 10,
        fontFamily: AppFonts.HelixaRegular,
    },
})