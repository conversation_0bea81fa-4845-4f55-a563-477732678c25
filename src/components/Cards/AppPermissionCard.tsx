import React, { memo } from 'react';
import { View, Text, Image, StyleSheet, Platform, TouchableOpacity, ViewStyle } from 'react-native';
// import useHealthPermissionStore from 'store/healthPermissionStore';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import SwitchBtn from '../Buttons/SwitchBtn';
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { ThemedStyleProp } from '../../types/ThemeStyleType';

interface AppPermissionCardProps {
    icon: any;
    onToggle: () => void;
    checked: boolean;
    iconWrapperStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">
}

export const AppPermissionCard = memo(({
    icon,
    onToggle,
    checked,
    iconWrapperStyle
}: AppPermissionCardProps) => {
    const { theme } = useTheme();
    const isLoadingHealthPermissions = false;
    // const { isLoadingHealthPermissions } = useHealthPermissionStore(state => state);

    // Get platform-specific description text
    const descriptionText = Platform.OS === 'ios'
        ? "Nouriq would like to access and update your health data."
        : "Nouriq would like to access and update your health data.";

    return (
        <View style={styles.rowContainer}>
            <View style={[styles.imageContainer,{
                borderColor:theme.icon.secondary
            },iconWrapperStyle]}>
                <Image
                    source={icon}
                    style={[
                        styles.healthIcon,
                        { opacity: 2 }
                    ]}
                />
            </View>
            <View style={styles.textContainer}>
                <Text style={[styles.sectionHeading,{
                    color:theme.text.primary
                }]}>Workout & Health data</Text>
                <Text style={[styles.sectionDescription,
                {
                    color:theme.text.primary
                }]}>
                    {descriptionText}
                </Text>
            </View>

            <View style={styles.toggleContainer}>
                {
                    Platform.OS === 'ios' ?
                        <TouchableOpacity activeOpacity={0.8}
                            onPress={onToggle}>
                            <MaterialIcons
                                name="manage-accounts"
                                size={32}
                                color={theme.icon.primary}
                            />
                        </TouchableOpacity>
                        :
                        <SwitchBtn
                            checked={checked}
                            onToggle={onToggle}
                            disabled={isLoadingHealthPermissions}
                        />
                }
            </View>
        </View>
    );
});


const styles = StyleSheet.create({
    rowContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        marginBottom: 10,
    },
    imageContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        width: 36,
        aspectRatio: 1,
        borderWidth: 1,
        borderRadius: 16,
        padding: 1,
    },
    healthIcon: {
        width: '100%',
        height: '100%',
        resizeMode: 'contain',
        padding: 5
    },
    textContainer: {
        flex: 3,
        marginLeft: 10,
        gap:4
    },
    sectionHeading: {
        fontSize: 12,
        fontFamily: AppFonts.HelixaBlack,
    },
    sectionDescription: {
        fontSize: 12,
        fontFamily: AppFonts.HelixaRegular,
    },
    toggleContainer: {
        flex: 1,
        alignItems: 'flex-end',
    },
});