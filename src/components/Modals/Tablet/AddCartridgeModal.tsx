import { StyleSheet, Text, View } from 'react-native'
import React, {  useEffect, useState } from 'react'
import CustomModal from '../CustomModal'
import { useTheme } from '../../../context/ThemeContext'
import CartridgeUI from '../../Icons/Cartridge/CartridgeUI'
import IconBtn from '../../Buttons/IconBtn'
import { AntDesign, Feather } from '@expo/vector-icons'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import SimpleBtn from '../../Buttons/SimpleBtn'
import cartridgeService from '../../../apis/cartridge/cartridgeService'
import Toast from 'react-native-toast-message'

type AddCartridgeModalProps = {
  cartridgeDetails: {id:string,name:string,numTablets:number,cartidgeColor:string,tabletColor:string} | null,
  onClose:()=>void,
  onSuccess:()=>void,
}

const AddCartridgeModal = ({onClose,cartridgeDetails,onSuccess}:AddCartridgeModalProps) => {
    const { theme } = useTheme();

    const [ numTablets, setNumTablets ] = useState(cartridgeDetails?.numTablets || 0);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        setNumTablets(cartridgeDetails?.numTablets || 0);
    }, [cartridgeDetails])

    const saveCartridgeStock = async () => {
        if(!cartridgeDetails) return;
        setLoading(true);
        const response = await cartridgeService.updateUserStock(cartridgeDetails.id,numTablets);
        if(response.success){
            onClose();
            onSuccess();
        }
        else {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: response.error.message,
                position: 'bottom'
            });
        }
        setLoading(false);
    }

    return (
        <CustomModal
            visible={cartridgeDetails!==null}
            onClose={onClose}
            title="Add Cartridge"
            subtitle={cartridgeDetails?.name || ""}
            isLoading={loading}
            icon={
                <View style={styles.cartridgeDetailsContainer}>
                    <CartridgeUI cartidgeColor={cartridgeDetails?.cartidgeColor || '#FFFFFF'} tabletColor={cartridgeDetails?.tabletColor || '#FFFFFF'} numTablets={numTablets}/>
                    <Text style={styles.cartidgeText}>Number of Cartridge</Text>
                    <View style={styles.cartridgeQuantityContainer}>
                        <IconBtn
                            icon={<Feather name="minus" size={20} color={theme.icon.secondary} />}
                            onPress={()=>{
                                if(numTablets>0){
                                    setNumTablets(numTablets-1);
                                }
                            }}

                            containerStyle={styles.minusIconBtn}
                        />
                        <Text style={styles.quantityText}>{numTablets}</Text>
                        <IconBtn
                            icon={<Feather name="plus" size={20} color={theme.icon.secondary} />}
                            onPress={()=>{
                                setNumTablets(numTablets+1)
                            }}
                            containerStyle={styles.minusIconBtn}
                        />
                    </View>
                    <SimpleBtn
                        title='Save Changes'
                        onPress={saveCartridgeStock}
                        containerBgColor='primary'
                        titleTextColor='secondary'
                        containerStyle={styles.saveChangesBtn}
                        disabled={loading}
                        disabledBgColor='primary'
                        disabledTitleColor='secondary'
                    />
                </View>
            }
        />
    )
}

export default AddCartridgeModal

const styles = StyleSheet.create({
    cartridgeDetailsContainer:{
        justifyContent:"space-between",
        alignItems:"center",
        gap:16
    },
    cartridgeQuantityContainer:{
        flexDirection:"row",
        gap:16,
        justifyContent:"center",
        alignItems:"center"
    },
    minusIconBtn:{
        position:'relative',
        right:'auto',
        left:"auto",
        padding:10,
        elevation:1
    },
    quantityText:{
        fontSize:24,
        fontFamily:AppFonts.HelixaBold,
    },
    cartidgeText:{
        fontSize:16,
        fontFamily:AppFonts.HelixaBlack,
    },
    saveChangesBtn:{
        width:"auto",
        paddingHorizontal:32
    }
})