import { StyleSheet, Text, View } from 'react-native'
import React, { useState } from 'react'
import CustomModal from '../CustomModal'

const ScheduleTabletModal = () => {
  const [ selectedTime , setSelectedTime ] = useState<Date | null>(null);
  return (
    <CustomModal
      visible={false}
      onClose={()=>{}}
      title="Schedule Tablet"
      subtitle=""
      desc="This feature is coming soon!"
    />
  )
}

export default ScheduleTabletModal

const styles = StyleSheet.create({})