import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import CustomModal from '../CustomModal'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import { useTheme } from '../../../context/ThemeContext'

type NutrientDetailsModalProps = {
    name:string | null,
    onClose:()=>void,
}

const healthBenefits = [
    'Heart Health',
    'Bone Health',
    'Immunity',
    'Cognitive Health',
    'Digestion',
    'Energy',
    'Mood',
    'Sleep',
    'Skin',
    'Hair',
    'Eyes',
    'Teeth',
    'Oral Health',
    'Menstrual Health',
    'Pregnancy',
    'Lactation',
    'Weight Management',
    // 'Metabolism',
]

const NutrientDetailsModal = ({name,onClose}:NutrientDetailsModalProps) => {
    const { theme } = useTheme();
    const chunkArray = (arr: any[], size: number) => {
        return arr.reduce((acc, _, i) => {
            if (i % size === 0) acc.push(arr.slice(i, i + size));
            return acc;
        }, [] as any[][]);
    };


    const groupedBenefits = chunkArray(healthBenefits, 3);


  return (
    <CustomModal
      visible={name !== null}
      onClose={onClose}
      title={`${name}`}
      subtitle="About"
      icon={
        <View>
            <Text style={styles.nutrientDetailsText}>
                Spinach is a low-calorie, nutrient-dense leafy green rich in vitamins (A, C, K, folate), minerals (iron, magnesium, potassium), and antioxidants like beta-carotene and lutein
            </Text>
            <View style={styles.nutrientBenefitsContainer}>
                {
                    groupedBenefits.map((item: string[], index: number)=> (
                        <View key={index} style={styles.nutrientBenefitsRow}>
                            {
                                item.map((benefit: string, index: number) => (
                                    <React.Fragment key={index}>
                                        <Text key={index} style={[styles.nutrientBenefitsText,{
                                            color:theme.text.quinary
                                        }]}>{benefit}</Text>
                                        {index !== item.length - 1 && <View style={{width:1,height:"100%",backgroundColor:theme.background.secondary}}/>}
                                    </React.Fragment>
                                ))
                            }
                        </View>
                    ))
                }
            </View>
        </View>
      }
    />
  )
}

export default NutrientDetailsModal

const styles = StyleSheet.create({
    nutrientDetailsText:{
        paddingHorizontal:32,
        fontSize:14,
        fontFamily:AppFonts.HelixaBook,
        textAlign:'center'
    },
    nutrientBenefitsContainer:{
        marginTop:20,
        marginBottom:8
    },
    nutrientBenefitsRow:{
        flexDirection:"row",
        justifyContent:"space-between",
        paddingBottom:8,
        alignItems:'center'
    },
    nutrientBenefitsText:{
        flex:1,
        fontSize:11,
        fontFamily:AppFonts.HelixaBold,
        textTransform:"uppercase",
        textAlign:'center',
        padding:8,
        paddingHorizontal:12
    }
})