import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import CustomModal from '../CustomModal'
import { Fontisto } from '@expo/vector-icons'
import { MaterialCommunityIcons } from '@expo/vector-icons'
import { useTheme } from '../../../context/ThemeContext'
import SimpleBtn from '../../Buttons/SimpleBtn'
import { AppFonts } from '../../../constants/theme/fonts/fonts'

type MailSentModalProps = {
    visible:boolean,
    onClose:()=>void,
    title:string,
    subtitle:string,
    desc:string,
}

const MailSentModal = ({
    visible,
    onClose,
    title,
    subtitle,
    desc,
}:MailSentModalProps) => {
    const { theme } = useTheme();
  return (
    <CustomModal
      visible={visible}
      onClose={onClose}
      title={title}
      subtitle={subtitle}
      desc={desc}
      showBackBtn={false}
      icon={
        <View style={styles.iconContainer}>
          <Fontisto name="email" size={120} color={theme.icon.primary}
            style={{
              alignSelf: "center"
            }}
          />
          <View style={styles.sendMessageContainer}>
            <Text style={[styles.sentText, { color: theme.text.primary }]}>
              Sent
            </Text>
            <MaterialCommunityIcons name="checkbox-marked-circle" size={18} color={theme.toast.success} />
          </View>
        </View>
      }
      buttons={
        <SimpleBtn
          title='Okay'
          onPress={onClose}
          containerStyle={{
            width:"auto",
            alignSelf:"center",
            paddingHorizontal:48
          }}
        />
      }
      innerContainerStyle={{width:"85%"}}
    />
  )
}

export default MailSentModal

const styles = StyleSheet.create({
    iconContainer:{
    },
    sendMessageContainer:{
        flexDirection:"row",
        justifyContent:"center",
        alignItems:"center",
        gap:4
    },
    sentText:{
        fontSize:16,
        fontFamily:AppFonts.HelixaBold,
        textAlign:"center"
    }
})