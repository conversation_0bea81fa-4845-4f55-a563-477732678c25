import { Modal, StyleSheet, Text, TouchableWithoutFeedback, View, ViewStyle } from 'react-native'
import React from 'react'
import { AppFonts } from '../../constants/theme/fonts/fonts'
import BackBtn from '../Buttons/BackBtn'
import { BlurView } from "@react-native-community/blur";
import { useTheme } from '../../context/ThemeContext';
import PageLoader from '../Loaders/PageLoader';
import { ThemedStyleProp } from '../../types/ThemeStyleType';
import Toast from 'react-native-toast-message';
import toastConfig from '../../config/CustomToastConfig';

type CustomModalProps = {
  isLoading?:boolean,
  visible:boolean,
  onClose:()=>void,
  onContentPress?:()=>void,
  title:string,
  subtitle:string,
  icon?:React.ReactNode,
  desc?:string,
  buttons?:React.ReactNode,
  showBackBtn?:boolean,
  innerContainerStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">,
}

const CustomModal = ({isLoading = false,visible,onClose,onContentPress,title,subtitle,icon,desc,buttons,showBackBtn=true,innerContainerStyle={}}:CustomModalProps) => {
  const { theme } = useTheme();
  
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <TouchableWithoutFeedback onPress={()=>{
          if(!isLoading) onClose();
        }}>
          <View style={styles.backgroundBlurContainer}>
            <BlurView
              style={styles.backgroundBlurBg}
              blurType="light"   
              blurAmount={5}
              reducedTransparencyFallbackColor={theme.background.secondary} 
            />
            <Toast config={toastConfig}/>
          </View>
        </TouchableWithoutFeedback>
        <TouchableWithoutFeedback onPress={onContentPress}>
          <View style={[styles.innerContainer,innerContainerStyle,{
            backgroundColor:theme.background.primary
          }]}>
            <PageLoader visible={isLoading} overlayBg='trasparent' />
            <View style={styles.headerContainer}>
                <Text style={[styles.title,
                {color:theme.text.primary}
              ]}>{title}</Text>
                {subtitle && <Text style={[styles.subtitle,
                {color:theme.text.primary}
              ]}>{subtitle}</Text>}
                {showBackBtn&&<BackBtn onPress={()=>{if(!isLoading) onClose()}} containerStyle={{left:0,padding:14}}/>}
            </View>
            {icon&&icon}
            {desc&&<Text style={[styles.desc,
                {color:theme.text.primary}
              ]}>{desc}</Text>}
            {buttons&&buttons}

          </View>
        </TouchableWithoutFeedback>
      </View>
    </Modal>
  )
}

export default CustomModal

const styles = StyleSheet.create({
    container:{
      flex:1,
      justifyContent:"center",
      alignItems:"center",
      backgroundColor:"rgba(0,0,0,0.5)"
    },
    backgroundBlurContainer:{
      position:"absolute",
      top:0,
      bottom:0,
      left:0,
      right:0,
      zIndex:1
    },
    backgroundBlurBg:{
      flex:1
    },
    innerContainer:{
      width:"80%",
      borderRadius:20,
      padding:16,
      gap:16,
      zIndex:2,
      elevation:1
    },
    headerContainer:{
      position:"relative",
      paddingHorizontal:48
    },
    title:{
      fontSize:24,
      fontFamily:AppFonts.HelixaBlack,
      textAlign:"center"
    },
    subtitle : {
      fontSize:14,
      fontFamily:AppFonts.HelixaBookItalic,
      textAlign:"center"
    },
    desc: {
      fontSize:15,
      fontFamily:AppFonts.HelixaBold,
      textAlign:"center",
      paddingHorizontal:4
    }
})