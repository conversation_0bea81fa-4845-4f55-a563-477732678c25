import { Linking, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import CustomModal from '../CustomModal'
import SimpleBtn from '../../Buttons/SimpleBtn'
import { FontAwesome } from '@expo/vector-icons'
import { useTheme } from '../../../context/ThemeContext'

type ImagePermissionModalProps = {
  visible:boolean,
  onClose:()=>void,
}

const ImagePermissionModal = ({visible,onClose}:ImagePermissionModalProps) => {
  const { theme } = useTheme();

  const openSettings = () => {
    Linking.openSettings();
  }

  return (
    <CustomModal
      visible={visible}
      onClose={onClose}
      title="Image Access"
      subtitle="Permission"
      icon={
        <FontAwesome name="image" size={96} color={theme.icon.primary}
          style={{
            alignSelf: "center"
          }}
        />
      }
      desc="Please grant image permission to continue"
      buttons={
        <View style={{ flexDirection: "row" }}>
          <SimpleBtn
            title='Open settings'
            onPress={openSettings}
            containerStyle={{
              flex: 1,
            }}
          />
        </View>
      }
    />
  )
}

export default ImagePermissionModal

const styles = StyleSheet.create({})