import { Image, Modal, Platform, StyleSheet, Text, TouchableWithoutFeedback, View } from 'react-native'
import React, { useState } from 'react'
import { BlurView } from "@react-native-community/blur";
import { useTheme } from '../../../context/ThemeContext';
import BackBtn from '../../Buttons/BackBtn';
import { AppFonts } from '../../../constants/theme/fonts/fonts';
import SwitchBtn from '../../Buttons/SwitchBtn';
import useBluetoothStore from '../../../store/BluetoothStore';
import { BluetoothStateManager } from 'react-native-bluetooth-state-manager';
import DeviceInputCard from '../../FormFields/DeviceInputCard';
import SimpleBtn from '../../Buttons/SimpleBtn';
import { Feather, Ionicons } from '@expo/vector-icons';
import ScannedDevicesModal from './ScannedDevicesModal';
import { Device } from 'react-native-ble-plx';
const BottleImg = require('../../../../assets/full-bottle-img.png')

type BluetoothDeviceModalProps = {
  visible:boolean,
  onClose:()=>void,
}

const BluetoothDeviceModal = ({visible,onClose}:BluetoothDeviceModalProps) => {
  const { theme } = useTheme();
  const isBluetoothConnected = useBluetoothStore(state => state.isBluetoothConnected);
  const requestBluetoothPermission = useBluetoothStore(state => state.requestBluetoothPermission);
  const connectingDevice = useBluetoothStore(state => state.connectingDevice);
  const connectedDevice = useBluetoothStore(state => state.connectedDevice);
  const localDeviceName = useBluetoothStore(state => state.localDeviceName);
  const editConnectDeviceLocalName = useBluetoothStore(state => state.editConnectDeviceLocalName);
  const [editingBottleName, setEditingBottleName] = useState(false);
  const removeDevice = useBluetoothStore(state => state.removeDevice);

  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const handleGetAllDevices = useBluetoothStore(state => state.handleGetAllDevices);

  const handleRemoveDevice = async() => {
    await removeDevice();
  }

  const handleAddDevice = () => {
    handleGetAllDevices();
  }

  const onModalClose = () => {
    onClose();
    setSelectedDevice(null);
    setEditingBottleName(false);
  }
  
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onModalClose}
    >
      <View style={styles.container}>
        <TouchableWithoutFeedback onPress={onModalClose}>
          <BlurView
            style={styles.backgroundBlurBg}
            blurType="light"   
            blurAmount={5}
            reducedTransparencyFallbackColor={theme.background.secondary} 
          />
        </TouchableWithoutFeedback>
        <View style={[styles.innerContainer,{
          backgroundColor:theme.background.primary
        }]}>
            <View style={styles.headerContainer}>
                <Text style={[styles.title,
            {
                color:theme.text.primary
            }
        ]}>Bluetooth</Text>
                <Text style={[styles.subtitle,
            {
                color:theme.text.quaternary
            }
        ]}>{isBluetoothConnected ? "Connected" : "Switched off"}</Text>
               <BackBtn onPress={onModalClose} containerStyle={{left:0,padding:14}}/>
                <View style={{
                    position: "absolute",
                    top: "50%",
                    right: 0,
                    transform: [{ translateY: '-50%' }],
                    zIndex: 10,
                }}>
                    <SwitchBtn
                        checked={isBluetoothConnected}
                        onToggle={async() => {
                            if(Platform.OS === 'android'){
                                await requestBluetoothPermission(true,false);
                                if(isBluetoothConnected){
                                    await BluetoothStateManager.requestToDisable();
                                }
                                else {
                                    await BluetoothStateManager.requestToEnable();
                                }
                            }
                        }}
                    />
                </View>
            </View>
            <View style={styles.mainContainer}>
                <Image source={BottleImg} style={[styles.bottleImg,
                {
                    opacity: connectedDevice || connectingDevice ? 1 : 0.25
                }
                ]} resizeMode='contain' />
               {
                  connectedDevice || connectingDevice ? (
                    <>
                        <DeviceInputCard
                            loading={connectingDevice}
                            placeholder='Bottle Name'
                            bottleModal={
                                connectedDevice?.name || connectedDevice?.localName || connectedDevice?.id || "Bottle id"
                            }
                            selectedBottleName={localDeviceName || ""}
                            setSelectedBottleName={(value) => {
                              editConnectDeviceLocalName(value);
                            }}
                            editingName={editingBottleName}
                            setEditingName={(value: boolean) => setEditingBottleName(value)}
                            containerNoEditBgColor="primary"
                            bottleModalNoEditTextColor="secondary"
                            bottleNameNoEditTextColor="secondary"
                        />
                        <SimpleBtn
                            title='Remove device'
                            onPress={handleRemoveDevice}
                            containerBgColor='trasparent'
                            titleTextColor='primary'
                            containerStyle={{
                                width: 'auto',
                                alignSelf: "center",
                                marginTop:2
                            }}
                            titleTextStyle={{
                                fontSize: 16,
                                textDecorationLine: "underline"
                            }}
                            elevation={0}
                            disabled={connectingDevice || !connectedDevice || editingBottleName}
                            disabledBgColor='trasparent'
                            disabledTitleColor='primary'
                            icon={<Feather name="trash-2" size={20} color={theme.icon.secondary} />}
                            iconPosition="right"
                        />
                        {/* <Text>
                            {`${connectingDevice} ${!connectedDevice} ${editingBottleName}`}
                        </Text> */}
                    </>
                  ):(
                    <>
                        <View style={[
                            styles.noBottleConnectedContainer,{
                                borderColor:theme.border.tertiary,
                            }
                        ]}>
                            <Text style={[
                                styles.noBottleConnectedText,{
                                    color:theme.text.primary
                                }
                            ]}>No bottle connected</Text>
                        </View>
                        <SimpleBtn
                            title='Add device'
                            onPress={handleAddDevice}
                            containerBgColor='primary'
                            titleTextColor='secondary'
                            containerStyle={{
                                width: 'auto',
                                alignSelf: "center",
                                marginTop:2,
                                paddingHorizontal:32
                            }}
                            titleTextStyle={{
                                fontSize: 16,
                            }}
                            elevation={0}
                            disabled={!isBluetoothConnected}
                        />
                    </>
                  )
               }
            </View>
        </View>
        <ScannedDevicesModal 
            selectedDevice={selectedDevice}
            setSelectedDevice={setSelectedDevice}
        />  
      </View>
    </Modal>
  )
}

export default BluetoothDeviceModal

const styles = StyleSheet.create({
    container:{
      flex:1,
      justifyContent:"center",
      alignItems:"center",
      backgroundColor:"rgba(0,0,0,0.5)"
    },
    backgroundBlurBg:{
      position:"absolute",
      top:0,
      bottom:0,
      left:0,
      right:0,
      zIndex:1
    },
    innerContainer:{
      width:"80%",
      borderRadius:20,
      padding:16,
      gap:16,
      zIndex:2,
      elevation:1
    },
    headerContainer:{
      position:"relative",
      paddingHorizontal:48
    },
    title:{
      fontSize:24,
      fontFamily:AppFonts.HelixaBlack,
      textAlign:"center"
    },
    subtitle : {
      fontSize:14,
      fontFamily:AppFonts.HelixaBookItalic,
      textAlign:"center"
    },
    mainContainer:{
    //   flex:1,
    },
    bottleImg:{
      width:"100%",
      height:"auto",
      aspectRatio: 1,
      resizeMode: "contain",
    },
    noBottleConnectedContainer:{
      justifyContent:"center",
      alignItems:"center",
      padding:8,
      borderWidth:1,
      borderRadius:100,
      marginBottom:8,
      opacity:.5
    },
    noBottleConnectedText:{
      fontSize:16,
      fontFamily:AppFonts.HelixaBold,
      textAlign:"center"
    }
})