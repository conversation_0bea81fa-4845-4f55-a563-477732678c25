import { Dimensions, FlatList, RefreshControl, StyleSheet, Text, View } from 'react-native'
import React, { useState } from 'react'
import CustomModal from '../CustomModal';
import useBluetoothStore from '../../../store/BluetoothStore';
import PageLoader from '../../Loaders/PageLoader';
import SimpleBtn from '../../Buttons/SimpleBtn';
import { Device } from 'react-native-ble-plx';
import { useTheme } from '../../../context/ThemeContext';
import ScanAndConnectBtn from '../../../screens/Onboarding/components/ScanAndConnectBtn';
import { AppFonts } from '../../../constants/theme/fonts/fonts';

interface ScannedDevicesModalProps {
    selectedDevice:Device | null,
    setSelectedDevice:(device:Device | null) => void
}

const ScannedDevicesModal = ({
    selectedDevice,
    setSelectedDevice
}:ScannedDevicesModalProps) => {
    const { theme } = useTheme();

    const showScannedDevices = useBluetoothStore(state => state.showScannedDevices);
    const setShowScannedDevices = useBluetoothStore(state => state.setShowScannedDevices);
    const scanningDevices = useBluetoothStore(state => state.scanningDevices);
    const refreshingDevices = useBluetoothStore(state => state.refreshingDevices);
    const devices = useBluetoothStore(state => state.devices);
    const startDevicesScan = useBluetoothStore(state => state.startDevicesScan);
    const connectDevice = useBluetoothStore(state => state.connectDevice);
    const connectingDevice = useBluetoothStore(state => state.connectingDevice);
    
    return (
        <CustomModal
            visible={showScannedDevices}
            onClose={() => setShowScannedDevices(false)}
            title="Bluetooth"
            subtitle="Nearby bluetooth devices"
            icon={
                <View style={[styles.btDevicesWrapper, { maxHeight: Dimensions.get('window').height / 4 }]}>
                    {
                        scanningDevices ? (
                            <View style={styles.scanningDeviceLoader}>
                                <PageLoader visible={scanningDevices} overlayBg='trasparent' />
                            </View>
                        ) : (
                            (devices.size > 0 || scanningDevices || refreshingDevices) ? (
                                <FlatList
                                    data={Array.from(devices.values())}
                                    keyExtractor={(item) => item.id}
                                    contentContainerStyle={{ gap: 16 }}
                                    refreshControl={
                                        <RefreshControl
                                            refreshing={refreshingDevices}
                                            onRefresh={() => {
                                                startDevicesScan({ isRefreshing: true });
                                            }}
                                            colors={[theme.button.primary]}
                                            progressViewOffset={-30}
                                        />
                                    }
                                    renderItem={({ item }) => (
                                        <SimpleBtn
                                            title={item.name || item.localName || item.id}
                                            onPress={() => {
                                                if (item === selectedDevice) {
                                                    setSelectedDevice(null);
                                                }
                                                else {
                                                    setSelectedDevice(item);
                                                }
                                            }}
                                            containerStyle={{
                                                flex: 1,
                                                borderWidth: 1.5
                                            }}
                                            containerBgColor={selectedDevice?.id === item.id ? "tertiary" : "secondary"}
                                            elevation={0}
                                            titleTextColor={selectedDevice?.id === item.id ? "secondary" : "primary"}
                                            borderColor={selectedDevice?.id === item.id ? "tertiary" : "primary"}
                                        />
                                    )}
                                />) : (
                                <View style={styles.noDeviceContainer}>
                                    <Text style={styles.noDeviceText}>No devices found</Text>
                                </View>
                            )
                        )
                    }
                </View>
            }
            buttons={
                <View style={{ flexDirection: "row", alignItems: 'center', justifyContent: 'center' }}>
                    <ScanAndConnectBtn
                        devices={devices}
                        connectToDevice={() => {
                            setShowScannedDevices(false);
                            if (!selectedDevice) return;
                            connectDevice(selectedDevice);
                        }}
                        startScan={() => {
                            startDevicesScan({ isRefreshing: false });
                        }}
                        disabled={
                            (devices.size !== 0 && (connectingDevice || !selectedDevice || Array.from(devices.values()).findIndex((item) => item.id === selectedDevice?.id) === -1)) || scanningDevices
                        }
                    />
                </View>
            }
        />
    )
}

export default ScannedDevicesModal

const styles = StyleSheet.create({
    btDevicesWrapper: {
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "row",
        gap: 16,
        overflow: "scroll"
    },
    scanningDeviceLoader: {
        height: Dimensions.get('window').height / 4,
        width: "100%",
        justifyContent: "center",
        alignItems: "center",
    },
    noDeviceContainer:{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
    },
    noDeviceText:{
        fontSize:16,
        fontFamily:AppFonts.HelixaBold,
        textAlign:"center"
    }
})