import { Image, Linking, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import CustomModal from '../CustomModal'
import { useTheme } from '../../../context/ThemeContext';
import useBluetoothStore from '../../../store/BluetoothStore';
import { Entypo, Feather } from '@expo/vector-icons';
import SimpleBtn from '../../Buttons/SimpleBtn';
const BTPermissionMobile = require('../../../../assets/BT_permission_mobile.png')

const BluetoothPermissionModal = () => {
    const { theme } = useTheme();
    const isBluetoothPermissionModalVisible = useBluetoothStore(state => state.isBluetoothPermissionModalVisible);
    const setIsBluetoothPermissionModalVisible = useBluetoothStore(state => state.setIsBluetoothPermissionModalVisible);

    const handleOpenSetting = () => {
        Linking.openSettings();
        setIsBluetoothPermissionModalVisible(false);
    }
    return (
        <CustomModal
            visible={isBluetoothPermissionModalVisible}
            onClose={() => setIsBluetoothPermissionModalVisible(false)}
            title="Bluetooth"
            subtitle="Permission"
            desc="Please grant bluetooth permission to continue"
            icon={
                <View style={styles.btPermissionModalIconWapper}>
                    <Image
                        source={BTPermissionMobile}
                        style={styles.btPermissionMobileImg}
                    />
                    <Entypo name="arrow-long-right" size={40} color={theme.icon.quaternary} style={styles.btArrowModal} />
                    <Feather name="bluetooth" size={26} color={theme.text.secondary} style={[
                        styles.btIconModal,
                        {
                            backgroundColor: theme.button.primary
                        }
                    ]} />
                </View>
            }
            buttons={
                <View style={{ flexDirection: "row" }}>
                    <SimpleBtn
                        title='Open settings'
                        onPress={handleOpenSetting}
                        containerStyle={{
                            flex: 1,
                        }}
                    />
                </View>
            }
        />
    )
}

export default BluetoothPermissionModal

const styles = StyleSheet.create({
    btPermissionModalIconWapper: {
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "row",
        gap: 16
    },
    btPermissionMobileImg: {
        width: "20%",
        height: "auto",
        aspectRatio: 9 / 16,
        resizeMode: "contain",
    },
    btIconModal: {
        padding: 4,
        paddingVertical: 12,
        borderRadius: 50
    },
    btArrowModal: {
        position: "absolute",
        top: "50%",
        left: "42%",
        transform: [{ translateY: '-50%' }],
    },
})