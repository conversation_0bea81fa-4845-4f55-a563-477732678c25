import { Linking, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import CustomModal from '../CustomModal'
import { Ionicons } from '@expo/vector-icons'
import useBluetoothStore from '../../../store/BluetoothStore'
import { useTheme } from '../../../context/ThemeContext'
import SimpleBtn from '../../Buttons/SimpleBtn'

const LocationPermissionModal = () => {
    const { theme } = useTheme();

    const isLocationPermissionModalVisible = useBluetoothStore(state => state.isLocationPermissionModalVisible);
    const setIsLocationPermissionModalVisible = useBluetoothStore(state => state.setIsLocationPermissionModalVisible);

    const handleOpenSettings = () => {
        Linking.openSettings();
        setIsLocationPermissionModalVisible(false);
    }

    return (
        <CustomModal
            visible={isLocationPermissionModalVisible}
            onClose={() => setIsLocationPermissionModalVisible(false)}
            title="Location"
            subtitle="Permission"
            icon={
                <Ionicons name="location-sharp" size={120} color={theme.icon.primary}
                    style={{
                        alignSelf: "center"
                    }}
                />
            }
            desc="Please grant location permission to continue"
            buttons={
            <View style={styles.locationModalBtnsContainer}>
                <SimpleBtn
                title='Open settings'
                onPress={handleOpenSettings}
                containerStyle={{
                    flex: 1,
                }}
                />
            </View>
            }
        />
    )
}

export default LocationPermissionModal

const styles = StyleSheet.create({
    locationModalBtnsContainer: {
        flexDirection: "row",
        gap: 16,
    }
})