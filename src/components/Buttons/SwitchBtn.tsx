import React, { useRef, useEffect, memo } from 'react';
import { View, TouchableOpacity, Animated, StyleSheet, Text } from 'react-native';
import { useTheme } from '../../context/ThemeContext';

interface SwitchBtnProps {
    onToggle: () => void;
    checked: boolean;
    disabled?: boolean;
}

const SwitchBtn = ({ onToggle, checked, disabled = false }: SwitchBtnProps) => {
    const { theme } = useTheme();

    const toggleAnim = useRef(new Animated.Value(checked ? 1 : 0)).current;

    useEffect(() => {
        const toValue = checked ? 1 : 0;
        Animated.timing(toggleAnim, {
            toValue,
            duration: 300,
            useNativeDriver: false,
        }).start();
    }, [checked]);

    const toggleSwitch = () => {
        if (disabled) return;
        onToggle();
    };

    const translateX = toggleAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [0, 22],
    });

    return (
        <View style={styles.container}>
            <TouchableOpacity onPress={toggleSwitch} activeOpacity={0.8}>
                <View style={[
                    styles.toggleContainer,
                    {
                        backgroundColor: checked ? theme.background.quaternary : theme.background.primary,
                        borderColor: theme.switch.primary,
                    },
                ]}>
                    <Animated.View
                        style={[
                            styles.toggleCircle,
                            { transform: [{ translateX }] },
                            { backgroundColor: checked ? theme.switch.primary  : theme.switch.tertiary },
                        ]}
                    />
                </View>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    label: {
        marginRight: 10,
        fontSize: 16,
    },
    toggleContainer: {
        width: 50,
        height: 27,
        borderRadius: 15,
        justifyContent: 'center',
        padding: 1,
        borderWidth: 1,
    },
    toggleCircle: {
        width: 24,
        height: 24,
        borderRadius: 12,
    },
});

export default memo(SwitchBtn);