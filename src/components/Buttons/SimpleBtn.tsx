import { StyleSheet, Text, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native'
import React, { memo } from 'react'
import { Theme } from '../../constants/theme/colors'
import { useTheme } from '../../context/ThemeContext'
import { ThemedStyleProp } from '../../types/ThemeStyleType'
import { AppFonts } from '../../constants/theme/fonts/fonts'

interface SimpleBtnProps {
    title: string,
    onPress: () => void,
    containerBgColor?: keyof Theme["button"],
    borderColor?: keyof Theme["border"],
    containerStyle?: ThemedStyleProp<ViewStyle, "backgroundColor"|"borderColor">,
    titleTextColor?: keyof Theme["text"],
    titleTextStyle?: ThemedStyleProp<TextStyle, "color">,
    elevation?: number,
    disabled?:boolean,
    disabledBgColor?:keyof Theme["button"],
    disabledTitleColor?:keyof Theme["text"],
    icon?:React.ReactNode,
    iconPosition?: "left" | "right"
}


const SimpleBtn = ({
    title,
    onPress,
    containerBgColor = "primary",
    borderColor="trasparent",
    containerStyle = {},
    titleTextColor = "secondary",
    titleTextStyle = {},
    elevation = 2,
    disabled=false,
    disabledBgColor="quaternary",
    disabledTitleColor="secondary",
    icon,
    iconPosition="left"
}: SimpleBtnProps) => {
    const { theme } = useTheme();
    return (
        <TouchableOpacity 
            activeOpacity={.8} 
            onPress={onPress} 
            disabled={disabled}
            style={[
                styles.wrapperBtn, containerStyle, 
                {
                    backgroundColor: theme.button[containerBgColor],
                    borderColor: theme.border[borderColor],
                    elevation: elevation
                },
                disabled && {
                    backgroundColor: theme.button[disabledBgColor],
                    borderColor: theme.border[borderColor],
                }
            ]}
        >
            <View
                style={[
                    styles.container
                ]}
            >
                {iconPosition === "left" && icon}
                <Text
                    style={[
                        styles.titleText, titleTextStyle,{
                            color: disabled ? theme.text[disabledTitleColor] : theme.text[titleTextColor]
                        }
                    ]}
                >{title}</Text>
                {iconPosition === "right" && icon}
            </View>
        </TouchableOpacity>
    )
}

export default memo(SimpleBtn)

const styles = StyleSheet.create({
    wrapperBtn: {
        width: "100%",
        borderRadius: 25,
    },
    container: {
        flexDirection: "row",
        padding: 4,
        alignItems: "center",
        paddingVertical: 6,
        gap:5,
        justifyContent:"center"
    },
    titleText: {
        fontSize: 16,
        fontFamily: AppFonts.HelixaBold,
        textAlign: "center",
    }
})