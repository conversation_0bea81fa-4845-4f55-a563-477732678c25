import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { memo } from 'react'
import { Fontisto } from '@expo/vector-icons'
import { useTheme } from '../../context/ThemeContext';

type BackBtnProps = {
  onPress:()=>void,
  containerStyle?:{}
}

const BackBtn = ({ onPress,containerStyle={},
 }:BackBtnProps) => {
    const { theme } = useTheme();
  return (
    <TouchableOpacity 
        activeOpacity={.8} 
        style={[
          styles.backBtn,
          {
            backgroundColor:theme.background.primary
          },
          containerStyle
          ]}
          onPress={onPress}
        >
        <Fontisto name="arrow-left" size={15} color={theme.icon.secondary}/>
    </TouchableOpacity>
  )
}

export default memo(BackBtn)

const styles = StyleSheet.create({
  backBtn: {
    position: "absolute",
    top: "50%",
    left: -10,
    transform: [{ translateY: '-50%' }],
    padding:15,
    zIndex:10,
    elevation:3,
    borderRadius:50
  },
})