import { StyleSheet, TouchableOpacity, ViewStyle } from 'react-native'
import React, { memo } from 'react'
import { useTheme } from '../../context/ThemeContext';
import { ThemedStyleProp } from '../../types/ThemeStyleType';
import { Theme } from '../../constants/theme/colors';

type IconBtnProps = {
  icon:React.ReactNode,
  onPress:()=>void,
  containerStyle?:ThemedStyleProp<ViewStyle,"backgroundColor"|"borderColor">,
  containerBgColor?:keyof Theme["button"],
  disabled?:boolean
}

const IconBtn = ({ icon,onPress,containerStyle={},containerBgColor="secondary",disabled=false }:IconBtnProps) => {
  const { theme } = useTheme();
  return (
    <TouchableOpacity 
      activeOpacity={.8} 
      style={[
        styles.iconBtn,
        {
          backgroundColor:theme.button[containerBgColor]
        },
        containerStyle
        ]}
        onPress={onPress}
        disabled={disabled}
    >
      {icon}
    </TouchableOpacity>
  )
}

export default memo(IconBtn)

const styles = StyleSheet.create({
  iconBtn: {
    position: "absolute",
    top: "50%",
    left: -10,
    transform: [{ translateY: '-50%' }],
    padding:15,
    zIndex:10,
    elevation:3,
    borderRadius:50
  },
})