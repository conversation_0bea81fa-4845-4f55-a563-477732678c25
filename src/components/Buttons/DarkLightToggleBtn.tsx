import React, { useEffect, useState } from 'react';
import {
  View,
  TouchableOpacity,
  Animated,
  StyleSheet,
  Text,
} from 'react-native';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/ThemeContext';

const DarkLightToggleBtn = () => {
  const { isDarkMode,toggleTheme } = useTheme();

  const [animatedValue] = useState(new Animated.Value(0));

  const toggleMode = () => {
    toggleTheme();
  };

  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [34,2],
  });

  const backgroundColorAnim = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['#FFFFFF', '#FFFFFF'],
  });

  const circleColorAnim = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['#FCD34D', '#6B7280'],
  });

  const SunIcon = ({color}:{color:string}) => (
    <MaterialIcons 
      name="sunny" 
      size={20} 
      color={color} 
    />
  );

  const MoonIcon = ({color}:{color:string}) => (
    <Ionicons 
      name="moon" 
      size={16} 
      color={color} 
    />
  );

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: isDarkMode ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [isDarkMode]);

  return (
    <TouchableOpacity style={styles.wrapperBtn} onPress={toggleMode} activeOpacity={1}>
      <Animated.View 
          style={[
          styles.toggleContainer,
          { backgroundColor: backgroundColorAnim }
          ]}
      >
          <View style={styles.iconRow}>
          <View style={styles.iconWrapper}>
              <MoonIcon color={'#000000'}/>
          </View>
          <View style={styles.iconWrapper}>
              <SunIcon color={'#000000'}/>
          </View>
          </View>
          
          <Animated.View
          style={[
            styles.toggleCircle,
            { 
              transform: [{ translateX }],
              backgroundColor: circleColorAnim
            }
          ]}
          >
          {isDarkMode ? <MoonIcon color={'#FFFFFF'}/> : <SunIcon color={'#FFFFFF'}/>}
          </Animated.View>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  wrapperBtn: {
    alignSelf:"flex-end",
    marginTop:4
  },
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  toggleContainer: {
    width: 68,
    height: 36,
    borderRadius: 20,
    position: 'relative',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  iconRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    height: '100%',
  },
  iconWrapper: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleCircle: {
    position: 'absolute',
    width: 32,
    height: 32,
    borderRadius: 18,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modeText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 20,
  },
});

export default DarkLightToggleBtn;