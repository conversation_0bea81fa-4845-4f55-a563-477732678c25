import React from "react";
import { View, StyleSheet } from "react-native";
import <PERSON><PERSON><PERSON>ie<PERSON> from "lottie-react-native";
import Loader from '../../../assets/SNBDummyLoader.json';
import { Theme } from "../../constants/theme/colors";
import { useTheme } from "../../context/ThemeContext";

type PageLoaderProps = {
    visible?: boolean;
    small?: boolean;
    overlayBg?:keyof Theme["background"]
}

const PageLoader = ({ visible = true, small = false,overlayBg="secondary" }:PageLoaderProps) => {
    const { theme } = useTheme();
    if (!visible) return null;

    return (
        <View style={[
            styles.overlay,
            { backgroundColor: theme.background[overlayBg] }
        ]}>
            <LottieView
                source={Loader}
                autoPlay
                loop
                speed={1.5}
                style={[styles.animation, small && { width: 50, height: 50 }]}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        position: "absolute",
        top:0,
        bottom:0,
        left:0,
        right:0,
        justifyContent: "center",
        alignItems: "center",
        zIndex: 9999
    },
    animation: {
        width: 150,
        height: 150,
    },
});

export default PageLoader;
