import React from "react";
import { View, StyleSheet } from "react-native";
import LottieView from "lottie-react-native";
import Loader from '../../../assets/SNBDummyLoader.json';
import { Theme } from "../../constants/theme/colors";
import { useTheme } from "../../context/ThemeContext";

type FlatListBottomLoaderProps = {
    visible?: boolean;
    small?: boolean;
    overlayBg?:keyof Theme["background"]
}

const FlatListBottomLoader = ({ visible = true, small = false,overlayBg="secondary" }:FlatListBottomLoaderProps) => {
    const { theme } = useTheme();
    if (!visible) return null;

    return (
        <View style={[
            styles.overlay,
            { backgroundColor: theme.background[overlayBg] }
        ]}>
            <LottieView
                source={Loader}
                autoPlay
                loop
                speed={1.5}
                style={[styles.animation, small && { width: 25, height: 25 }]}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        zIndex: 9999
    },
    animation: {
        width: 50,
        height: 50,
    },
});

export default FlatListBottomLoader;
