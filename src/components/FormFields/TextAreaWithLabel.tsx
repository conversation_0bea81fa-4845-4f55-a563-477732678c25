import { StyleSheet, Text, TextStyle, View, ViewStyle } from 'react-native'
import React from 'react'
import TextArea from './TextArea'
import { Theme } from '../../constants/theme/colors'
import { AppFonts } from '../../constants/theme/fonts/fonts'
import { ThemedStyleProp } from '../../types/ThemeStyleType'
import { useTheme } from '../../context/ThemeContext'

interface TextAreaWithLabelProps {
  label:string,
  placeholder?:string,
  selected:string,
  setSelected:(value:string)=>void,
  error?:string,
  clearValidationError?:()=>void,
  secure?:boolean,
  containerStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">,
  numericOnly?:boolean,
  inputContainerBgColor?:keyof Theme["background"],
  inputBgColor?:keyof Theme["background"],
  inputTextColor?:keyof Theme["text"],
  placeholderTextColor?:keyof Theme["text"],
  secureIconColor?:keyof Theme["icon"],
  inputTextStyle? :ThemedStyleProp<TextStyle,"color">
  icon?:React.ReactNode,
  loading?:boolean,
  onIconPress?:()=>void,
  disabled?:boolean,
  labelStyle?:ThemedStyleProp<TextStyle,"color">,
  labelColor?:keyof Theme["text"],
  inputContainerWrapperStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">,
  numberOfLines?:number
}


const TextAreaWithLabel = ({
  label,
  placeholder,
  selected,
  setSelected,
  error,
  clearValidationError,
  secure=false,
  containerStyle={},
  numericOnly=false,
  inputContainerBgColor="secondary",
  inputBgColor="secondary",
  inputTextColor="primary",
  placeholderTextColor="tertiary",
  secureIconColor="primary",
  inputTextStyle={},
  icon,
  onIconPress,
  loading=false,
  disabled=false,
  labelStyle={},
  labelColor="primary",
  inputContainerWrapperStyle={},
  numberOfLines=1
}:TextAreaWithLabelProps) => {

  const { theme } = useTheme();
  return (
    <View style={{...containerStyle}}>
        <Text style={[styles.label,labelStyle,{color:theme.text[labelColor]}]} numberOfLines={1}>{label}</Text>
      <TextArea 
        placeholder={placeholder} 
        selected={selected} 
        setSelected={setSelected} 
        secure={secure} 
        error={error} 
        clearValidationError={clearValidationError} 
        numericOnly={numericOnly} 
        inputContainerBgColor={inputContainerBgColor} 
        inputBgColor={inputBgColor}
        inputTextColor={inputTextColor}
        placeholderTextColor={placeholderTextColor}
        secureIconColor={secureIconColor}
        inputTextStyle={inputTextStyle}
        icon={icon}
        onIconPress={onIconPress}
        loading={loading}
        disabled={disabled}
        inputContainerWrapperStyle={inputContainerWrapperStyle}
        numberOfLines={numberOfLines}
      />
    </View>
  )
}

export default TextAreaWithLabel

const styles = StyleSheet.create({
    label:{
      marginLeft:8,
      fontSize:16,
      fontFamily:AppFonts.HelixaBold
    }
})