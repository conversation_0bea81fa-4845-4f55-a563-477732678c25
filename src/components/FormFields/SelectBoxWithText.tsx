import { DimensionValue, StyleSheet, Text, TextStyle, View, ViewStyle } from 'react-native'
import React from 'react'
import { Theme } from '../../constants/theme/colors'
import SelectBox from './SelectBox';
import { ThemedStyleProp } from '../../types/ThemeStyleType';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { useTheme } from '../../context/ThemeContext';

export interface SelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

interface SelectBoxWithLabelProps {
  label?: string;
  placeholder?: string;
  placeholderTextColor?: keyof Theme["text"];
  options?: SelectOption[];
  selectedValue?: string | number;
  onValueChange?: (value: string | number) => void;
  disabled?: boolean;
  disabledBgColor?: keyof Theme["background"];
  backgroundColor?: keyof Theme["background"];
  triggerZ?: number;
  listZ?: number;
  currentOpenDropdown?: string | null;
  scrollToOffset?: (yPos: number) => void;
  changeBG?: boolean;
  width?: DimensionValue;
  textColor?: keyof Theme["text"];
  triggerStyle?: ThemedStyleProp<ViewStyle,"backgroundColor">;
  dropdownId: string ;
  setCurrentOpenDropdown: (id: string | null) => void;
  optionsBgColor?: keyof Theme["background"];
  optionsBorderWidth?: number;
  optionsBorderRadius?: number;
  optionsBorderColor?: keyof Theme["border"];
  optionsTextColor?: keyof Theme["text"];
  activeOptionBgColor?: keyof Theme["button"];
  activeOptionTextColor?: keyof Theme["text"];
  triggerBorderWidth?: number;
  error?:string;
  clearValidationError?: () => void;
  alignDropdown?: "auto" | "flex-start" | "flex-end" | "center";
  disabledTextColor?: keyof Theme["text"];
  remeasureYpos?: boolean;
  duration?: number;
  loading?: boolean;
  maxOptionsToShow?: number;
  containerStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">;
  scrollbarThumbColor?: keyof Theme["background"];
  wrapperStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">,
  labelStyle?:ThemedStyleProp<TextStyle,"color">,
  labelColor?:keyof Theme["text"],
  selectBoxStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">
}

const SelectBoxWithLabel = ({
  label,
  placeholder,
  placeholderTextColor="tertiary",
  options = [],
  selectedValue,
  onValueChange,
  disabled,
  disabledBgColor = 'secondary',
  backgroundColor = 'secondary',
  triggerZ,
  listZ,
  currentOpenDropdown,
  scrollToOffset,
  changeBG,
  width = "auto",
  textColor = 'primary',
  triggerStyle = {},
  dropdownId,
  setCurrentOpenDropdown,
  optionsBgColor = 'primary',
  optionsBorderWidth = 1.5,
  optionsBorderRadius = 8,
  optionsBorderColor = 'tertiary',
  optionsTextColor = 'tertiary',
  activeOptionBgColor = 'tertiary',
  activeOptionTextColor = 'secondary',
  triggerBorderWidth = 1.5,
  error,
  clearValidationError,
  alignDropdown = "auto",
  disabledTextColor = 'secondary',
  remeasureYpos,
  duration = 400,
  loading = false,
  maxOptionsToShow = 3,
  containerStyle={},
  scrollbarThumbColor = 'secondary',
  wrapperStyle={},
  labelStyle={},
  labelColor="primary",
  selectBoxStyle={}
}:SelectBoxWithLabelProps) => {
  const { theme } = useTheme();
  return (
    <View style={{...wrapperStyle}}>
      <Text style={[styles.label,labelStyle,{color:theme.text[labelColor]}]} numberOfLines={1}>{label}</Text>
      <SelectBox
        placeholder={placeholder}
        placeholderTextColor={placeholderTextColor}
        options={options}
        selectedValue={selectedValue}
        onValueChange={onValueChange}
        disabled={disabled}
        disabledBgColor={disabledBgColor}
        backgroundColor={backgroundColor}
        triggerZ={triggerZ}
        listZ={listZ}
        currentOpenDropdown={currentOpenDropdown}
        scrollToOffset={scrollToOffset}
        changeBG={changeBG}
        width={width}
        textColor={textColor}
        triggerStyle={triggerStyle}
        dropdownId={dropdownId}
        setCurrentOpenDropdown={setCurrentOpenDropdown}
        optionsBgColor={optionsBgColor}
        optionsBorderWidth={optionsBorderWidth}
        optionsBorderRadius={optionsBorderRadius}
        optionsBorderColor={optionsBorderColor}
        triggerBorderWidth={triggerBorderWidth}
        activeOptionBgColor={activeOptionBgColor}
        activeOptionTextColor={activeOptionTextColor}
        optionsTextColor={optionsTextColor}
        error={error}
        clearValidationError={clearValidationError}
        alignDropdown={alignDropdown}
        disabledTextColor={disabledTextColor}
        remeasureYpos={remeasureYpos}
        duration={duration}
        loading={loading}
        maxOptionsToShow={maxOptionsToShow}
        containerStyle={containerStyle}
        scrollbarThumbColor={scrollbarThumbColor}
        selectBoxStyle={selectBoxStyle}
      />
    </View>
  )
}

export default SelectBoxWithLabel

const styles = StyleSheet.create({
    label:{
        marginLeft:8,
        fontSize:16,
        fontFamily:AppFonts.HelixaBold
    }
})