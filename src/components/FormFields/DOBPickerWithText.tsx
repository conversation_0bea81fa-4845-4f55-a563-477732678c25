import { StyleSheet, Text, TextStyle, View, ViewStyle } from 'react-native'
import React from 'react'
import DOBPicker from './DOBPicker';
import { ThemedStyleProp } from '../../types/ThemeStyleType';
import { Theme } from '../../constants/theme/colors';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { useTheme } from '../../context/ThemeContext';

interface DOBPickerWithLabelProps {
    value?: string;
    label:string,
    placeholder?: string;
    onSelectDate: (date: string, age: number) => void;
    error?: string;
    clearValidationError?: () => void;
    onPress?: () => void;
    containerStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">,
    inputBtnTextColor?:keyof Theme["text"],
    wrapperStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">,
    labelStyle?:ThemedStyleProp<TextStyle,"color">,
    labelColor?:keyof Theme["text"],
    inputContainerWrapperStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">
}

const DOBPickerWithLabel = ({
    value,
    label,
    placeholder = "Choose date",
    onSelectDate,
    error,
    clearValidationError,
    onPress,
    containerStyle={},
    inputBtnTextColor="primary",
    wrapperStyle={},
    labelStyle={},
    labelColor="primary",
    inputContainerWrapperStyle={}
}:DOBPickerWithLabelProps) => {
    const { theme } = useTheme();
  return (
    <View style={{...wrapperStyle}}>
        <Text style={[styles.label,labelStyle,{color:theme.text[labelColor]}]} numberOfLines={1}>{label}</Text>
        <DOBPicker
            value={value}
            placeholder={placeholder}
            onSelectDate={onSelectDate}
            error={error}
            clearValidationError={clearValidationError}
            onPress={onPress}
            containerStyle={containerStyle}
            inputBtnTextColor={inputBtnTextColor}
            inputContainerWrapperStyle={inputContainerWrapperStyle}
        />
    </View>
  )
}

export default DOBPickerWithLabel

const styles = StyleSheet.create({
    label:{
        marginLeft:8,
        fontSize:16,
        fontFamily:AppFonts.HelixaBold
    }
})