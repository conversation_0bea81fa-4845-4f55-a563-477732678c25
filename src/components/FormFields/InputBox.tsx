import { StyleSheet, Text, TextInput, TouchableOpacity, View, TextStyle, ViewStyle } from 'react-native'
import React, { useState } from 'react'
import { Theme } from '../../constants/theme/colors'
import Ionicons from "@expo/vector-icons/Ionicons";
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { ThemedStyleProp } from '../../types/ThemeStyleType';
import SkeletonItem from '../SkeletonLoader/SimpleSkeletonLoader';

interface InputBoxProps {
  placeholder?:string,
  selected:string,
  setSelected:(value:string)=>void,
  error?:string,
  secure?:boolean,
  clearValidationError?:()=>void,
  numericOnly?:boolean,
  inputContainerBgColor?:keyof Theme["background"],
  inputBgColor?:keyof Theme["background"],
  inputTextColor?:keyof Theme["text"],
  placeholderTextColor?:keyof Theme["text"],
  secureIconColor?:keyof Theme["icon"],
  inputTextStyle? :ThemedStyleProp<TextStyle,"color">,
  icon?:React.ReactNode,
  onIconPress?:()=>void,
  loading?:boolean,
  disabled?:boolean,
  containerStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">,
  inputContainerWrapperStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">
}

const InputBox = ({
  placeholder,
  selected,
  setSelected,
  error,
  secure = false,
  clearValidationError = ()=>{},
  numericOnly = false,
  inputContainerBgColor="secondary",
  inputBgColor="secondary",
  inputTextColor="primary",
  placeholderTextColor="tertiary",
  secureIconColor="primary",
  inputTextStyle={},
  icon,
  onIconPress,
  loading=false,
  disabled=false,
  containerStyle={},
  inputContainerWrapperStyle={}
}:InputBoxProps) => {
  const { theme } = useTheme();
  const [ showSecureText,setShowSecureText ] = useState(secure);

  if(loading) return <SkeletonItem borderRadius={8} height={39} isLoading={loading} />;
    
  return (
    <View style={[styles.container,containerStyle]}>
      <View style={[styles.inputContainerWrapper,inputContainerWrapperStyle]}>
        <View style={[styles.inputContainer,{
            backgroundColor:theme.background[inputContainerBgColor],
          }]
        }>
        <TextInput
          value={selected}
          onChangeText={(value)=>{
            setSelected(value);
            error && clearValidationError();
          }}
          style={[
            styles.textInput,{
              backgroundColor:theme.background[inputBgColor],
              color:theme.text[inputTextColor],
              ...inputTextStyle
            },
            selected.length==0 && {
              fontFamily:AppFonts.HelixaBoldItalic,
            }
          ]}
          secureTextEntry={showSecureText}
          onPress={clearValidationError}
          placeholder={placeholder}
          placeholderTextColor={theme.text[placeholderTextColor]}
          keyboardType={numericOnly?'number-pad':'default'}
          editable={!disabled}
          numberOfLines={1}
        />
          {
            icon && (
              <TouchableOpacity
                activeOpacity={.5}
                style={styles.secureIconBtn}
                onPress={onIconPress}
              >
                {icon}
              </TouchableOpacity>
            )
          }
          {secure && (
            <TouchableOpacity
              activeOpacity={.5}
              style={styles.secureIconBtn}
              onPress={() => setShowSecureText(prev=>!prev)}
            >
              {!showSecureText ? (
                <Ionicons name="eye" size={24} color={theme.icon[secureIconColor]} />
              ) : (
                <Ionicons name="eye-off" size={24} color={theme.icon[secureIconColor]} />
              )}
            </TouchableOpacity>
          )}
      </View>
      </View>
      {error&&(
        <Text 
          style={[styles.errorText,
            {color:theme.text["error"]}
          ]}
          numberOfLines={1}
        >
          {error}
        </Text>
      )}
    </View>
  )
}

export default InputBox

const styles = StyleSheet.create({
  container:{},
  inputContainerWrapper:{
    overflow:"hidden",
    borderRadius:8,
    elevation:2
  },
  inputContainer:{
    flexDirection:"row",
    justifyContent:"space-between",
    alignItems:"center",
    paddingHorizontal:8,
    elevation:2
  },
  textInput:{
    flex:1,
    fontSize:15,
    fontFamily:AppFonts.HelixaBold,
  },
  errorText:{
    marginLeft:8,
    fontSize:12,
    fontFamily:AppFonts.HelixaBold,
  },
  secureIconBtn:{
    marginLeft:4,
  }
})