import React,
{ memo } from 'react';
import { View, TouchableWithoutFeedback, Text, StyleSheet, ViewStyle } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import { Theme } from '../../constants/theme/colors';
import { ThemedStyleProp } from '../../types/ThemeStyleType';
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';

interface CheckBoxWithTextProps {
    title:string,
    checked:boolean,
    setChecked:(value:boolean)=>void,
    disabled?:boolean,
    error?:string,
    clearValidationError?:()=>void,
    containerStyle:ThemedStyleProp<ViewStyle,"backgroundColor">,
    checkedIconColor?:keyof Theme["icon"],
    checkBoxBgColor?:keyof Theme["background"],
    uncheckedBoxBgColor?:keyof Theme["background"],
    uncheckedBoxBorderColor?:keyof Theme["border"],
    errorCheckedBoxBgColor?:keyof Theme["background"],
    errorCheckedBoxBorderColor?:keyof Theme["border"],
    errorTextColor?:keyof Theme["text"]
}

const CheckBoxWithText = ({ 
    title = "Checkbox",
    checked = false,
    setChecked,
    disabled,
    error,
    clearValidationError,
    containerStyle,
    checkedIconColor="primary",
    checkBoxBgColor="secondary",
    uncheckedBoxBgColor="secondary",
    uncheckedBoxBorderColor="primary",
    errorCheckedBoxBgColor="error",
    errorCheckedBoxBorderColor="error",
    errorTextColor="error"
}:CheckBoxWithTextProps) => {
    const { theme } = useTheme();
    
    const handlePress = () => {
        const newCheckedState = !checked;
        setChecked(newCheckedState);
        clearValidationError&&clearValidationError();
    };

    return (
        <View style={[styles.checkboxMainConatiner,containerStyle]}>
            <TouchableWithoutFeedback disabled={disabled} onPress={handlePress}>
                <View style={[
                    styles.checkBoxContainer,
                    {
                        borderColor:theme.icon[checkedIconColor],
                        backgroundColor:theme.background[checkBoxBgColor]
                    },
                    !checked && {
                        backgroundColor:theme.background[uncheckedBoxBgColor],
                        borderColor:theme.border[uncheckedBoxBorderColor]
                    },
                    error && {
                        backgroundColor:theme.background[errorCheckedBoxBgColor],
                        borderColor:theme.border[errorCheckedBoxBorderColor]
                    }
                ]}>
                    {checked && (
                        <Ionicons
                            name="checkmark-sharp"
                            size={15}
                            color={theme.icon[checkedIconColor]}
                        />
                    )}
                </View>
            </TouchableWithoutFeedback>
            <View style={styles.checkboxTitleContainer}>
                <Text style={[styles.checkboxTitle,
                    error && {
                        color:theme.text[errorTextColor]
                    }
                ]}>{title}</Text>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    checkboxMainConatiner: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 15,
        overflow:"hidden",
    },
    checkBoxContainer: {
        width: 18,
        height: 18,
        borderRadius: 4,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
    },
    checkboxTitleContainer: {
        flex:1,
    },
    checkboxTitle: {
        marginLeft: 6,
        fontSize: 14,
        textAlign: 'left',
        fontFamily:AppFonts.HelixaBold
    },
});

export default memo(CheckBoxWithText);