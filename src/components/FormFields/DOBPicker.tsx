import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Platform, ViewStyle } from 'react-native';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import InputBox from './InputBox';
import SimpleBtn from '../Buttons/SimpleBtn';
import { Theme } from '../../constants/theme/colors';
import { ThemedStyleProp } from '../../types/ThemeStyleType';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { useTheme } from '../../context/ThemeContext';

interface DOBPickerProps {
    value?: string;
    placeholder?: string;
    inputBtnTextColor?:keyof Theme["text"];
    onSelectDate: (date: string, age: number) => void;
    error?: string;
    clearValidationError?: () => void;
    onPress?: () => void;
    containerStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">,
    inputContainerWrapperStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">
}

const DOBPicker = ({
    value,
    placeholder = "Choose date",
    inputBtnTextColor="primary",
    onSelectDate,
    error,
    clearValidationError,
    onPress,
    containerStyle={},
    inputContainerWrapperStyle={}
}: DOBPickerProps) => {
    const { theme } = useTheme();

    const [showCalendar, setShowCalendar] = useState(false);
    const [selectedDate, setSelectedDate] = useState(value ? new Date(value) : null);
    const [tempDate, setTempDate] = useState(value ? new Date(value) : new Date());

    // Set date limits
    const today = new Date();
    const maxDate = today;
    const minDate = new Date(today.getFullYear() - 120, today.getMonth(), today.getDate());

    // Update selectedDate when value prop changes
    useEffect(() => {
        if (value) {
            setSelectedDate(new Date(value));
            setTempDate(new Date(value));
        }
    }, [value]);

    // Calculate age from date of birth
    const calculateAge = (birthDate: Date) => {
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDifference = today.getMonth() - birthDate.getMonth();

        if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }

        return age;
    };

    // Format date for display (Date object to DD/MM/YYYY)
    const formatDateForDisplay = (date: Date) => {
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    };

    // Handle date picker change
    const handleDateChange = (event: DateTimePickerEvent, date?: Date) => {
        // For Android, we can use the default behavior
        if (Platform.OS === 'android') {
            setShowCalendar(false);
            if (event.type === 'set' && date) {
                setSelectedDate(date);
                // Format date for API (YYYY-MM-DD)
                const formattedDate = date.toISOString().split('T')[0];
                const age = calculateAge(date);
                // Call the parent component's callback with the selected date and age
                onSelectDate(formattedDate, age);
            }
        } else {
            // For iOS, just update the tempDate without closing the picker
            if (date) {
                setTempDate(date);
            }
        }
    };

    // Handle confirming date selection (for iOS)
    const handleConfirmDate = () => {
        setSelectedDate(tempDate);
        setShowCalendar(false);

        // Format date for API (YYYY-MM-DD)
        const formattedDate = tempDate.toISOString().split('T')[0];
        const age = calculateAge(tempDate);

        // Call the parent component's callback with the selected date and age
        onSelectDate(formattedDate, age);
    };

    const handleCancelDate = () => {
        setTempDate(selectedDate || new Date());
        setShowCalendar(false);
    };

    return (
        <View style={containerStyle}>
            <TouchableOpacity activeOpacity={.9} 
                onPress={() => {
                    setShowCalendar(true);
                    onPress && onPress();
                }}
                onPressIn={() => {
                    if (clearValidationError) clearValidationError();
                }}
            >
                <View pointerEvents="none">
                    <InputBox
                        selected={selectedDate ? formatDateForDisplay(selectedDate) : placeholder}
                        setSelected={()=>{}}
                        inputTextColor={
                            selectedDate ? inputBtnTextColor : "tertiary"
                        }
                        inputTextStyle={{
                            fontFamily:selectedDate ? AppFonts.HelixaBold : AppFonts.HelixaBoldItalic
                        }}
                        inputContainerWrapperStyle={inputContainerWrapperStyle}
                    />
                </View>
            </TouchableOpacity>

            {error && (
                <Text style={[styles.error,{
                    color:theme.text["error"]
                }]}>{error}</Text>
            )}

            {/* For Android: use the default DateTimePicker */}
            {showCalendar && Platform.OS === 'android' && (
                <DateTimePicker
                    value={selectedDate || new Date()}
                    mode="date"
                    display="spinner"
                    maximumDate={maxDate}
                    minimumDate={minDate}
                    onChange={handleDateChange}
                    positiveButton={{
                        textColor: theme.button["primary"],
                    }}
                />
            )}

            {/* For iOS: use a Modal with DateTimePicker and buttons */}
            {Platform.OS === 'ios' && (
                <Modal
                    visible={showCalendar}
                    transparent={true}
                    animationType="fade"
                >
                    <View style={styles.modalContainer}>
                        <View style={[styles.calendarContainer,{
                            backgroundColor:theme.background["secondary"]
                        }]}>
                            <View style={styles.calendarHeader}>
                                <Text style={[styles.calendarTitle,{
                                    color:theme.text[inputBtnTextColor]
                                }]}>{placeholder}</Text>
                            </View>

                            <DateTimePicker
                                value={tempDate}
                                mode="date"
                                display="spinner"
                                maximumDate={maxDate}
                                minimumDate={minDate}
                                onChange={handleDateChange}
                                style={styles.datePicker}
                            />

                            <View style={styles.buttonContainer}>
                                <SimpleBtn title="Cancel" onPress={handleCancelDate} containerStyle={{flex:1}} titleTextColor='primary' containerBgColor='secondary'/>

                                <SimpleBtn title="Confirm" onPress={handleConfirmDate} containerStyle={{flex:1}}/>
                            </View>
                        </View>
                    </View>
                </Modal>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    calendarContainer: {
        width: '90%',
        borderRadius: 20,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    calendarHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 15,
    },
    calendarTitle: {
        fontSize: 18,
        fontFamily: AppFonts.HelixaBlack,
    },
    datePicker: {
        width: '100%',
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20,
        gap:16
    },
    error: {
        marginHorizontal: 7,
        fontSize: 12,
        marginBottom: 0,
    },
});

export default DOBPicker;