import { StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import { useTheme } from '../../context/ThemeContext';
import SkeletonItem from '../SkeletonLoader/SimpleSkeletonLoader';
import { Theme } from '../../constants/theme/colors';
import { FontAwesome5, Foundation } from '@expo/vector-icons';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import IconBtn from '../Buttons/IconBtn';

interface DeviceInputCardProps {
  loading?:boolean,
  placeholder:string,
  bottleModal:string,
  selectedBottleName:string,
  setSelectedBottleName:(value:string)=>void,
  error?:string,
  clearValidationError?:()=>void,
  editingName?:boolean,
  setEditingName:(value:boolean)=>void,
  editIconColor?:keyof Theme["icon"],
  saveIconColor?:keyof Theme["icon"],
  containerEditBgColor?:keyof Theme["button"],
  containerNoEditBgColor?:keyof Theme["button"],
  bottleNameEditTextColor?:keyof Theme["text"],
  bottleModalNoEditTextColor?:keyof Theme["text"],
  bottleNameNoEditTextColor?:keyof Theme["text"],

}

const DeviceInputCard = ({
  loading=false,
  placeholder,
  bottleModal,
  selectedBottleName,
  setSelectedBottleName,
  error,
  clearValidationError,
  editingName=false,
  setEditingName,
  editIconColor="secondary",
  saveIconColor="secondary",
  containerEditBgColor="secondary",
  containerNoEditBgColor="secondary",
  bottleNameEditTextColor="primary",
  bottleModalNoEditTextColor="primary",
  bottleNameNoEditTextColor="primary",
}:DeviceInputCardProps) => {
  const { theme } = useTheme();
  const [ errorText,setErrorText ] = useState(error);
  const [ text,setText ] = useState(selectedBottleName);

  if(loading) return <SkeletonItem borderRadius={8} height={39} isLoading={loading} />;
    
  return (
    <View>
      <View style={[styles.container,{
        backgroundColor:theme.button[editingName ? containerEditBgColor : containerNoEditBgColor]
      }]}>
        {
          editingName ? (
            <View>
              <TextInput
                value={text}
                onChangeText={(value)=>{
                  setText(value);
                  error && clearValidationError?.();
                }}
                style={[styles.bottleNameTextInput,{color:theme.text[bottleNameEditTextColor]}]}
              />
              <IconBtn
                icon={<Foundation name="check" size={24} color={theme.icon[saveIconColor]} />}
                onPress={()=>{
                  if(!text) {
                    setErrorText("Please enter bottle name");
                    return;
                  }
                  setEditingName(false);
                  setSelectedBottleName(text);
                }}
                containerStyle={{
                  ...styles.editIconBtn,
                  paddingHorizontal:14
                }}
                containerBgColor='secondary'
              />
            </View>
          ):(
            <View>
              <Text style={[styles.bottleNameText,{color:theme.text[bottleNameNoEditTextColor]}]}>{selectedBottleName}</Text>
              <Text style={[styles.bottleModalText,{color:theme.text[bottleModalNoEditTextColor]}]}>{bottleModal}</Text>
              <IconBtn
                icon={<FontAwesome5 name="pen" size={20} color={theme.icon[editIconColor]} />}
                onPress={()=>{setEditingName(true)}}
                containerStyle={styles.editIconBtn}
                containerBgColor='secondary'
              />
            </View>
          )
        }
      </View>
      {errorText&&(
        <Text style={[styles.errorText,{color:theme.text["error"]}]}>{errorText}</Text>
      )}
    </View>
  )
}

export default DeviceInputCard

const styles = StyleSheet.create({
  container:{
    borderWidth:1,
    borderRadius:100,
    borderColor:"#E5E5E5",
    padding:8,
    gap:8
  },
  editIconBtn:{
    right:-4,
    left:"auto",
    padding:12,
    elevation:1
  },
  bottleNameTextInput:{
    fontSize:16,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center",
  },
  bottleNameText:{
    fontSize:16,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center",
  },
  bottleModalText:{
    fontSize:12,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center"
  },
  errorText:{
    fontSize:12,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center",
    marginTop:2
  }
})