import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { useTheme } from '../../../context/ThemeContext';
import { Theme } from '../../../constants/theme/colors';

interface UpgradeIconProps {
  fill?: keyof Theme["icon"];
  width?: number;
  height?: number;
}

const UpgradeIcon = ({ 
  fill = "primary", 
  width = 16, 
  height = 15, 
  ...props 
}: UpgradeIconProps) => {
    const { theme } = useTheme();
    return (
        <Svg 
            width={width} 
            height={height} 
            viewBox="0 0 16 15" 
            fill="none"
            {...props}
        >
            <Path
            d="M11.8777 14.1473C11.8777 14.4532 11.6297 14.7012 11.3237 14.7012H4.6762C4.37026 14.7012 4.12224 14.4532 4.12224 14.1473C4.12224 13.8413 4.37026 13.5933 4.6762 13.5933H11.3237C11.6296 13.5933 11.8777 13.8413 11.8777 14.1473ZM11.3237 11.3775H4.6762C4.37026 11.3775 4.12224 11.6255 4.12224 11.9314C4.12224 12.2374 4.37026 12.4854 4.6762 12.4854H11.3237C11.6297 12.4854 11.8777 12.2374 11.8777 11.9314C11.8777 11.6255 11.6297 11.3775 11.3237 11.3775ZM15.0392 7.10804L8.39166 0.460531C8.17529 0.244276 7.82462 0.244276 7.60825 0.460531L0.960743 7.10804C0.802303 7.26647 0.754902 7.50475 0.840643 7.71175C0.926386 7.91876 1.12839 8.05373 1.35245 8.05373H4.12224V9.71561C4.12224 10.0215 4.37026 10.2696 4.6762 10.2696H11.3237C11.6296 10.2696 11.8777 10.0216 11.8777 9.71561V8.05373H14.6475C14.8715 8.05373 15.0735 7.91876 15.1593 7.71175C15.245 7.50475 15.1976 7.26647 15.0392 7.10804Z"
            fill={theme.icon[fill]}
            />
        </Svg>
    )
};

export default UpgradeIcon;