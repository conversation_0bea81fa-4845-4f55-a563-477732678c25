import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { useTheme } from '../../../context/ThemeContext';
import { Theme } from '../../../constants/theme/colors';

interface PrivacyIconProps {
  fill?: keyof Theme["icon"];
  width?: number;
  height?: number;
}

const PrivacyIcon = ({ 
  fill = "primary", 
  width = 16, 
  height = 16, 
  ...props 
}: PrivacyIconProps) => {
    const { theme } = useTheme();
    return (
        <Svg 
            width={width} 
            height={height} 
            viewBox="0 0 18 17"
            fill="none"
            {...props}
        >
            <Path
                d="M14.7064 13.3948C12.5339 15.8938 8.98858 16.6895 5.95648 15.3586C2.9244 14.0274 1.11052 10.8789 1.47983 7.5881C1.84885 4.297 4.31562 1.6285 7.56763 1.00244M10.4331 1.00858C12.5859 1.42318 14.453 2.7513 15.5508 4.64899C16.6487 6.54671 16.8693 8.82739 16.1555 10.9004"
                stroke={theme.icon[fill]} 
                strokeLinecap="round"
            />
            <Path
                d="M7.36641 8.02784V6.56594C7.36641 5.67951 8.09869 4.96094 9.00209 4.96094C9.9055 4.96094 10.6378 5.67951 10.6378 6.56594V8.02784M6.95749 8.02784H11.0467C11.4998 8.02784 11.8645 8.39259 11.8645 8.84568V11.0947C11.8645 11.5478 11.4998 11.9126 11.0467 11.9126H6.95749C6.50439 11.9126 6.13965 11.5478 6.13965 11.0947V8.84568C6.13965 8.39259 6.50439 8.02784 6.95749 8.02784Z"
                stroke={theme.icon[fill]}
            />
        </Svg>
        )
};

export default PrivacyIcon;
