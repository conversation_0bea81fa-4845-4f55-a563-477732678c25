import { StyleSheet, Text, View, ViewStyle } from 'react-native'
import React from 'react'
import { useTheme } from '../../../context/ThemeContext';
import { ThemedStyleProp } from '../../../types/ThemeStyleType';

interface CartridgeUIProps {
    cartidgeColor:string,
    tabletColor:string,
    numTablets:number,
    containerStyle?:ThemedStyleProp<ViewStyle,"backgroundColor"|"borderColor">,
    tabletContainerStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">,
    borderTopEndRadius?:number,
    borderTopStartRadius?:number,
    borderBottomEndRadius?:number,
    borderBottomStartRadius?:number,
}

const CartridgeUI = ({cartidgeColor,tabletColor,numTablets,containerStyle,tabletContainerStyle,borderTopEndRadius = 8,borderTopStartRadius = 8,borderBottomEndRadius = 8,borderBottomStartRadius = 8}:CartridgeUIProps) => {
    const { theme } = useTheme();
  return (
    <View style={[styles.container,
        {borderColor:cartidgeColor,backgroundColor:cartidgeColor},
        containerStyle
    ]}>
      <View style={[styles.tabletContainer,tabletContainerStyle,{backgroundColor:theme.background.primary}]}>
        {
            Array.from({ length: 5 }, (_, index) => (
                <View key={index} style={[styles.tablet,
                    {backgroundColor:5-numTablets<=index?tabletColor:'#ffffff'},
                    index==0&&{borderTopEndRadius:borderTopEndRadius||8,borderTopStartRadius:borderTopStartRadius||8},
                    index==4&&{borderBottomEndRadius:borderBottomEndRadius||8,borderBottomStartRadius:borderBottomStartRadius||8}
                ]}/>
            ))
        }
      </View>
    </View>
  )
}

export default CartridgeUI

const styles = StyleSheet.create({
    container:{
        width:41,
        height:139,
        backgroundColor:"#FFFFFF",
        borderRadius:8,
        elevation:10,
        borderWidth:4,
        paddingBottom:12,
    },
    tabletContainer:{
        width:"100%",
        height:'100%',
        borderRadius:8,
        padding:2,
        gap:2
    },
    tablet:{
        flex:1,
        width:"100%",
    }
})