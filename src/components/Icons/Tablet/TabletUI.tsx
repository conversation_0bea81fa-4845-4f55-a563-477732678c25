import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { useTheme } from '../../../context/ThemeContext';

interface TabletUIProps {
    cartridgeColor: string;
    tabletColor: string;
    outer?: {
        borderRadius?: number;
        borderWidth?: number;
        padding?: number;
        size?: number;
    };
    inner?: {
        borderRadius?: number;
    };
}

const TabletUI = ({
    cartridgeColor,
    tabletColor,
    outer = { borderRadius: 16, borderWidth: 2.5, padding: 3, size: 57 },
    inner = { borderRadius: 12 },
}: TabletUIProps) => {
    const { theme } = useTheme();

    return (
        <View
            style={[
                styles.pillIcon,
                {
                    backgroundColor: theme.background.primary,
                    borderColor: cartridgeColor,
                    borderRadius: outer.borderRadius,
                    width: outer.size,
                    height: outer.size,
                    borderWidth: outer.borderWidth,
                    padding: outer.padding,
                },
            ]}
        >
            <View
                style={[
                    styles.pill,
                    { backgroundColor: tabletColor, borderRadius: inner.borderRadius },
                ]}
            />
        </View>
    );
};

export default TabletUI;

const styles = StyleSheet.create({
    pillIcon: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    pill: {
        width: '100%',
        height: '100%',
    },
});
