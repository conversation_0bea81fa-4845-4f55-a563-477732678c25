import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { useTheme } from '../../../context/ThemeContext';

interface BottleWithWaterProps {
    liquidColor:string
}

const BottleWithWater = ({liquidColor}:BottleWithWaterProps) => {
    const { theme } = useTheme();
  return (
    <View style={[styles.bottleIconContainerWrapper,{
        borderColor:theme.border.tertiary
    }]}>
    <View style={[styles.bottleIconContainer,{
        aspectRatio:3.2,
        borderBottomWidth:1,
        borderBottomColor:theme.border.tertiary
    }]}/>
    <View style={[styles.bottleIconContainer,{
        aspectRatio:9/11,
        borderBottomWidth:1,
        borderBottomColor:theme.border.tertiary,
        marginBottom:1
    }]}/>
    <View style={[styles.bottleIconContainer,{
        backgroundColor:liquidColor,
        borderEndEndRadius:12,
        borderBottomLeftRadius:12,
    }]}/>

</View>
  )
}

export default BottleWithWater

const styles = StyleSheet.create({
    bottleIconContainerWrapper:{
        borderRadius:12,
        overflow:"hidden",
        borderWidth:1,
        padding:2
    },
    bottleIconContainer:{
        width:56,
        aspectRatio:9/14,
    }
})