import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import { useTheme } from '../../context/ThemeContext';

const SimpleSkeletonItem = ({
  width: itemWidth = '100%',
  height = 20,
  borderRadius = 4,
  style = {},
  isCircle = false,
  isLoading = true,
  children = null,
}) => {
  const { theme } = useTheme();
  // Animation opacity value
  const opacity = useSharedValue(0.5);

  // Start the animation when component mounts
  useEffect(() => {
    opacity.value = withRepeat(
      withTiming(1, { duration: 1000, easing: Easing.bezier(0.25, 0.1, 0.25, 1) }),
      -1, // Infinite repeat
      true // Reverse
    );
  }, []);

  // Animated style for the pulse effect
  const pulseStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  // If not in loading state, render children
  if (!isLoading) {
    return children;
  }

  // Calculate dimensions for circle if isCircle is true
  const dimensions = isCircle
    ? {
      width: height,
      height: height,
      borderRadius: height / 2,
    }
    : {
      width: itemWidth,
      height,
      borderRadius,
    };

  return (
    <Animated.View
      style={[
        styles.skeletonContainer,
        {
          backgroundColor:theme.background.secondary
        },
        dimensions,
        pulseStyle,
        style,
      ]}
    />
  );
};

/**
 * SkeletonCard - A skeleton loader for card-like UI elements
 */
export const SimpleSkeletonCard = ({
  width = '100%',
  height = 120,
  borderRadius = 25,
  style = {},
  isLoading = true,
  children = null,
}) => {
  return (
    <SimpleSkeletonItem
      width={width}
      height={height}
      borderRadius={borderRadius}
      style={style}
      isLoading={isLoading}
    >
      {children}
    </SimpleSkeletonItem>
  );
};

/**
 * SkeletonCircle - A circular skeleton loader
 */
export const SimpleSkeletonCircle = ({
  size = 40,
  style = {},
  isLoading = true,
  children = null,
}) => {
  return (
    <SimpleSkeletonItem
      height={size}
      borderRadius={size / 2}
      style={style}
      isCircle={true}
      isLoading={isLoading}
    >
      {children}
    </SimpleSkeletonItem>
  );
};

/**
 * SkeletonText - A skeleton loader for text elements
 */
export const SimpleSkeletonText = ({
  width = '80%',
  height = 15,
  style = {},
  isLoading = true,
  children = null,
}) => {
  return (
    <SimpleSkeletonItem
      width={width}
      height={height}
      style={style}
      isLoading={isLoading}
    >
      {children}
    </SimpleSkeletonItem>
  );
};

const styles = StyleSheet.create({
  skeletonContainer: {
    overflow: 'hidden',
  },
});

export default SimpleSkeletonItem;
