
import { StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import { useNetInfo } from '@react-native-community/netinfo';
import PageLoader from '../Loaders/PageLoader';
import { useTheme } from '../../context/ThemeContext';
import SimpleBtn from '../Buttons/SimpleBtn';
import { AppFonts } from '../../constants/theme/fonts/fonts';

const ConnectivityWrapper = ({children}:{children:React.ReactNode}) => {
  const { theme } = useTheme();

  const [isServerConnected, setIsServerConnected] = useState(true);
  const [loading, setLoading] = useState(false);

  const  { isConnected:isInternetConnected } = useNetInfo();

  const checkServerConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/health`);
      setIsServerConnected(response.ok);
    } catch (error) {
      setIsServerConnected(false);
    }
    setLoading(false);
  };


  useEffect(() => {
    checkServerConnection();
  }, []);
;

  if(loading || isInternetConnected==null){
    return (
       <View style={[styles.container, { backgroundColor: theme.background.primary }]}>
        <PageLoader overlayBg='trasparent'/>
       </View>
    );
  }

  if (!isInternetConnected || !isServerConnected) {
    return (
      <View style={[styles.container, { backgroundColor: theme.background.primary }]}>
        <Text style={[styles.errorText,
          {color:theme.text.primary}
        ]}>{!isInternetConnected ? 'No Internet Connection':'Server Not Responding'}</Text>
        <SimpleBtn
          title='Retry'
          onPress={() => checkServerConnection()}
          containerBgColor='primary'
          titleTextColor='secondary'
          containerStyle={{
            width: 'auto',
            paddingHorizontal: 32,
          }}
        />
      </View>
    );
  }

  return (
    <View style={{flex:1}}>
      {children}
    </View>
  )
}

export default ConnectivityWrapper


const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontFamily: AppFonts.HelixaBold,
    fontSize: 20,
    marginBottom: 20,
  },
});