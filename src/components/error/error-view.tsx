import { ExclamationCircleOutlined } from '@ant-design/icons';
import React from 'react';

interface ErrorProps {
  message?: string;
}

export const ErrorView: React.FC<ErrorProps> = ({ message }) => {
  return (
    <div className="text-red-500 flex h-full flex-col items-center justify-center">
      <ExclamationCircleOutlined style={{ fontSize: '40px' }} />
      {message && <p className="mt-4 text-lg">{message}</p>}
    </div>
  );
};
