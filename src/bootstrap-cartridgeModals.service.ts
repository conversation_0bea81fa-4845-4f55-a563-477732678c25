import { CartridgeModal } from "models/cartridge/cartridgeModal.schema";
import { CustomLogger } from "./common/services";
import { Model } from "mongoose";
import { InjectModel } from "@nestjs/mongoose";
import { Nutrient } from "models/cartridge/nutrient.schema";
import NutrientsData from "./constants/cartridge/nutrients";
import { cartridgeData } from "./constants/cartridge/cartridgeModal";

export class BootstrapCartidgeModalService{
    constructor(
        private readonly logger: CustomLogger,

        @InjectModel(Nutrient.name)
        private readonly nutrientModel: Model<Nutrient>,

        @InjectModel(CartridgeModal.name)
        private readonly cartridgeModel: Model<CartridgeModal>,
        
    ){
        // this.logger.setContext('BootstrapCartidgeModalService');
    }

    async bootstrapNutrientModal() {
        this.logger.log('Bootstrapping Nutrient Modal...');

        try {
            await Promise.all(
            NutrientsData.map(async (nutrient) => {
                const existingNutrient = await this.nutrientModel.findOne({ name: nutrient.name });
                if (!existingNutrient) {
                    const newNutrient = new this.nutrientModel(nutrient);
                    await newNutrient.save();
                    this.logger.log(`Nutrient ${nutrient.name} created successfully.`);
                } else {
                    this.logger.log(`Nutrient ${nutrient.name} already exists.`);
                }
            }),
            );
        } catch (error) {
            this.logger.error(error);
        }
    }


    async bootstrapCartridgeModal() {
        this.logger.log('Bootstrapping Cartridge Modal...');

        try {
            await Promise.all(
            cartridgeData.map(async (cartridge) => {
                const existingCartridge = await this.cartridgeModel.findOne({ healthName: cartridge.healthName });
                if (!existingCartridge) {
                    const nutrients = await Promise.all(
                        cartridge.nutrients.map(async (nutrient) => {
                        const existingNutrient = await this.nutrientModel.findOne({ name: nutrient.name });
                        if (!existingNutrient) {
                            throw new Error(`Nutrient ${nutrient.name} does not exist.`);
                        }
                        return {
                            nutrientId: existingNutrient._id,
                            rda: {
                            quantity: nutrient.rda.quantity,
                            unit: nutrient.rda.unit,
                            percentage: nutrient.rda.percentage || null,
                            },
                        };
                        }),
                    );

                    const newCartridge = new this.cartridgeModel({
                        ...cartridge,
                        nutrients,
                    });
                    await newCartridge.save();
                    this.logger.log(`Cartridge ${cartridge.healthName} created successfully.`);
                } else {
                    this.logger.log(`Cartridge ${cartridge.healthName} already exists.`);
                }
            }),
            );
        } catch (error) {
            this.logger.error(error);
        }
    }

}