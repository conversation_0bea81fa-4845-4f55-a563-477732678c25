import { App as AntdApp } from 'antd';
import { Helmet } from 'react-helmet-async';

import Router from '@/router/index';
import AntdConfig from '@/theme/antd';

import { MotionLazy } from './components/animate/motion-lazy';
import { useUserActions } from './store/userStore';
import { eventBus } from './api/services/axios/eventBus';
import { useEffect } from 'react';

const TITLE = import.meta.env.VITE_GLOB_APP_TITLE;

function App() {
  const { clearUserInfoAndToken } = useUserActions();
  // const { backToLogin } = useLoginStateContext();

  useEffect(() => {
    const handleLogout = () => {
      clearUserInfoAndToken();
    };

    eventBus.on('logout', handleLogout);
    return () => eventBus.off('logout', handleLogout);
  }, []);
  return (
    <AntdConfig>
      <AntdApp>
        <MotionLazy>
          <Helmet>
            <title>{TITLE}</title>
          </Helmet>

          <Router />
        </MotionLazy>
      </AntdApp>
    </AntdConfig>
  );
}

export default App;
