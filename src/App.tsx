import * as SplashScreen from 'expo-splash-screen';
import * as React from 'react';
import { RootStack } from './navigation/RootStack';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import useAppFonts, { AppFonts } from './constants/theme/fonts/fonts';
import { Platform, StyleSheet } from 'react-native';
import { useEffect , useState } from 'react';
import useAppStateStore from './store/AppStateStore';
import { ThemeProvider } from './context/ThemeContext';
import Toast from 'react-native-toast-message';
import toastConfig from './config/CustomToastConfig';
import setAxiosInterceptors from './apis/axios/axiosInterceptors';
import useBluetoothStore from './store/BluetoothStore';
import { NavigationContainer } from '@react-navigation/native';
import {linkingConfig} from './navigation/config/linkingConfig';
import { navigationRef } from './navigation/navigationRef';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

SplashScreen.preventAutoHideAsync();

const queryClient = new QueryClient();

export function App() {
  const {fontsLoaded,fontError} = useAppFonts();

  const [ navigationReady,setNavigationReady ] = useState(false);
  const setAppReady = useAppStateStore(state=>state.setAppReady);

  const reconnectDevice = useBluetoothStore(state=>state.reconnectDevice);
  const bleManager = useBluetoothStore(state=>state.bleManager);
  
  const setIsBluetoothConnected = useBluetoothStore(state=>state.setIsBluetoothConnected);

  useEffect(() => {
    if (fontsLoaded || fontError) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  useEffect(() => {
    const toggleCustomSplashScreen = async() => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setAppReady(true);
    };
    toggleCustomSplashScreen();
  }, [ navigationReady ]);

  useEffect(()=>{
    setAxiosInterceptors();
  },[]);

  useEffect(() => {
    const subscription = bleManager.onStateChange((state) => {
      if (state === 'PoweredOn') {
        reconnectDevice();
        setIsBluetoothConnected(true);
      }
      else {
        setIsBluetoothConnected(false)
      }
    }, true);

    return () => {
        subscription.remove()
    }
  }, [])

  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <NavigationContainer 
          onReady={() => {
            setNavigationReady(true);
          }}
          linking={linkingConfig}
          ref={navigationRef}>
          <SafeAreaView
            style={{
              flex: 1,
            }}
            edges={["bottom"]}
          >
             <QueryClientProvider client={queryClient}> 
              <RootStack/>
             </QueryClientProvider>
          </SafeAreaView>
          <Toast config={toastConfig}/>
        </NavigationContainer>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontFamily: AppFonts.HelixaBold,
    fontSize: 20,
    marginBottom: 20,
  },
});