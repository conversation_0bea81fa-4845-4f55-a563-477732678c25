import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { BaseToast, BaseToastProps, ErrorToast } from "react-native-toast-message";
import { useTheme } from "../context/ThemeContext";
import { AppFonts } from "../constants/theme/fonts/fonts";

type CustomToastProps = BaseToastProps & {
  props: {
    type:"success" | "error",
    onPress?: () => void;
    linkText?: string;
  };
};

const CustomToast = ({ text1, text2, props }: CustomToastProps) => {
  const { theme } = useTheme();

  return (
    <View
      style={{
        width:340,
        height: "auto",
        minHeight: 60,
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderLeftWidth: 5,
        borderLeftColor: props.type === "success" ? theme.toast.success : theme.toast.error,
        backgroundColor: theme.background.primary,
        borderRadius: 8,
      }}
    >
      {text1 ? (
        <Text
          style={{
            fontFamily: AppFonts.HelixaBlack,
            color: theme.text.primary,
            fontSize: 16,
            marginBottom: 4,
          }}
        >
          {text1}
        </Text>
      ) : null}

      {text2 ? (
        <Text
          style={{
            fontFamily: AppFonts.HelixaBook,
            color: theme.text.primary,
            fontSize: 14,
            marginBottom: props?.onPress ? 6 : 0,
          }}
        >
          {text2}
        </Text>
      ) : null}

      {props?.onPress ? (
        <TouchableOpacity onPress={props.onPress}>
          <Text
            style={{
              fontFamily: AppFonts.HelixaBook,
              color: theme.toast.success,
              fontSize: 14,
              textDecorationLine: "underline",
            }}
          >
            {props.linkText || "Tap here"}
          </Text>
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

const toastConfig = {
  success: (props: BaseToastProps) => {
    const { theme } = useTheme();
    return (
      <BaseToast
        {...props}
        style={{
          height: "auto",
          minHeight: 60,
          paddingVertical: 8,
          borderLeftColor: theme.toast.success,
          backgroundColor: theme.background.primary,
        }}
        text1Style={{
          fontFamily: AppFonts.HelixaBold,
          color: theme.text.primary,
          fontSize: 16,
        }}
        text2Style={{
          fontFamily: AppFonts.HelixaBook,
          color: theme.text.primary,
          fontSize: 14,
        }}
        text2NumberOfLines={0}
      />
    );
  },

  error: (props: BaseToastProps) => {
    const { theme } = useTheme();
    return (
      <ErrorToast
        {...props}
        style={{
          height: "auto",
          minHeight: 60,
          paddingVertical: 8,
          borderLeftColor: theme.toast.error,
          backgroundColor: theme.background.primary,
        }}
        text1Style={{
          fontFamily: AppFonts.HelixaBold,
          color: theme.text.primary,
          fontSize: 16,
        }}
        text2Style={{
          fontFamily: AppFonts.HelixaBook,
          color: theme.text.primary,
          fontSize: 14,
        }}
        text2NumberOfLines={0}
      />
    );
  },

  customWithAction: (props: CustomToastProps) => <CustomToast {...props} />,
};

export default toastConfig;
