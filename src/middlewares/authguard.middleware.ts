import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { InjectModel } from '@nestjs/mongoose';
import { AccessToken, RefreshToken } from 'models/auth';
import { ROLE_VALUES } from 'models/user/user.schema';
import { Model } from 'mongoose';
import { CustomLogger } from 'src/common/services';
import { UserJourneyService } from 'src/common/services';
import { UserRepoService } from 'src/repo/user-repo.service';
import { convertDOBtoAge } from 'src/utils/dateConvertors/convertDOBtoAge';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly logger: CustomLogger,
    private readonly userRepo: UserRepoService,
    private readonly userJourneyService: UserJourneyService,

    @InjectModel(AccessToken.name) private accessTokenModel: Model<AccessToken>,
  ) {
    this.logger.setContext('AuthGuard');
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const authority = this.reflector.get<boolean>(
      'authority',
      context.getHandler(),
    );

    const request = context.switchToHttp().getRequest();
    const authHeader = request.get('Authorization');
    const authorization = authHeader ? authHeader.split(' ')[1] : null;

    if (!authorization) {
      throw new UnauthorizedException('Login to access this route...');
    }

    try {
      const accessToken = await this.accessTokenModel.findOne({
        token: authorization,
        isExpired: false,
      });

      if (!accessToken || accessToken.expiry.getTime() < Date.now()) {
        throw new UnauthorizedException(
          'Authentication token is missing or invalid. Please log in again.',
        );
      }

      const user = await this.userRepo.findUserById(
        accessToken.userId,
      );

      if (!user.isEmailVerified) {
        throw new UnauthorizedException(
          'Your account is not verified. Please verify your account first.',
        );
      }

      if (!authority) {
        request['user'] = user;
        if (user.isAccountCompleted) {
          await this.userJourneyService.ensureUserJourney(
            user._id,
            user.timeZone,
            convertDOBtoAge(user.dob),
          );
        }
        return true;
      }

      const isAdmin = user.role === ROLE_VALUES.ADMIN;
      if (isAdmin) {
        request['user'] = user;
        return true;
      }

      throw new UnauthorizedException(
        'User does not have permissions to access this route.',
      );
    } catch (error) {
      throw error;
    }
  }
}
