import { useMutation } from '@tanstack/react-query';
import { App } from 'antd';
import { useNavigate } from 'react-router-dom';

import {
  DeleteDeviceApi,
  CreatDeviceApi,
  UpdateDeviceApi,
  AddBulkDevicesApi,
} from '../services/device/deviceService';

const useAddBulkDevices = () => {
  const { message } = App.useApp();

  const navigate = useNavigate();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ data }: { data: FormData }) => AddBulkDevicesApi(data),
  });

  const addBulkDevices = async (data: FormData) => {
    try {
      const { msg, error } = await mutateAsync({ data });
      if (error === false) {
        message.success(msg || 'CSV processed succcessfully !!');
        navigate('/Device/list_Device');
      } else {
        message.error(msg || `Failed to create the Devices. Please try again.`);
      }
    } catch (error: any) {
      console.error({ error });
      message.error(
        error?.response?.data?.message || `An error occurred while creating the Device.`,
      );
    }
  };

  return addBulkDevices;
};

const useCreateDevice = () => {
  const { message } = App.useApp();

  const navigate = useNavigate();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ data }: { data: FormData }) => CreatDeviceApi(data),
  });

  const createDevice = async (data: FormData) => {
    try {
      const { msg, error } = await mutateAsync({ data });
      if (error === false) {
        message.success(msg || 'Device created succcessfully !!');
        navigate('/device/list_Device');
      } else {
        message.error(msg || `Failed to create the Device. Please try again.`);
      }
    } catch (error: any) {
      console.error({ error });
      message.error(
        error?.response?.data?.message || `An error occurred while creating the Device.`,
      );
    }
  };

  return createDevice;
};

const useDeleteDevice = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id }: { id: string }) => {
      return await DeleteDeviceApi(id);
    },
  });

  const deleteDevice = async (id: string, onDelete: () => void) => {
    try {
      const { msg, error } = await mutateAsync({ id });

      if (error === false) {
        message.success(`Device deleted successfully.`);
        onDelete();
      } else {
        message.error(msg || `Failed to delete the Device. Please try again.`);
        onDelete();
      }
    } catch (err: any) {
      console.error({ err });

      message.error(err?.response?.data?.message || `An error occurred while deleting the Device.`);
    }
  };

  return { deleteDevice };
};

const useUpdateDevice = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id, updateData }: { id: string; updateData: FormData }) => {
      return await UpdateDeviceApi(id, updateData);
    },
  });

  const UpdateDevice = async (id: string, updateData: FormData) => {
    try {
      const { error } = await mutateAsync({ id, updateData });

      if (error === false) {
        return true;
      } else {
        return false;
      }
    } catch (err: any) {
      console.error({ err });

      message.error(err?.response?.data?.message || `An error occurred while updating the Device.`);
    }
  };

  return { UpdateDevice };
};

export { useUpdateDevice, useDeleteDevice, useCreateDevice, useAddBulkDevices };
