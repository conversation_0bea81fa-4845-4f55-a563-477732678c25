import { useMutation } from '@tanstack/react-query';
import { App } from 'antd';
import { useNavigate } from 'react-router-dom';

import {
  DeleteMoodMediaApi,
  CreatMoodMediaApi,
  UpdateMoodMediaApi,
} from '../services/mood-media/moodService';
import { CreatMoodMediaReqObj, UpdateMoodMediaApiReqObj } from '../services/mood-media/types';

const useCreateMood = () => {
  const { message } = App.useApp();

  const navigate = useNavigate();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ data }: { data: CreatMoodMediaReqObj }) => CreatMoodMediaApi(data),
  });

  const createMood = async (data: CreatMoodMediaReqObj) => {
    try {
      const { msg, error } = await mutateAsync({ data });
      if (error === false) {
        message.success(msg || 'Mood created succcessfully !!');
        navigate('/mood_videos/list_mood_videos');
      } else {
        message.error(msg || `Failed to create the Mood. Please try again.`);
      }
    } catch (error: any) {
      console.error({ error });
      message.error(error?.response?.data?.message || `An error occurred while creating the Mood.`);
    }
  };

  return createMood;
};

const useDeleteMoodVideo = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id }: { id: string }) => {
      return await DeleteMoodMediaApi(id);
    },
  });

  const deleteMood = async (id: string, onDelete: () => void) => {
    try {
      const { msg, error } = await mutateAsync({ id });

      if (error === false) {
        message.success(`Mood deleted successfully.`);
        onDelete();
      } else {
        message.error(msg || `Failed to delete the Mood. Please try again.`);
        onDelete();
      }
    } catch (err: any) {
      console.error({ err });

      message.error(err?.response?.data?.message || `An error occurred while deleting the Mood.`);
    }
  };

  return { deleteMood };
};

const useUpdateMoodVideo = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({
      id,
      updateData,
    }: {
      id: string;
      updateData: UpdateMoodMediaApiReqObj;
    }) => {
      return await UpdateMoodMediaApi(id, updateData);
    },
  });

  const UpdateMoodVideo = async (id: string, updateData: UpdateMoodMediaApiReqObj) => {
    try {
      const { msg, error } = await mutateAsync({ id, updateData });

      if (error === false) {
        message.success(`Mood updated successfully.`);
        return true;
      } else {
        message.error(msg || `Failed to delete the Mood. Please try again.`);
        return false;
      }
    } catch (err: any) {
      console.error({ err });

      message.error(err?.response?.data?.message || `An error occurred while deleting the Mood.`);
    }
  };

  return { UpdateMoodVideo };
};

export { useUpdateMoodVideo, useDeleteMoodVideo, useCreateMood };
