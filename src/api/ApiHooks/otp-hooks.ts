// nouriq-admin/src/api/ApiHooks/otp-hooks.ts
import { useMutation } from '@tanstack/react-query';
import { App } from 'antd';

import { verifyOtpApi } from '../services/otp/otpService';

const useVerifyOtp = () => {
  const { message } = App.useApp();

  const { mutateAsync, isPending } = useMutation({
    mutationFn: ({ otp }: { otp: string }) => verifyOtpApi(otp),
  });

  const errMsg = `An error occurred during OTP verification, Please try again later`;

  const verify = async (otp: string, onSuccess: (data: any) => void) => {
    try {
      const res = await mutateAsync({ otp });
      if (res.error === false) {
        message.success(res.message);
        onSuccess(res);
      } else {
        message.warning({
          content: res.message || errMsg,
          duration: 3,
        });
      }
    } catch (err) {
      message.warning({
        content: err?.response?.data?.message || errMsg,
        duration: 3,
      });
    }
  };

  return { verify, isPending };
};

export { useVerifyOtp };
