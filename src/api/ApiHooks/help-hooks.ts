import { useMutation } from '@tanstack/react-query';
import { App } from 'antd';

import { DeleteHelpApi, CreatHelpApi, UpdateHelpApi } from '../services/help/helpService';
import { UpdateHelpApiReqObj, CreateHelpReqObj } from '../services/help/types';

const useCreateHelp = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ data }: { data: CreateHelpReqObj }) => CreatHelpApi(data),
  });

  const createHelp = async (data: CreateHelpReqObj, onSave: () => void) => {
    try {
      const { msg, error } = await mutateAsync({ data });
      if (error === false) {
        message.success(msg || 'Help created succcessfully !!');
        onSave();
      } else {
        message.error(msg || `Failed to create the Help data. Please try again.`);
      }
    } catch (error: any) {
      console.error({ error });
      message.error(
        error?.response?.data?.message || `An error occurred while creating the Help data.`,
      );
    }
  };

  return createHelp;
};

const useDeleteHelp = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id }: { id: string }) => {
      return await DeleteHelpApi(id);
    },
  });

  const deleteHelp = async (id: string, onDelete: () => void) => {
    try {
      const { msg, error } = await mutateAsync({ id });

      if (error === false) {
        message.success(`Help deleted successfully.`);
        onDelete();
      } else {
        message.error(msg || `Failed to delete the Help data. Please try again.`);
        onDelete();
      }
    } catch (err: any) {
      console.error({ err });

      message.error(
        err?.response?.data?.message || `An error occurred while deleting the Help data.`,
      );
    }
  };

  return { deleteHelp };
};

const useUpdateHelp = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id, updateData }: { id: string; updateData: UpdateHelpApiReqObj }) => {
      return await UpdateHelpApi(id, updateData);
    },
  });

  const UpdateHelp = async (id: string, updateData: UpdateHelpApiReqObj) => {
    try {
      const { msg, error } = await mutateAsync({ id, updateData });

      if (error === false) {
        message.success(`Help updated successfully.`);
        return true;
      } else {
        message.error(msg || `Failed to delete the Help data. Please try again.`);
        return false;
      }
    } catch (err: any) {
      console.error({ err });

      message.error(
        err?.response?.data?.message || `An error occurred while deleting the Help data.`,
      );
    }
  };

  return { UpdateHelp };
};

export { useUpdateHelp, useDeleteHelp, useCreateHelp };
