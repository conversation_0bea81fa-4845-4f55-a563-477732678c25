import { useMutation } from '@tanstack/react-query';
import { App } from 'antd';
import { useNavigate } from 'react-router-dom';

import {
  DeleteActivityMediaApi,
  CreatActivityMediaApi,
  UpdateActivityMediaApi,
} from '../services/activity-media/activityService';
import {
  CreatActivityMediaReqObj,
  UpdateActivityMediaApiReqObj,
} from '../services/activity-media/types';

const useCreateActivity = () => {
  const { message } = App.useApp();

  const navigate = useNavigate();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ data }: { data: CreatActivityMediaReqObj }) => CreatActivityMediaApi(data),
  });

  const createActivity = async (data: CreatActivityMediaReqObj) => {
    try {
      const { msg, error } = await mutateAsync({ data });
      if (error === false) {
        message.success(msg || 'Activity created succcessfully !!');
        navigate('/Activity_videos/list_Activity_videos');
      } else {
        message.error(msg || `Failed to create the Activity. Please try again.`);
      }
    } catch (error: any) {
      console.error({ error });
      message.error(
        error?.response?.data?.message || `An error occurred while creating the Activity.`,
      );
    }
  };

  return createActivity;
};

const useDeleteActivityVideo = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id }: { id: string }) => {
      return await DeleteActivityMediaApi(id);
    },
  });

  const deleteActivity = async (id: string, onDelete: () => void) => {
    try {
      const { msg, error } = await mutateAsync({ id });

      if (error === false) {
        message.success(`Activity deleted successfully.`);
        onDelete();
      } else {
        message.error(msg || `Failed to delete the Activity. Please try again.`);
        onDelete();
      }
    } catch (err: any) {
      console.error({ err });

      message.error(
        err?.response?.data?.message || `An error occurred while deleting the Activity.`,
      );
    }
  };

  return { deleteActivity };
};

const useUpdateActivityVideo = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({
      id,
      updateData,
    }: {
      id: string;
      updateData: UpdateActivityMediaApiReqObj;
    }) => {
      return await UpdateActivityMediaApi(id, updateData);
    },
  });

  const UpdateActivityVideo = async (id: string, updateData: UpdateActivityMediaApiReqObj) => {
    try {
      const { msg, error } = await mutateAsync({ id, updateData });

      if (error === false) {
        message.success(`Activity deleted successfully.`);
        return true;
      } else {
        message.error(msg || `Failed to delete the Activity. Please try again.`);
        return false;
      }
    } catch (err: any) {
      console.error({ err });

      message.error(
        err?.response?.data?.message || `An error occurred while deleting the Activity.`,
      );
    }
  };

  return { UpdateActivityVideo };
};

export { useUpdateActivityVideo, useDeleteActivityVideo, useCreateActivity };
