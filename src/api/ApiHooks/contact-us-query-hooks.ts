import { useMutation } from '@tanstack/react-query';
import { App } from 'antd';

import { UpdateContactApi } from '../services/contact-us-query/contactService';
import { UpdateContactQueryApiReqObj } from '../services/contact-us-query/types';

const useUpdateContactQuery = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({
      id,
      updateData,
    }: {
      id: string;
      updateData: UpdateContactQueryApiReqObj;
    }) => {
      return await UpdateContactApi(id, updateData);
    },
  });

  const UpdateContactQuery = async (id: string, updateData: UpdateContactQueryApiReqObj) => {
    try {
      const { msg, error } = await mutateAsync({ id, updateData });

      if (error === false) {
        message.success(`Query updated successfully.`);
        return true;
      } else {
        message.error(msg || `Failed to update the Query data. Please try again.`);
        return false;
      }
    } catch (err: any) {
      console.error({ err });

      message.error(
        err?.response?.data?.message || `An error occurred while deleting the Query data.`,
      );
    }
  };

  return { UpdateContactQuery };
};

export { useUpdateContactQuery };
