import { useMutation } from '@tanstack/react-query';
import { App } from 'antd';

import { DeleteFAQApi, CreatFAQApi, UpdateFAQApi } from '../services/faq/faqService';
import { CreatFAQReqObj, UpdateFAQApiReqObj } from '../services/faq/types';

const useCreateFAQ = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ data }: { data: CreatFAQReqObj }) => CreatFAQApi(data),
  });

  const createFAQ = async (data: CreatFAQReqObj, onSave: () => void) => {
    try {
      const { msg, error } = await mutateAsync({ data });
      if (error === false) {
        message.success(msg || 'FAQ created succcessfully !!');
        onSave();
      } else {
        message.error(msg || `Failed to create the FAQ data. Please try again.`);
      }
    } catch (error: any) {
      console.error({ error });
      message.error(
        error?.response?.data?.message || `An error occurred while creating the FAQ data.`,
      );
    }
  };

  return createFAQ;
};

const useDeleteFAQ = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id }: { id: string }) => {
      return await DeleteFAQApi(id);
    },
  });

  const deleteFAQ = async (id: string, onDelete: () => void) => {
    try {
      const { msg, error } = await mutateAsync({ id });

      if (error === false) {
        message.success(`FAQ deleted successfully.`);
        onDelete();
      } else {
        message.error(msg || `Failed to delete the FAQ data. Please try again.`);
        onDelete();
      }
    } catch (err: any) {
      console.error({ err });

      message.error(
        err?.response?.data?.message || `An error occurred while deleting the FAQ data.`,
      );
    }
  };

  return { deleteFAQ };
};

const useUpdateFAQ = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id, updateData }: { id: string; updateData: UpdateFAQApiReqObj }) => {
      return await UpdateFAQApi(id, updateData);
    },
  });

  const UpdateFAQ = async (id: string, updateData: UpdateFAQApiReqObj) => {
    try {
      const { msg, error } = await mutateAsync({ id, updateData });

      if (error === false) {
        message.success(`FAQ updated successfully.`);
        return true;
      } else {
        message.error(msg || `Failed to delete the FAQ data. Please try again.`);
        return false;
      }
    } catch (err: any) {
      console.error({ err });

      message.error(
        err?.response?.data?.message || `An error occurred while deleting the FAQ data.`,
      );
    }
  };

  return { UpdateFAQ };
};

export { useUpdateFAQ, useDeleteFAQ, useCreateFAQ };
