import { App } from 'antd';
import { useNavigate } from 'react-router-dom';

import { useUserActions } from '@/store/userStore';

import { GetAdminPermissionRoutesApi } from '../services/admin/adminService';
import { LogInApi } from '../services/auth/authService';
import { logInReqObj, LogInResObj } from '../services/auth/types';

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

export const useLogIn = () => {
  const navigate = useNavigate();
  const { message } = App.useApp();
  const { setUserToken, setUserInfo, setPermissions, setRole } = useUserActions();

  const signIn = async (data: logInReqObj) => {
    try {
      // Perform the sign-in request
      const res = await LogInApi(data);

      const { user, accessToken, refreshToken }: LogInResObj = res.data;

      setUserToken({ accessToken, refreshToken });
      setUserInfo(user);
      setRole(user?.role || '');

      const AdminPermissionsRes = await GetAdminPermissionRoutesApi();

      if (AdminPermissionsRes.data.error === false && AdminPermissionsRes.data !== null) {
        setPermissions(AdminPermissionsRes.data.permissions);
        navigate(HOMEPAGE, { replace: true });
      }
    } catch (err: any) {
      message.warning({
        content: err?.response?.data?.message || 'Something went wrong, Please try again later',
        duration: 3,
      });
    }
  };

  return signIn;
};
