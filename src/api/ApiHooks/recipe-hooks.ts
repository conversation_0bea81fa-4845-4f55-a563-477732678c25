import { useMutation } from '@tanstack/react-query';
import { App } from 'antd';
import { useNavigate } from 'react-router-dom';

import {
  DeleteRecipeApi,
  CreatRecipeApi,
  UpdateRecipeApi,
  AddBulkRecipesApi,
} from '../services/recipe/recipeService';

const useAddBulkRecipes = () => {
  const { message } = App.useApp();

  const navigate = useNavigate();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ data }: { data: FormData }) => AddBulkRecipesApi(data),
  });

  const addBulkRecipes = async (data: FormData) => {
    try {
      const { msg, error } = await mutateAsync({ data });
      if (error === false) {
        message.success(msg || 'CSV processed succcessfully !!');
        navigate('/recipe/list_Recipe');
      } else {
        message.error(msg || `Failed to create the Recipes. Please try again.`);
      }
    } catch (error: any) {
      console.error({ error });
      message.error(
        error?.response?.data?.message || `An error occurred while creating the Recipe.`,
      );
    }
  };

  return addBulkRecipes;
};

const useCreateRecipe = () => {
  const { message } = App.useApp();

  const navigate = useNavigate();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ data }: { data: FormData }) => CreatRecipeApi(data),
  });

  const createRecipe = async (data: FormData) => {
    try {
      const { msg, error } = await mutateAsync({ data });
      if (error === false) {
        message.success(msg || 'Recipe created succcessfully !!');
        navigate('/recipe/list_Recipe');
      } else {
        message.error(msg || `Failed to create the Recipe data. Please try again.`);
      }
    } catch (error: any) {
      console.error({ error });
      message.error(
        error?.response?.data?.message || `An error occurred while creating the Recipe data.`,
      );
    }
  };

  return createRecipe;
};

const useDeleteRecipe = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id }: { id: string }) => {
      return await DeleteRecipeApi(id);
    },
  });

  const deleteRecipe = async (id: string, onDelete: () => void) => {
    try {
      const { msg, error } = await mutateAsync({ id });

      if (error === false) {
        message.success(`Recipe deleted successfully.`);
        onDelete();
      } else {
        message.error(msg || `Failed to delete the Recipe data. Please try again.`);
        onDelete();
      }
    } catch (err: any) {
      console.error({ err });

      message.error(
        err?.response?.data?.message || `An error occurred while deleting the Recipe data.`,
      );
    }
  };

  return { deleteRecipe };
};

const useUpdateRecipe = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id, updateData }: { id: string; updateData: FormData }) => {
      return await UpdateRecipeApi(id, updateData);
    },
  });

  const UpdateRecipe = async (id: string, updateData: FormData) => {
    try {
      const { error, msg, Recipe } = await mutateAsync({ id, updateData });

      if (error === false) {
        if (Recipe === null) {
          message.warning(msg || `Something went wrong, Please try again.`);
          return false;
        } else {
          message.success(msg || `Recipe updated successfully.`);
          return true;
        }
      } else {
        message.error(`Something went wrong, Please try again.`);
        return false;
      }
    } catch (err: any) {
      console.error({ err });

      message.error(err?.response?.data?.message || `An error occurred while updating the recipe.`);
    }
  };

  return { UpdateRecipe };
};

export { useUpdateRecipe, useDeleteRecipe, useCreateRecipe, useAddBulkRecipes };
