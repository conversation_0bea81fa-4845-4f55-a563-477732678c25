import { useMutation } from '@tanstack/react-query';
import { App } from 'antd';

import { DeleteUserApi } from '../services/user/userService';

const useDeleteUser = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: ({ userId }: { userId: string }) => DeleteUserApi(userId),
  });

  const errMsg = `An error occurred during user delete, Please try again later`;

  const hit = async (userId: string, onDelete: () => void, onError: () => void) => {
    try {
      const { error, msg } = await mutateAsync({
        userId,
      });

      if (error === false) {
        message.success(msg);
        onDelete();
      } else {
        message.warning({
          content: msg || errMsg,
          duration: 3,
        });
        onError();
      }
    } catch (err) {
      message.warning({
        content: err?.response?.data?.message || errMsg,
        duration: 3,
      });
      onError();
    }
  };

  return hit;
};

export { useDeleteUser };
