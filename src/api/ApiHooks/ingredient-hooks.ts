import { useMutation } from '@tanstack/react-query';
import { App } from 'antd';
import { useNavigate } from 'react-router-dom';

import {
  CreateIngredientApi,
  UpdateIngredientApi,
  DeleteIngredient<PERSON>pi,
  AddBulkIngredientsApi,
} from '../services/ingredient/ingredientService';

const useAddBulkIngredients = () => {
  const { message } = App.useApp();
  const navigate = useNavigate();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ data }: { data: FormData }) => AddBulkIngredientsApi(data),
  });

  const addBulkIngredients = async (data: FormData) => {
    try {
      const response = await mutateAsync({ data });
      // Defensive: handle both { error } and no error field
      const isSuccess = response?.error === false || !('error' in response);
      if (isSuccess) {
        message.success(response?.msg || 'Ingredients processed successfully!');
        navigate('/ingredient/list_Ingredient');
      } else {
        message.error(response?.msg || 'Failed to create the Ingredients. Please try again.');
      }
    } catch (error: any) {
      console.error({ error });
      message.error(
        error?.response?.data?.message || 'An error occurred while creating the Ingredient(s).',
      );
    }
  };

  return addBulkIngredients;
};

const useCreateIngredient = () => {
  const { message } = App.useApp();
  const navigate = useNavigate();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ data }: { data: FormData }) => CreateIngredientApi(data as any),
  });

  const createIngredient = async (data: FormData) => {
    try {
      const { msg, error } = await mutateAsync({ data });
      if (error === false) {
        message.success(msg || 'Ingredient created successfully !!');
        navigate('/ingredient/list_Ingredient');
      } else {
        message.error(msg || `Failed to create the Ingredient. Please try again.`);
      }
    } catch (error: any) {
      console.error({ error });
      message.error(
        error?.response?.data?.message || `An error occurred while creating the Ingredient.`,
      );
    }
  };

  return createIngredient;
};

const useDeleteIngredient = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id }: { id: string }) => await DeleteIngredientApi(id),
  });

  const deleteIngredient = async (id: string, onDelete: () => void) => {
    try {
      const { msg, error } = await mutateAsync({ id });

      if (error === false) {
        message.success(`Ingredient deleted successfully.`);
        onDelete();
      } else {
        message.error(msg || `Failed to delete the Ingredient. Please try again.`);
        onDelete();
      }
    } catch (err: any) {
      console.error({ err });
      message.error(
        err?.response?.data?.message || `An error occurred while deleting the Ingredient.`,
      );
    }
  };

  return { deleteIngredient };
};

const useUpdateIngredient = () => {
  const { message } = App.useApp();

  const { mutateAsync } = useMutation({
    mutationFn: async ({ id, updateData }: { id: string; updateData: FormData }) =>
      await UpdateIngredientApi(id, updateData as any),
  });

  const updateIngredient = async (id: string, updateData: FormData) => {
    try {
      const { error } = await mutateAsync({ id, updateData });

      if (error === false) {
        return true;
      } else {
        return false;
      }
    } catch (err: any) {
      console.error({ err });
      message.error(
        err?.response?.data?.message || `An error occurred while updating the ingredient.`,
      );
    }
  };

  return { updateIngredient };
};

export { useUpdateIngredient, useDeleteIngredient, useCreateIngredient, useAddBulkIngredients };
