import apiClient from '../axios/axiosInstance';
import {
  CreateHelpApiResObj,
  CreateHelpReqObj,
  DeleteHelpApiResObj,
  GetAllHelpApiResObj,
  GetSingleHelpApiResObj,
  UpdateHelpApiReqObj,
  UpdateHelpApiResObj,
} from './types';

const ApiObj = {
  API_BASE: `/admin/help/`,
} as const;

export const GetAllHelpApi = async (
  page: number = 1,
  search: string = '',
): Promise<GetAllHelpApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `?${page}&title=${search}&description=${search}`)).data;
};

export const CreatHelpApi = async (data: CreateHelpReqObj): Promise<CreateHelpApiResObj> => {
  return (await apiClient.post(ApiObj.API_BASE, data)).data;
};

export const GetSingleHelpApi = async (id: string): Promise<GetSingleHelpApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `${id}`)).data;
};

export const UpdateHelpApi = async (
  Help_id: string,
  updateData: UpdateHelpApiReqObj,
): Promise<UpdateHelpApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `${Help_id}?`, updateData)).data;
};

export const DeleteHelpApi = async (Help_id: string): Promise<DeleteHelpApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `delete/${Help_id}`, {})).data;
};
