import { ApiBaseResponse } from '#/api';
import { HelpData, HelpTopic } from '#/help';

export interface CreateHelpReqObj {
  category: string;

  topics: HelpTopic[];
}

export interface UpdateHelpApiReqObj {
  category?: string;

  topics?: HelpTopic[];
  isDeleted?: boolean;
}

export interface GetAllHelpApiResObj extends ApiBaseResponse {
  total?: number;
  data?: HelpData[];
}

export interface CreateHelpApiResObj extends ApiBaseResponse {}

export interface GetSingleHelpApiResObj extends ApiBaseResponse {
  data: HelpData;
}

export interface UpdateHelpApiResObj extends ApiBaseResponse {}

export interface DeleteHelpApiResObj extends ApiBaseResponse {}

export interface FaqData {
  id: string;
  question: string;
  answer: string;
}
