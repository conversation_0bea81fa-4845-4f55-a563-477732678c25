import { AccessHRdashboardResObj, ExitHRdashboardResObj } from './types';
import apiClient from '../axios/axiosInstance';

const ApiObj = {
  AccessHRdashboard: `/admin/permissions/access`,
  ExitHRdashboard: `/admin/permissions/exit`,
  getAdminPermissionRoutes: `/admin/permissions`,
} as const;

export const AccessHRdashboardApi = async (id: string): Promise<AccessHRdashboardResObj> => {
  return (await apiClient.put(ApiObj.AccessHRdashboard + `/${id}`, {})).data;
};

export const ExitHRdashboardApi = async (): Promise<ExitHRdashboardResObj> => {
  return (await apiClient.put(ApiObj.ExitHRdashboard, {})).data;
};

export const GetAdminPermissionRoutesApi = async () => {
  return await apiClient.get(ApiObj.getAdminPermissionRoutes);
};
