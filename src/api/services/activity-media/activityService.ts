import {
  CreatActivityMediaApiResObj,
  CreatActivityMediaReqObj,
  DeleteActivityMediaApiResObj,
  GetAllActivityMediaApiResObj,
  GetAllActivityMediaSubtagsApiResObj,
  GetSingleActivityMediaApiResObj,
  UpdateActivityMediaApiReqObj,
  UpdateActivityMediaApiResObj,
} from './types';
import apiClient from '../axios/axiosInstance';

const ApiObj = {
  API_BASE: `/admin/activity_videos/`,
  SUBTAGS_API: `/admin/activity_videos_subtags`,
} as const;

export const GetAllActivityMediaSubTagsApi = async (
  tag: string,
): Promise<GetAllActivityMediaSubtagsApiResObj> => {
  return (await apiClient.get(ApiObj.SUBTAGS_API + `?tag=${tag}`)).data;
};

export const GetAllActivityMediaApi = async (
  page: number = 1,
  title: string = '',
  tag: string = '',
): Promise<GetAllActivityMediaApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `?page=${page}&title=${title}&tag=${tag}`)).data;
};

export const CreatActivityMediaApi = async (
  data: CreatActivityMediaReqObj,
): Promise<CreatActivityMediaApiResObj> => {
  return (await apiClient.post(ApiObj.API_BASE, data)).data;
};

export const GetSingleActivityApi = async (
  id: string,
): Promise<GetSingleActivityMediaApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `${id}`)).data;
};

export const UpdateActivityMediaApi = async (
  Activity_id: string,
  updateData: UpdateActivityMediaApiReqObj,
): Promise<UpdateActivityMediaApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `${Activity_id}`, updateData)).data;
};

export const DeleteActivityMediaApi = async (
  Activity_id: string,
): Promise<DeleteActivityMediaApiResObj> => {
  return (await apiClient.delete(ApiObj.API_BASE + `${Activity_id}`)).data;
};
