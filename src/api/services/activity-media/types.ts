import { ActivityVideoData } from '#/activityVideo';
import { ApiBaseResponse } from '#/api';

export interface CreatActivityMediaReqObj {
  title: string;
  description: string;
  completionTime: number;
  tag: string;
  subTags: string[];
  calorieEstimate: number;
  videoUrl: string;
  isPublished: boolean;
}

export interface UpdateActivityMediaApiReqObj {
  title?: string;
  description?: string;
  completionTime?: number;
  subTags?: string[];
  calorieEstimate?: number;
  isPublished?: boolean;
}

export interface GetAllActivityMediaSubtagsApiResObj extends ApiBaseResponse {
  tag: string;
  subTags: string[];
}
export interface GetAllActivityMediaApiResObj extends ApiBaseResponse {
  total?: number;
  ActivityMedias?: ActivityVideoData[];
}
export interface CreatActivityMediaApiResObj extends ApiBaseResponse {}
export interface GetSingleActivityMediaApiResObj extends ApiBaseResponse {
  ActivityMedia: ActivityVideoData;
}
export interface UpdateActivityMediaApiResObj extends ApiBaseResponse {}
export interface DeleteActivityMediaApiResObj extends ApiBaseResponse {}
