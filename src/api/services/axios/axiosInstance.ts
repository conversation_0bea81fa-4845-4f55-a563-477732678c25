import axios from 'axios';
import TokenRefreshManager from './refreshTokenManager';
const tokenManager = TokenRefreshManager.getInstance();
const { VITE_BACKEND_BASE_URL } = import.meta.env;

export const BASE_URL = VITE_BACKEND_BASE_URL;

const apiClient = axios.create({
  baseURL: VITE_BACKEND_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
});

apiClient.interceptors.request.use(
  (config) => {
    const tokens = JSON.parse(localStorage.getItem('token') || '{}');
    const accessToken = tokens?.accessToken;

    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => Promise.reject(error),
);

apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    const tokens = JSON.parse(localStorage.getItem('token') || '{}');
    const accessToken = tokens?.accessToken;
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      accessToken &&
      error.response?.data?.message ===
        'Authentication token is missing or invalid. Please log in again.'
    ) {
      originalRequest._retry = true;

      try {
        const newToken = await tokenManager.refreshToken();
        originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  },
);

export default apiClient;
