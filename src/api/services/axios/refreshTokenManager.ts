import { RefreshTokenApi } from '../auth/authService';
import apiClient from './axiosInstance';
import { eventBus } from './eventBus';

class TokenRefreshManager {
  private static instance: TokenRefreshManager | null = null; // ✅ declare it
  private isRefreshing = false;
  private failedQueue: { resolve: (token: string) => void; reject: (err: any) => void }[] = [];

  private constructor() {} // make constructor private to enforce singleton

  static getInstance() {
    if (!TokenRefreshManager.instance) {
      TokenRefreshManager.instance = new TokenRefreshManager();
    }
    return TokenRefreshManager.instance;
  }

  private processQueue(error: any, token: string | null = null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token!);
      }
    });
    this.failedQueue = [];
  }

  async refreshToken() {
    if (this.isRefreshing) {
      return new Promise<string>((resolve, reject) => {
        this.failedQueue.push({ resolve, reject });
      });
    }

    this.isRefreshing = true;

    try {
      const tokens = JSON.parse(localStorage.getItem('token') || '{}');
      const refreshToken = tokens?.refreshToken;

      const refreshResponse = await RefreshTokenApi(refreshToken);
      const newAccessToken = refreshResponse.data.accessToken;

      const updatedUserData = { ...tokens, accessToken: newAccessToken };
      localStorage.setItem('token', JSON.stringify(updatedUserData));

      this.processQueue(null, newAccessToken);
      return newAccessToken;
    } catch (error) {
      console.log('Token refresh failed - clearing user data');
      this.processQueue(error, null);

      Object.keys(apiClient.defaults.headers.common).forEach((key) => {
        delete apiClient.defaults.headers.common[key];
      });

      eventBus.emit('logout');

      throw error;
    } finally {
      this.isRefreshing = false;
    }
  }

  reset() {
    this.isRefreshing = false;
    this.failedQueue = [];
  }

  static clearInstance() {
    TokenRefreshManager.instance = null;
  }
}

export default TokenRefreshManager;
