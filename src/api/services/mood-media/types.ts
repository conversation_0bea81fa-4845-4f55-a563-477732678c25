import { ApiBaseResponse } from '#/api';
import { MoodVideoData } from '#/moodVideo';

export interface CreatMoodMediaReqObj {
  title: string;
  description: string;
  hungerLevel: string;
  moodType: string;
  videoUrl: string;
  isPublished: boolean;
}

export interface UpdateMoodMediaApiReqObj {
  title?: string;
  description?: string;
  isPublished?: boolean;
}

export interface GetAllMoodMediaApiResObj extends ApiBaseResponse {
  total?: number;
  moodMedias?: MoodVideoData[];
}
export interface CreatMoodMediaApiResObj extends ApiBaseResponse {}
export interface GetSingleMoodMediaApiResObj extends ApiBaseResponse {
  moodMedia: MoodVideoData;
}
export interface UpdateMoodMediaApiResObj extends ApiBaseResponse {}
export interface DeleteMoodMediaApiResObj extends ApiBaseResponse {}
