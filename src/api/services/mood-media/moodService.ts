import apiClient from '../axios/axiosInstance';
import {
  CreatMoodMediaApiResObj,
  CreatMoodMediaReqObj,
  DeleteMoodMediaApiResObj,
  GetAllMoodMediaApiResObj,
  GetSingleMoodMediaApiResObj,
  UpdateMoodMediaApiReqObj,
  UpdateMoodMediaApiResObj,
} from './types';

const ApiObj = {
  API_BASE: `/admin/mood_videos/`,
} as const;

export const GetAllMoodMediaApi = async (
  page: number = 1,
  title: string = '',
  moodType: string = '',
  hungerLevel: string = '',
): Promise<GetAllMoodMediaApiResObj> => {
  return (
    await apiClient.get(
      ApiObj.API_BASE +
        `?page=${page}&title=${title}&moodType=${moodType}&hungerLevel=${hungerLevel}`,
    )
  ).data;
};

export const CreatMoodMediaApi = async (
  data: CreatMoodMediaReqObj,
): Promise<CreatMoodMediaApiResObj> => {
  return (await apiClient.post(ApiObj.API_BASE, data)).data;
};

export const GetSingleMoodApi = async (id: string): Promise<GetSingleMoodMediaApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `${id}`)).data;
};

export const UpdateMoodMediaApi = async (
  Mood_id: string,
  updateData: UpdateMoodMediaApiReqObj,
): Promise<UpdateMoodMediaApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `${Mood_id}`, updateData)).data;
};

export const DeleteMoodMediaApi = async (Mood_id: string): Promise<DeleteMoodMediaApiResObj> => {
  return (await apiClient.delete(ApiObj.API_BASE + `${Mood_id}`)).data;
};
