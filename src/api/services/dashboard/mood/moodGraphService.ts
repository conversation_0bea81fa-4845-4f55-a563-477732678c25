import { MoodDistributionData } from '#/moodAnalytics';
import apiClient from '../../axios/axiosInstance';

const MOOD_ENDPOINT = `/admin/analytics/mood`;

export const GetMoodAnalyticsTrend = async (
  filter: 'daily' | 'weekly' | 'monthly',
): Promise<MoodDistributionData> => {
  const res = await apiClient.get<MoodDistributionData>(`${MOOD_ENDPOINT}`, {
    params: { filter },
  });

  return res.data;
};
