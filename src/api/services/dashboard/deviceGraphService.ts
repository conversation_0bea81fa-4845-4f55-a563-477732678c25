import apiClient from '../axios/axiosInstance';

const DEVICE_ENDPOINT = `/admin/analytics/device`;

type DeviceTrend = {
  label: string;
  averageDeviceUsage: number;
};

export const GetAverageDeviceUsage = async (
  period: 'weekly' | 'monthly',
): Promise<DeviceTrend[]> => {
  const res = await apiClient.get<{
    data: DeviceTrend[];
  }>(`${DEVICE_ENDPOINT}/average-device-usage/${period}`);

  return res.data.data;
};

type DeviceUsageGoal = {
  goalAchieved: number;
  totalUsers: number;
};

export const GetDeviceUsageGoalAchieved = async (
  period: '24h' | '7d' | '30d',
): Promise<DeviceUsageGoal> => {
  const res = await apiClient.get<{
    data: DeviceUsageGoal;
  }>(`${DEVICE_ENDPOINT}/device-usage-goal-achieved/${period}`);

  return res.data.data;
};
