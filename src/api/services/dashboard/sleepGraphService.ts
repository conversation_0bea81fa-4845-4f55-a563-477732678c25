import apiClient from '../axios/axiosInstance';

const SLEEP_ENDPOINT = `/admin/analytics/sleep`;

type SleepTrend = {
  label: string;
  averageSleep: number;
};

export const GetUserSleepTrends = async (period: 'weekly' | 'monthly'): Promise<SleepTrend[]> => {
  const res = await apiClient.get<{
    data: SleepTrend[];
  }>(`${SLEEP_ENDPOINT}/trends/${period}`);

  return res.data.data;
};

export type SleepGoalAchieved = {
  goalAchieved: number;
  totalUsers: number;
};

export const GetSleepGoalAchieved = async (
  period: '24h' | '7d' | '30d',
): Promise<SleepGoalAchieved> => {
  const res = await apiClient.get<{
    data: SleepGoalAchieved;
  }>(`${SLEEP_ENDPOINT}/goals_achieved/${period}`);

  return res.data.data;
};

export const GetRecommendedSleepAchieved = async (
  period: '24h' | '7d' | '30d',
): Promise<SleepGoalAchieved> => {
  const res = await apiClient.get<{
    data: SleepGoalAchieved;
  }>(`${SLEEP_ENDPOINT}/recommended_sleep_achieved/${period}`);

  return res.data.data;
};
