import apiClient from '../axios/axiosInstance';

const NUTRITION_ENDPOINT = `/admin/analytics`;

export type NutritionTrend = {
  period: string;
  noOfUser: number;
};

export const GetNutritionTrend = async (
  period: 'weekly' | 'monthly',
): Promise<NutritionTrend[]> => {
  const res = await apiClient.get<{
    data: NutritionTrend[];
  }>(`${NUTRITION_ENDPOINT}/nutrition?filter=${period}`);

  return res.data.data;
};

type MealTypeLoggedTrend = {
  period: string;
  mealTypeUserCounts: {
    ['Afternoon Snack']: number;
    ['Breakfast']: number;
    ['Lunch']: number;
    ['Dinner']: number;
    ['Late Night Snack']: number;
    ['Mid Morning Snack']: number;
  };
};

export const GetMealTypeLoggedTrend = async (
  period: 'weekly' | 'monthly',
): Promise<MealTypeLoggedTrend[]> => {
  const res = await apiClient.get<{
    data: MealTypeLoggedTrend[];
  }>(`${NUTRITION_ENDPOINT}/mealType?filter=${period}`);

  return res.data.data;
};

type PopularMeals = {
  recipeId: string;
  recipeName: string;
  count: number;
};

type Macronutrition = {
  period: string;
  calories: number;
  protein: number;
  carbs: number;
  fats: number;
  fiber: number;
};

export const GetPopularMeals = async (period: 'weekly' | 'monthly'): Promise<PopularMeals[]> => {
  const res = await apiClient.get<{
    data: PopularMeals[];
  }>(`${NUTRITION_ENDPOINT}/popularMeal?filter=${period}`);

  return res.data.data;
};

export const NutrientsTrends = async (period: 'weekly' | 'monthly') => {
  const res = await apiClient.get<{
    data: Macronutrition[];
  }>(`${NUTRITION_ENDPOINT}/macronutrition?filter=${period}`);

  return res.data;
};
