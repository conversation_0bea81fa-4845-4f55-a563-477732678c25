import apiClient from '../axios/axiosInstance';

const ApiObj = {
  GET_USER_AGE_ANALYTICS: `/admin/analytics/user_age_groups`,
} as const;

export const GetUserAgeAnalytics = async (
  period: '24h' | '7d' | '30d',
  dataType: 'activeUsers' | 'registeredUsers',
) => {
  const res = await apiClient.get(`${ApiObj.GET_USER_AGE_ANALYTICS}/${period}`);

  const data = res.data;

  const ageGroups = ['child', 'teen', 'youngAdult', 'adult', 'senior'];

  const labels = ageGroups.map((group) =>
    group.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase()),
  );

  const series = ageGroups.map((group) => data[group]?.[dataType] || 0);

  return ageGroups.map((_, index) => ({
    name: labels[index],
    value: series[index],
  }));
};
