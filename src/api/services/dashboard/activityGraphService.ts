import apiClient from '../axios/axiosInstance';

const ACTIVITY_ENDPOINT = `/admin/analytics`;

type ActivityTrend = {
  label: string;
  averageCaloriesBurned: number;
};

export const GetCaloriesBurnedTrends = async (
  period: 'weekly' | 'monthly',
): Promise<ActivityTrend[]> => {
  const res = await apiClient.get<{
    data: ActivityTrend[];
  }>(`${ACTIVITY_ENDPOINT}/burned_calories?filter=${period}`);

  return res.data.data;
};

type PopularActivities = {
  activityId: string;
  activityName: string;
  count: number;
};

export const GetPopularActivities = async (
  period: 'weekly' | 'monthly',
): Promise<PopularActivities[]> => {
  const res = await apiClient.get<{
    data: PopularActivities[];
  }>(`${ACTIVITY_ENDPOINT}/popular_activities?filter=${period}`);

  return res.data.data;
};
