import apiClient from '../axios/axiosInstance';

const ApiObj = {
  GET_ACTIVELY_USING_DEVICE: `/admin/analytics/device_usage_stats`,
} as const;

export const GetActiveUsersApi = async (period: '24h' | '7d' | '30d') => {
  const res = await apiClient.get(`${ApiObj.GET_ACTIVELY_USING_DEVICE}/${period}`);
  const { activelyUsingDevice, totalConnectedUsers } = res.data;

  return [
    {
      name: 'Actively Using Device',
      value: activelyUsingDevice,
    },
    {
      name: 'Not Actively Using Device',
      value: totalConnectedUsers - activelyUsingDevice,
    },
  ];
};
