import apiClient from '../axios/axiosInstance';

const WEIGHT_ENDPOINT = `/admin/analytics/weight`;

export type WeightStatusSummary = {
  maintaining: number;
  gaining: number;
  losing: number;
};

export const GetWeightRecordSummary = async (
  period: '7d' | '30d',
): Promise<WeightStatusSummary> => {
  const res = await apiClient.get<{
    data: WeightStatusSummary;
  }>(`${WEIGHT_ENDPOINT}/weight-status-summary/${period}`);

  return res.data.data;
};
