import { ApiBaseResponse } from '#/api';
import {
  DIET_PREFERENCE,
  MEAL_TYPES,
  SERVING_NUTRITION_QUANTITY,
  SLICE_NUTRITION_QUANTITY,
  PIECE_NUTRITION_QUANTITY,
} from '#/enum';
import { RecipeData } from '#/recipes';

export interface nutritionByQuantityInterface {
  quantity: SERVING_NUTRITION_QUANTITY | SLICE_NUTRITION_QUANTITY | PIECE_NUTRITION_QUANTITY;
  protein: number;
  calories: number;
  fats: number;
  carbs: number;
  fiber: number;
}

export interface CreatRecipeReqObj {
  title: string;
  ingredients: string[];
  directions: string;
  mealType: string;
  category: string;
  timeToPrep: number; // always in minutes
  nutritionByQuantity: nutritionByQuantityInterface[];
  thumbnailFile: File | null;
  isPublished: boolean;
}

export interface UpdateRecipeApiReqObj {
  title?: string;
  ingredients?: string; // ingredients seprated by comman ,
  directions?: string;
  timeToPrep?: number; // always in minutes
  numOfServings?: number;
  thumbnailUrl?: string;
  totalCalories?: number;
  mealType?: MEAL_TYPES;
  category?: DIET_PREFERENCE;
  nutritionByQuantity?: string;
  isPublished?: boolean;
  thumbnailFile?: File;
}

export interface GetAllRecipeApiResObj extends ApiBaseResponse {
  total?: number;
  recipes?: RecipeData[];
}
export interface CreatRecipeApiResObj extends ApiBaseResponse {}
export interface GetSingleRecipeApiResObj extends ApiBaseResponse {
  recipe: RecipeData;
}
export interface UpdateRecipeApiResObj extends ApiBaseResponse {
  Recipe: null | RecipeData;
}
export interface DeleteRecipeApiResObj extends ApiBaseResponse {}
