import apiClient from '../axios/axiosInstance';
import {
  CreatRecipeApiResObj,
  DeleteRecipeApiResObj,
  GetAllRecipeApiResObj,
  GetSingleRecipeApiResObj,
  UpdateRecipeApiResObj,
} from './types';

const ApiObj = {
  API_BASE: `/admin/recipes/`,
} as const;

export const GetAllRecipeApi = async (
  page: number = 1,
  title: string = '',
  category: string = '',
  mealType: string = '',
  visibility: string = '',
): Promise<GetAllRecipeApiResObj> => {
  return (
    await apiClient.get(
      ApiObj.API_BASE +
        `?&page=${page}&title=${title}&category=${category}&mealType=${mealType}&visibility=${visibility}`,
    )
  ).data;
};

export const CreatRecipeApi = async (data: FormData): Promise<CreatRecipeApiResObj> => {
  return (await apiClient.post(ApiObj.API_BASE, data)).data;
};

export const AddBulkRecipesApi = async (data: FormData): Promise<CreatRecipeApiResObj> => {
  return (await apiClient.post(ApiObj.API_BASE + 'bulk', data)).data;
};

export const GetSingleRecipeApi = async (id: string): Promise<GetSingleRecipeApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `${id}`)).data;
};

export const UpdateRecipeApi = async (
  Recipe_id: string,
  updateData: FormData,
): Promise<UpdateRecipeApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `${Recipe_id}`, updateData)).data;
};

export const DeleteRecipeApi = async (Recipe_id: string): Promise<DeleteRecipeApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `delete/${Recipe_id}`, {})).data;
};
