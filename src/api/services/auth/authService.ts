import { logInReqObj } from './types';
import apiClient from '../axios/axiosInstance';

const ApiObj = {
  logIn: `/admin/login`,
  logOut: `/admin/logout`,
  getProfile: `/me`,
} as const;

export const LogInApi = async (data: logInReqObj) => {
  return await apiClient.post(ApiObj.logIn, data);
};

export const LogOutApi = async () => {
  return await apiClient.get(ApiObj.logOut);
};

export const GetProfileApi = async () => {
  return await apiClient.get(ApiObj.getProfile);
};

export const RefreshTokenApi = async (refreshToken: string) => {
  return await apiClient.put(`/token/refresh`, { refreshToken });
};
