import apiClient from '../axios/axiosInstance';
import {
  CreatFAQApiResObj,
  CreatFAQReqObj,
  DeleteFAQApiResObj,
  GetAllFAQApiResObj,
  GetSingleFAQApiResObj,
  UpdateFAQApiReqObj,
  UpdateFAQApiResObj,
} from './types';

const ApiObj = {
  API_BASE: `/admin/faq/`,
} as const;

export const GetAllFAQApi = async (
  page: number = 1,
  title: string = '',
): Promise<GetAllFAQApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `?page=${page}&question=${title}`)).data;
};

export const CreatFAQApi = async (data: CreatFAQReqObj): Promise<CreatFAQApiResObj> => {
  return (await apiClient.post(ApiObj.API_BASE, data)).data;
};

export const GetSingleFAQApi = async (id: string): Promise<GetSingleFAQApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `${id}`)).data;
};

export const UpdateFAQApi = async (
  FAQ_id: string,
  updateData: UpdateFAQApiReqObj,
): Promise<UpdateFAQApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `${FAQ_id}`, updateData)).data;
};

export const DeleteFAQApi = async (FAQ_id: string): Promise<DeleteFAQApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `delete/${FAQ_id}`, {})).data;
};
