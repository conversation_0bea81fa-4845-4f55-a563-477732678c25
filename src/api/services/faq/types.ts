import { ApiBaseResponse } from '#/api';
import { FaqData } from '#/faq';

export interface CreatFAQReqObj {
  question: string;
  answer: string;
}

export interface UpdateFAQApiReqObj {
  question?: string;
  answer?: string;
}

export interface GetAllFAQApiResObj extends ApiBaseResponse {
  total?: number;
  data?: FaqData[];
}
export interface CreatFAQApiResObj extends ApiBaseResponse {}
export interface GetSingleFAQApiResObj extends ApiBaseResponse {
  data?: FaqData;
}
export interface UpdateFAQApiResObj extends ApiBaseResponse {}
export interface DeleteFAQApiResObj extends ApiBaseResponse {}
