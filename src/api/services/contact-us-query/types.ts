import { ApiBaseResponse } from '#/api';
import { ContactQueryData } from '#/contactQuery';
import { QUERY_STATUS } from '#/enum';

export interface UpdateContactQueryApiReqObj {
  status: QUERY_STATUS; // its required in this api
}

export interface GetAllContactQueryApiResObj extends ApiBaseResponse {
  total?: number;
  queries?: ContactQueryData[];
}
export interface GetSingleContactQueryApiResObj extends ApiBaseResponse {
  query: ContactQueryData;
}

export interface UpdateContactQueryApiResObj extends ApiBaseResponse {}
