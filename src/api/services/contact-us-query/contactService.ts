import {
  GetAllContactQueryApiResObj,
  GetSingleContactQueryApiResObj,
  UpdateContactQueryApiReqObj,
  UpdateContactQueryApiResObj,
} from './types';
import apiClient from '../axios/axiosInstance';

const ApiObj = {
  API_BASE: `/admin/contact/`,
} as const;

export const GetAllContactApi = async (
  page: number = 1,
  email: string = '',
  status: string = '',
): Promise<GetAllContactQueryApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `?page=${page}&email=${email}&status=${status}`))
    .data;
};

export const GetSingleContactApi = async (id: string): Promise<GetSingleContactQueryApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `${id}`)).data;
};

export const UpdateContactApi = async (
  Contact_id: string,
  updateData: UpdateContactQueryApiReqObj,
): Promise<UpdateContactQueryApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `${Contact_id}`, updateData)).data;
};
