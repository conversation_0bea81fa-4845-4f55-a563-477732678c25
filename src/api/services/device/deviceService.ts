import {
  CreatDeviceApiResObj,
  DeleteDeviceApiResObj,
  GetAllDeviceApiResObj,
  GetSingleDeviceApiResObj,
  UpdateDeviceApiResObj,
} from './types';
import apiClient from '../axios/axiosInstance';

const ApiObj = {
  API_BASE: `/admin/devices/`,
} as const;

export const GetAllDeviceApi = async (
  page: number = 1,
  name: string = '',
): Promise<GetAllDeviceApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `?&page=${page}&name=${name}`)).data;
};

export const CreatDeviceApi = async (data: FormData): Promise<CreatDeviceApiResObj> => {
  return (await apiClient.post(ApiObj.API_BASE, data)).data;
};

export const AddBulkDevicesApi = async (data: FormData): Promise<CreatDeviceApiResObj> => {
  return (await apiClient.post(ApiObj.API_BASE + 'bulk', data)).data;
};

export const GetSingleDeviceApi = async (id: string): Promise<GetSingleDeviceApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `${id}`)).data;
};

export const UpdateDeviceApi = async (
  Device_id: string,
  updateData: FormData,
): Promise<UpdateDeviceApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `${Device_id}`, updateData)).data;
};

export const DeleteDeviceApi = async (Device_id: string): Promise<DeleteDeviceApiResObj> => {
  return (await apiClient.delete(ApiObj.API_BASE + `${Device_id}`)).data;
};
