import { ApiBaseResponse } from '#/api';
import { DeviceData } from '#/device';
import {
  DIET_PREFERENCE,
  MEAL_TYPES,
  SERVING_NUTRITION_QUANTITY,
  SLICE_NUTRITION_QUANTITY,
  PIECE_NUTRITION_QUANTITY,
} from '#/enum';

export interface nutritionByQuantityInterface {
  quantity: SERVING_NUTRITION_QUANTITY | SLICE_NUTRITION_QUANTITY | PIECE_NUTRITION_QUANTITY;
  protein: number;
  fats: number;
  carbs: number;
  fiber: number;
}

export interface CreatDeviceReqObj {
  name: string;
  description: string;
  deviceGuide: string;
  version: number;
  type: string;
  thumbnailFile: null | File;
  serialIds: string[];
}

export interface UpdateDeviceApiReqObj {
  title?: string;
  ingredients?: string; // ingredients seprated by comman ,
  directions?: string;
  timeToPrep?: number; // always in minutes
  numOfServings?: number;
  thumbnailUrl?: string;
  totalCalories?: number;
  mealType?: MEAL_TYPES;
  category?: DIET_PREFERENCE;
  nutritionByQuantity?: string;
  isPublished?: boolean;
  thumbnailFile?: File;
}

export interface GetAllDeviceApiResObj extends ApiBaseResponse {
  total?: number;
  devices?: DeviceData[];
}
export interface CreatDeviceApiResObj extends ApiBaseResponse {}
export interface GetSingleDeviceApiResObj extends ApiBaseResponse {
  device: DeviceData;
}
export interface UpdateDeviceApiResObj extends ApiBaseResponse {}
export interface DeleteDeviceApiResObj extends ApiBaseResponse {}
