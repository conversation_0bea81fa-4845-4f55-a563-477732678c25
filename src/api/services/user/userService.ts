import { getAPIConfig } from '../api-utils';
import apiClient from '../axios/axiosInstance';

const ApiObj = {
  GET_ALL_USERS: `/admin/users`,
  GET_SINGLE_USER: `/admin/users`,
  DELETE_USER: `/admin/users`,
} as const;

export const GetAllUsersApi = async (
  page: number = 1,
  email: string = '',
  isDeleted: string = '',
) => {
  const otpAccessToken = localStorage.getItem('otpAccessToken');
  return (
    await apiClient.get(
      ApiObj.GET_ALL_USERS + `?page=${page}&email=${email}&isDeleted=${isDeleted}`,
      getAPIConfig(otpAccessToken),
    )
  ).data;
};

export const GetSingleUserApi = async (userId: string) => {
  const otpAccessToken = localStorage.getItem('otpAccessToken');
  return (await apiClient.get(`${ApiObj.GET_SINGLE_USER}/${userId}`, getAPIConfig(otpAccessToken)))
    .data;
};

export const DeleteUserApi = async (userId: string) => {
  const otpAccessToken = localStorage.getItem('otpAccessToken');
  return (await apiClient.delete(`${ApiObj.DELETE_USER}/${userId}`, getAPIConfig(otpAccessToken)))
    .data;
};
