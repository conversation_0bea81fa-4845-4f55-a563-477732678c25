import { ApiBaseResponse } from '#/api';

export interface addSingleUserReqObj {
  firstName: string;
  lastName: string;
  email: string;
  designation: string;
  departmentId: string;
}

export interface addBulkUserReqObj {
  csvFile: File;
}

export interface updateUserStatusReqObj {
  userId: number;
  isActive: boolean;
}
export interface updateUserRoleReqObj {
  isRoleUpdate: boolean;
}
export interface updateUserReqObj {
  isActive: boolean;
  assignHrRole: boolean;
}

// -----------------------------------------
export interface addSingleUserResObj extends ApiBaseResponse {}
export interface addBulkUserResObj extends ApiBaseResponse {}
export interface updateUserResObj extends ApiBaseResponse {}
export interface deleteUserResObj extends ApiBaseResponse {}
