import apiClient from '../axios/axiosInstance';
import {
  CreateIngredientApiResObj,
  CreateIngredientReqObj,
  CreatIngredientApiResObj,
  DeleteIngredientApiResObj,
  GetAllIngredientApiResObj,
  GetSingleIngredientApiResObj,
  UpdateIngredientApiReqObj,
  UpdateIngredientApiResObj,
} from './types';

const ApiObj = {
  API_BASE: `/admin/ingredients/`,
} as const;

export const GetAllIngredientApi = async (
  page: number = 1,
  name: string = '',
): Promise<GetAllIngredientApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `?page=${page}&name=${encodeURIComponent(name)}`))
    .data;
};

export const CreateIngredientApi = async (
  data: CreateIngredientReqObj,
): Promise<CreateIngredientApiResObj> => {
  return (await apiClient.post(ApiObj.API_BASE, data)).data;
};

export const AddBulkIngredientsApi = async (data: FormData): Promise<CreatIngredientApiResObj> => {
  return (await apiClient.post(ApiObj.API_BASE + 'bulk', data)).data;
};

export const GetSingleIngredientApi = async (id: string): Promise<GetSingleIngredientApiResObj> => {
  return (await apiClient.get(ApiObj.API_BASE + `${id}`)).data;
};

export const UpdateIngredientApi = async (
  ingredientId: string,
  updateData: UpdateIngredientApiReqObj,
): Promise<UpdateIngredientApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `${ingredientId}`, updateData)).data;
};

export const DeleteIngredientApi = async (
  ingredientId: string,
): Promise<DeleteIngredientApiResObj> => {
  return (await apiClient.put(ApiObj.API_BASE + `delete/${ingredientId}`, {})).data;
};
