import { ApiBaseResponse } from '#/api';
import { PIECE_QUANTITY_TYPE, STANDARD_QUANTITY_TYPE } from '#/enum';
import { IngredientData } from '#/ingredient'; // make sure this interface exists

// Nutrition info structure for ingredient based on quantity type
export interface ingredientNutritionByQuantityInterface {
  quantity: STANDARD_QUANTITY_TYPE | PIECE_QUANTITY_TYPE;
  protein: number;
  calories: number;
  fats: number;
  carbs: number;
  fiber: number;
}

// Request payload for creating an ingredient
export interface CreateIngredientReqObj {
  name: string;
  ingredientNutritionByQuantity: ingredientNutritionByQuantityInterface[];
  thumbnailFile: File | null;
}

// Request payload for updating an ingredient
export interface UpdateIngredientApiReqObj {
  name?: string;
  ingredientNutritionByQuantity?: ingredientNutritionByQuantityInterface[];
  thumbnailFile?: File | null;
}

// Response for fetching all ingredients
export interface GetAllIngredientApiResObj extends ApiBaseResponse {
  total?: number;
  data?: IngredientData[];
}

// Response after creating an ingredient
export interface CreateIngredientApiResObj extends ApiBaseResponse {}

// Response after getting a single ingredient
export interface GetSingleIngredientApiResObj extends ApiBaseResponse {
  data: IngredientData;
}

// Response after updating an ingredient
export interface UpdateIngredientApiResObj extends ApiBaseResponse {}

// Response after deleting an ingredient
export interface DeleteIngredientApiResObj extends ApiBaseResponse {}

export interface CreatIngredientApiResObj extends ApiBaseResponse {}
