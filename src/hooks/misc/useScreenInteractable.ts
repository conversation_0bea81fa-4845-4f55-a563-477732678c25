
import { useEffect, useState } from 'react';
import { InteractionManager } from 'react-native';

export const useScreenInteractable = () => {
  const [isScreenLoaded, setIsScreenLoaded] = useState(false);

  useEffect(() => {
    let isMounted = true;
    
    const handle = InteractionManager.runAfterInteractions(() => {
      if (isMounted) {
        setIsScreenLoaded(true);
      }
    });

    return () => {
      isMounted = false;
      handle.cancel();
    };
  }, []);

  return isScreenLoaded;
};
