import { useCallback, useEffect } from 'react';

import { StackActions, useNavigation } from '@react-navigation/native';
import * as SecureStore from 'expo-secure-store';

import { USER_STORAGE_KEY } from '../../store/constants/store_keys';
import useUserStore from '../../store/UserStore';
import useAppStateStore from '../../store/AppStateStore';

const useSession = () => {
    const navigation: any = useNavigation();
    const isAuthenticated = useUserStore((state) => state.isAuthenticated);
    const isAccountCompleted = useUserStore((state) => state.isAccountCompleted);
    const splashDone = useAppStateStore((state) => state.splashDone);

    const gotoWelcomeScreen = useCallback(() => {
        navigation.dispatch(StackActions.replace('Auth'));
    }, []);

    const gotoOnboardingScreen = useCallback(() => {
        navigation.dispatch(StackActions.replace('Onboarding'));
    }, []);

    const gotoAuthScreen = useCallback(() => {
        navigation.dispatch(StackActions.replace('Main'));
    }, []);

  const initAppFlow = useCallback(async () => {
    try {
        if (!splashDone) {
            return;
        }
        if (!isAuthenticated) {
            gotoWelcomeScreen();
            return;
        }
        if (isAuthenticated && !isAccountCompleted) {
            gotoOnboardingScreen();
            return;
        }
        if (isAuthenticated && isAccountCompleted) {
            gotoAuthScreen();
            return;
        }
    } catch (e: any) {
      await SecureStore.deleteItemAsync(USER_STORAGE_KEY);
      gotoWelcomeScreen();
    }
  }, [isAuthenticated, isAccountCompleted, splashDone]);

  useEffect(() => {
    initAppFlow();
  }, [isAuthenticated, isAccountCompleted, splashDone]);

  return null;
};

export default useSession;