
import { Module } from '@nestjs/common';
import { AdminUsersController } from './admin-users.controller';
import { AdminUsersService } from './admin-users.service';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RedisModule } from '@nestjs-modules/ioredis';
import { RepoModule } from 'src/repo/repo.module';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'models/user';
import { UserFileUploads, UserFileUploadsSchema } from 'models/file-upload/file-upload.schema';
import { RouteAccessToken, RouteAccessTokenSchema } from 'models/auth';
import { JwtModule } from '@nestjs/jwt';
import { FileUploadModule } from 'src/file-upload/file-upload.module';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.ROUTE_ACCESS_TOKEN_SECRET,
      signOptions: { expiresIn: '15m' },
    }),
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: UserFileUploads.name, schema: UserFileUploadsSchema },
      { name: RouteAccessToken.name, schema: RouteAccessTokenSchema },
    ]),
    CommonModule,
    AuthModule,
    RedisModule,
    RepoModule,
    FileUploadModule
  ],
  controllers: [
    AdminUsersController,
  ],
  providers: [
    AdminUsersService,
  ],
})

export class AdminUsersModule {}
