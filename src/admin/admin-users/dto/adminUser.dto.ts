import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNumber,
  IsOptional,
} from 'class-validator';
import { GENDER_TYPES } from 'models/user';
import { ActivityLevelEnum, User } from 'models/user/user.schema';

export class AdminUserDTO {
  @ApiProperty({
    example: '123',
  })
  id: string;

  @ApiProperty({
    example: 'John',
  })
  firstName: string;

  @ApiProperty({
    example: 'Doe',
  })
  lastName: string;

  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'https://example.com/profile-pics/profile-pic-123.jpg',
  })
  profilePic: string | null;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isEmailVerified: boolean;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isDeleted: boolean;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isAccountCompleted: boolean;

  @ApiProperty({
    example: '01-01-2021',
    required: false,
  })
  @IsOptional()
  dob?: string;

  @ApiProperty({
    example: 'male',
    required: false,
  })
  @IsOptional()
  @IsEnum(GENDER_TYPES)
  gender?: GENDER_TYPES;

  @ApiProperty({
    example: 73,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({
    example: 70,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  weight?: number;

  @ApiProperty({
    example: 'New York',
    required: false,
  })
  @IsOptional()
  city?: string;

  @ApiProperty({
    example: 'New York',
    required: false,
  })
  @IsOptional()
  state?: string;

  @ApiProperty({
    example: 'USA',
    required: false,
  })
  @IsOptional()
  country?: string | null;

  @ApiProperty({
    example: 'sedentary',
    required: false,
  })
  @IsOptional()
  activityLevel?: ActivityLevelEnum;

  static transform(object: User,profilePic: string | null): AdminUserDTO {
    const transformedObj: AdminUserDTO = new AdminUserDTO();

    transformedObj.id = object._id.toString();
    transformedObj.email = object.email;
    transformedObj.firstName = object.firstName;
    transformedObj.lastName = object.lastName;
    transformedObj.profilePic = profilePic;
    transformedObj.isEmailVerified = object.isEmailVerified;
    transformedObj.isAccountCompleted = object.isAccountCompleted;
    transformedObj.isDeleted = object.isDeleted;
    transformedObj.dob = object.dob;
    transformedObj.gender = object.gender;
    transformedObj.height = object.height;
    transformedObj.weight = object.weight;
    transformedObj.city = object.city;
    transformedObj.state = object.state;
    transformedObj.country = object.country;
    transformedObj.activityLevel = object.activityLevel;

    return transformedObj;
  }
}
