import { Module } from '@nestjs/common';
import { AdminAuthController } from './admin-auth.controller';
import { AdminAuthService } from './admin-auth.service';
import { AuthModule } from 'src/auth/auth.module';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'models/user';
import { CommonModule } from 'src/common/common.module';
import { RedisModule } from '@nestjs-modules/ioredis';
import { RepoModule } from 'src/repo/repo.module';
import { FileUploadModule } from 'src/file-upload/file-upload.module';

@Module({
  imports: [
    MongooseModule.forFeature([
        { name: User.name, schema: UserSchema },
    ]),
    CommonModule,
    AuthModule,
    RedisModule,
    RepoModule,
    FileUploadModule,
  ],
  controllers: [
    AdminAuthController,
  ],
  providers: [
    AdminAuthService,
  ],
})

export class AdminAuthModule {}
