import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';
import { Faq } from 'models/help';

export class FaqDTO {
  @ApiProperty({
    description: 'The id of the FAQ',
    example: '123',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'The question for the FAQ',
    example: 'How do I reset my password?',
  })
  @IsString()
  @IsNotEmpty()
  question: string;

  @ApiProperty({
    description: 'The answer to the FAQ question',
    example: 'Go to settings and click on reset password.',
  })
  @IsString()
  @IsNotEmpty()
  answer: string;

  @ApiProperty({
    description: 'Indicates if the FAQ is deleted',
    example: false,
  })
  @IsBoolean()
  isDeleted: boolean;

  createdAt: Date;

  static transform(object: Faq): FaqDTO {
    const transformedObj: FaqDTO = new FaqDTO();

    transformedObj.id = object._id.toString();
    transformedObj.question = object.question;
    transformedObj.answer = object.answer;
    transformedObj.isDeleted = object.isDeleted;
    transformedObj.createdAt = (object as any).createdAt;

    return transformedObj;
  }
}
