import { Module } from '@nestjs/common';
import { AdminFaqController } from './admin-faq.controller';
import { AdminFaqService } from './admin-faq.service';

import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RedisModule } from '@nestjs-modules/ioredis';
import { RepoModule } from 'src/repo/repo.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Faq, FaqSchema } from 'models/help';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Faq.name, schema: FaqSchema },
    ]),
    CommonModule,
    AuthModule,
    RedisModule,
    RepoModule,
  ],
  controllers: [
    AdminFaqController,
  ],
  providers: [
    AdminFaqService,
  ],
})

export class AdminFaqModule {}
