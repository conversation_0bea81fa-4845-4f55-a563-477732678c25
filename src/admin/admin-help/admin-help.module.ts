import { Modu<PERSON> } from '@nestjs/common';
import { AdminHelpController } from './admin-help.controller';
import { AdminHelpService } from './admin-help.service';

import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RedisModule } from '@nestjs-modules/ioredis';
import { RepoModule } from 'src/repo/repo.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Help, HelpSchema } from 'models/help';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Help.name, schema: HelpSchema },
    ]),
    CommonModule,
    AuthModule,
    RedisModule,
    RepoModule,
  ],
  controllers: [
    AdminHelpController,
  ],
  providers: [
    AdminHelpService,
  ],
})

export class AdminHelpModule {}
