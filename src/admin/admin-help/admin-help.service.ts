import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Help } from 'models/help';
import { UtilsService } from 'src/common/services';
import { GetAllHelpQueryInterface } from './interface';
import {
  CreateHelpReqDTO,
  DeleteHelpResDTO,
  GetAllHelpResDTO,
  GetSingleHelpResDTO,
  HelpDTO,
  UpdateHelpReqDTO,
} from './dto';
import { CreateHelpResDTO } from './dto/createHelp.dto';
import { UpdateHelpResDTO } from './dto/updateHelp.dto';

@Injectable()
export class AdminHelpService {
  constructor(
    @InjectModel(Help.name) private helpModel: Model<Help>,
    private readonly utilsService: UtilsService,
  ) {}

  async createHelp(helpData: CreateHelpReqDTO,adminId:Types.ObjectId): Promise<CreateHelpResDTO> {

    const newHelp = new this.helpModel({
      ...helpData,
      createdBy:adminId,
    });

    await newHelp.save();

    return {
      error: false,
      statusCode: HttpStatus.CREATED,
      msg: 'Help article created successfully',
      data: HelpDTO.transform(newHelp),
    };
  }
  
  async getAllHelp(
    queryFilters: GetAllHelpQueryInterface,
  ): Promise<GetAllHelpResDTO> {
    const { page, title, description } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false };

    // Build array of conditions for $or operator
    const orConditions = [];

    // Ensure title is a valid string before applying regex
    if (typeof title === 'string') {
      const trimmedTitle = title.trim();

      if (trimmedTitle.length > 0) {
        try {
          orConditions.push({
            title: { $regex: new RegExp(trimmedTitle, 'i') },
          });
        } catch (error) {
          console.error('Regex Error for title:', error);
        }
      }
    }

    // Add description search capability
    if (typeof description === 'string') {
      const trimmedDescription = description.trim();
      if (trimmedDescription.length > 0) {
        try {
          orConditions.push({
            description: {
              $regex: new RegExp(trimmedDescription, 'i'),
            },
          });
        } catch (error) {
          console.error('Regex Error for description:', error);
        }
      }
    }

    // Add the $or conditions to the query if any exist
    if (orConditions.length > 0) {
      if (orConditions.length === 1) {
        // If only one condition, no need for $or
        Object.assign(query, orConditions[0]);
      } else {
        query.$or = orConditions;
      }
    }

    const helpEntries = await this.helpModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.helpModel.countDocuments(query);
    const helpResponses = helpEntries.map((item) => HelpDTO.transform(item));

    return {
      error: false,
      statusCode: HttpStatus.OK,
      total,
      nbHits: helpResponses.length,
      data: helpResponses,
    };
  }

  async getHelpById(id: string): Promise<GetSingleHelpResDTO> {
    const help = await this.helpModel.findById(id).exec();
    if (!help || help.isDeleted) {
      throw new NotFoundException('Help entry not found');
    }
    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: HelpDTO.transform(help),
    };
  }

  async updateHelp(
    id: string,
    updateData: UpdateHelpReqDTO,
  ): Promise<UpdateHelpResDTO> {
    const existingHelp = await this.helpModel.findById(id).exec();

    if (!existingHelp) {
      throw new NotFoundException('Help entry not found');
    }

    if (existingHelp.isDeleted) {
      throw new BadRequestException('Cannot update a deleted help entry');
    }

    const updatedHelp = await this.helpModel
      .findByIdAndUpdate(id, updateData, { new: true, runValidators: true })
      .exec();

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Help article updated successfully',
      data: HelpDTO.transform(updatedHelp),
    };
  }

  async removeHelp(id: string): Promise<DeleteHelpResDTO> {
    const existingHelp = await this.helpModel.findById(id).exec();

    if (!existingHelp) {
      throw new NotFoundException('Help entry not found');
    }

    if (existingHelp.isDeleted) {
      throw new BadRequestException('Help entry is already deleted');
    }

    existingHelp.isDeleted = true;
    await existingHelp.save();

    return {
      error: false,
      statusCode: HttpStatus.OK,
      msg: 'Help article marked as deleted successfully',
    };
  }
}
