import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { Help } from 'models/help';
import { HelpTopicDTO } from './helpTopic.dto';

export class HelpDTO {
  @ApiProperty({ description: 'Help ID', example: '123' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Title of the help',
    example: 'Account Issues',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Description of the help',
    example: 'Steps to resolve account issues',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Indicates if the help is deleted',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;

  @ApiProperty({
    description: 'ID of the user who created the help',
    example: '123',
  })
  @IsString()
  @IsNotEmpty()
  createdBy: string;


  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  static transform(help: Help): HelpDTO {
    return {
      id: help._id.toString(),
      title: help.title,
      createdBy: help.createdBy.toString(),
      description: help.description,
      isDeleted: help.isDeleted,
      createdAt: new Date(),
    };
  }
}
