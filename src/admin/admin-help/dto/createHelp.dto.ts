import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';
import { HelpTopicDTO } from './helpTopic.dto';
import { BaseResponse } from 'src/utils/responses';
import { HelpDTO } from './help.dto';
import { Type } from 'class-transformer';

export class CreateHelpReqDTO {
  @ApiProperty({ description: 'Title of help', example: 'Account Issues' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Description of the help',
    type: String,
  })
  @IsNotEmpty() 
  description: string;
}

export class CreateHelpResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Help article created successfully',
  })
  msg: string;

  @ApiProperty({ description: 'Created help article details', type: HelpDTO })
  data: HelpDTO;
}
