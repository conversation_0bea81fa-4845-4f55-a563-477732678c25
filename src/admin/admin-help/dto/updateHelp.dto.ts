import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';
import { HelpTopicDTO } from './helpTopic.dto';
import { BaseResponse } from 'src/utils/responses';
import { HelpDTO } from './help.dto';
import { Type } from 'class-transformer';

export class UpdateHelpReqDTO {
  @ApiPropertyOptional({
    description: 'Updated title',
    example: 'Technical Support',
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({
    description: 'Updated description',
    example: 'Steps to resolve account issues',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Update deletion status',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;
}

export class UpdateHelpResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Response message',
    example: 'Help article updated successfully',
  })
  msg: string;

  @ApiProperty({ description: 'Updated help article details', type: HelpDTO })
  data: HelpDTO;
}
