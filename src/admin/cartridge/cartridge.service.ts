import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { AddCartridgeReqDTO, AddCartridgeResDTO } from './cartridge-dtos';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Cartridge } from 'models/cartridge/cartridge.schema';
import { CartridgeModal } from 'models/cartridge/cartridgeModal.schema';

@Injectable()
export class CartridgeService {
    constructor(
        @InjectModel(Cartridge.name)
        private readonly cartridge: Model<Cartridge>,

        @InjectModel(CartridgeModal.name)
        private readonly cartridgeModal: Model<CartridgeModal>,
    ){}
    
    async addCartridge(addCartridgeData: AddCartridgeReqDTO,adminId:Types.ObjectId):Promise<AddCartridgeResDTO> {

        const { cartridgeId, cartridgeModalId } = addCartridgeData;

        const existingCartridgeModal = await this.cartridgeModal.findOne({ _id: cartridgeModalId });

        if(!existingCartridgeModal){
            throw new NotFoundException('Cartridge modal does not exist');
        }
        
        const existingCartridge = await this.cartridge.findOne({ cartridgeId,isDeleted:false });
        if(existingCartridge){
            throw new BadRequestException('Cartridge already exists');
        }

        await this.cartridge.create({
            cartridgeId,
            cartridgeModalId,
            tablets: 2030938240,
            addedBy:adminId,
            currentCount: 5,
        });

        return AddCartridgeResDTO.transform('Cartridge added successfully');
    }
}
