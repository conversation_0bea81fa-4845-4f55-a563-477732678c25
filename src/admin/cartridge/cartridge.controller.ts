import { Body, Controller, Post, Request, UseGuards } from '@nestjs/common';
import { CartridgeService } from './cartridge.service';
import { AuthGuard } from 'src/middlewares';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Authority } from 'src/utils/decorators';
import { AddCartridgeReqDTO, AddCartridgeResDTO } from './cartridge-dtos';
import { ErrorResponse } from 'src/utils/responses';


@ApiTags('Admin-Cartridge')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('admin')
export class CartridgeController {
  constructor(private readonly cartridgeService: CartridgeService) {}

  @ApiOperation({ summary: 'Add a new cartridge' })
  @ApiResponse({
    status: 200,
    description: 'Successfully added the cartridge.',
    type: AddCartridgeResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority()
  @Post('/add-cartridge')
  async  addCartridge(
    @Request() req,
    @Body() addCartridgeData: AddCartridgeReqDTO
  ):Promise<AddCartridgeResDTO> {
    return this.cartridgeService.addCartridge(addCartridgeData,req.user._id);
  }
}
