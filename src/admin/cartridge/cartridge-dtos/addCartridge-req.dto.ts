
import { BadRequestException } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { Types } from 'mongoose';

export class AddCartridgeReqDTO {
  @ApiProperty({
    description: 'The cartridge id',
    example: '64e06e0e0e0e0e0e0e0e0e0e',
  })
  @IsNotEmpty()
  cartridgeId: string;

  @ApiProperty({
    type: String,
    description: 'The cartridge modal id',
    example: '64e06e0e0e0e0e0e0e0e0e0e',
  })
  @IsNotEmpty()
  @Transform(({ value }: { value: string }) => {
    if (!Types.ObjectId.isValid(value)) {
      throw new BadRequestException('Invalid cartridge modal id');
    }
    return new Types.ObjectId(value);
  })
  cartridgeModalId: Types.ObjectId;

}

