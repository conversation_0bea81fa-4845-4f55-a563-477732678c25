
import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export class AddCartridgeResDTO extends BaseResponse {
    @ApiProperty({
        type: String,
        description: 'Message to be sent in the response',
        example: 'Cartridge added successfully',
    })
    message: string;

    static transform(message: string): AddCartridgeResDTO {
        const transformedObj = new AddCartridgeResDTO();
        transformedObj.message = message;
        return transformedObj;
    }
}
