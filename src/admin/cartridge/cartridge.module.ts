import { Module } from '@nestjs/common';
import { CartridgeService } from './cartridge.service';
import { CartridgeController } from './cartridge.controller';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RedisModule } from '@nestjs-modules/ioredis';
import { RepoModule } from 'src/repo/repo.module';
import { Cartridge, CartridgeSchema } from 'models/cartridge/cartridge.schema';
import { CartridgeModal, CartridgeModalSchema } from 'models/cartridge/cartridgeModal.schema';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports:[   
    MongooseModule.forFeature([
      { name: Cartridge.name, schema: CartridgeSchema },
      { name: CartridgeModal.name, schema: CartridgeModalSchema },
    ]),
    CommonModule,
    AuthModule,
    RedisModule,
    RepoModule,
  ],
  controllers: [CartridgeController],
  providers: [CartridgeService],
})
export class AdminCartridgeModule {}
