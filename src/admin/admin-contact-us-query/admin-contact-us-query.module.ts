import { Module } from '@nestjs/common';
import { AdminContactUsQueryController } from './admin-contact-us-query.controller';
import { AdminContactUsQueryService } from './admin-contact-us-query.service';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RedisModule } from '@nestjs-modules/ioredis';
import { RepoModule } from 'src/repo/repo.module';
import { MongooseModule } from '@nestjs/mongoose';
import { ContactUsQuery, ContactUsQuerySchema } from 'models/help';
import { FileUploadService } from 'src/file-upload/file-upload.service';
import { UserFileUploads, UserFileUploadsSchema } from 'models/file-upload/file-upload.schema';
import { ClamAVService } from 'src/file-upload/clamav.service';
import { AwsS3Service } from 'src/third-party/aws';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ContactUsQuery.name, schema: ContactUsQuerySchema },
      { name: UserFileUploads.name, schema: UserFileUploadsSchema },
    ]),
    CommonModule,
    AuthModule,
    RedisModule,
    RepoModule,
  ],
  controllers: [
    AdminContactUsQueryController,
  ],
  providers: [
    AdminContactUsQueryService,
    FileUploadService,
    ClamAVService,
    AwsS3Service,
  ],
})

export class AdminContactUsQueryModule {}
