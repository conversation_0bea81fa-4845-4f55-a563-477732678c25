import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { ContactUsQueryDTO } from 'src/user/contact_us/contact_us-dto';

export class GetSingleContactUsQueryResDTO extends BaseResponse {
  @ApiProperty({
    description: 'The contact query object',
    type: ContactUsQueryDTO,
  })
  @IsNotEmpty()
  query: ContactUsQueryDTO;
}
