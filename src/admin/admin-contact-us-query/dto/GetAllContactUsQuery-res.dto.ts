import { BaseResponse } from 'src/utils/responses';
import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsInt, IsNotEmpty } from 'class-validator';
import { ContactUsQueryDTO } from 'src/user/contact_us/contact_us-dto';

export class GetAllContactUsQueryResDTO extends BaseResponse {
  @ApiProperty({
    description: 'The number of hits found',
    type: Number,
  })
  @IsInt()
  @IsNotEmpty()
  nbHits: number;

  @ApiProperty({
    description: 'The total number of queries',
    type: Number,
  })
  @IsInt()
  @IsNotEmpty()
  total: number;

  @ApiProperty({
    description: 'List of contact queries',
    type: [ContactUsQueryDTO],
  })
  @IsArray()
  queries: ContactUsQueryDTO[];
}
