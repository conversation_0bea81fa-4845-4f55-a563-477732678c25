import { Modu<PERSON> } from '@nestjs/common';
import { AdminCartridgeModule } from './cartridge/cartridge.module';
import { AdminFaqModule } from './admin-faq/admin-faq.module';
import { AdminContactUsQueryModule } from './admin-contact-us-query/admin-contact-us-query.module';
import { AdminHelpModule } from './admin-help/admin-help.module';
import { AdminAuthModule } from './auth/admin-auth.module';
import { AdminPermissionModule } from './admin-permissions/admin-permission.module';
import { AdminUsersModule } from './admin-users/admin-users.module';
import { AdminOtpModule } from './admin-otp-verification/admin-otp.module';
import { AdminAnalyticsModule } from './admin-analytics/admin-analytics.module';

@Module({
  imports: [
    AdminAuthModule,
    AdminPermissionModule,
    AdminUsersModule,
    AdminCartridgeModule,
    AdminFaqModule,
    AdminContactUsQueryModule,
    AdminHelpModule,
    AdminOtpModule,
    AdminAnalyticsModule
  ],
  controllers: [],
  providers: [],
})

export class AdminModule {}
