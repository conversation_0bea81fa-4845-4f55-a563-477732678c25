import { HttpStatus, Injectable, BadRequestException } from '@nestjs/common';
import { Model } from 'mongoose';
import { UserJourneyPerDay } from 'models/user-journey';
import { User } from '../../../../models/user';
import { CombinedTrendLine } from './dto/getActiveUsers-res.dto';
import { InjectModel } from '@nestjs/mongoose';
import {
  ACTIVITY_PERIOD,
  AGE_GROUP_LABEL,
  UserAgeGroupStatsResDto,
} from './dto/getUsersStatsByAge-res.dto';

@Injectable()
export class UserAdminAnalyticsUtilsService {
  constructor(
    @InjectModel(UserJourneyPerDay.name)
    private readonly userJourneyPerDayModel: Model<UserJourneyPerDay>,

    @InjectModel(User.name)
    private readonly userModel: Model<User>,

  ) {}

  private getPastDates(days: number): string[] {
    const dates: string[] = [];
    const today = new Date();
    today.setUTCHours(0, 0, 0, 0);

    for (let i = 0; i < days; i++) {
      const d = new Date(today);
      d.setDate(today.getDate() - i);
      dates.push(d.toISOString().split('T')[0]);
    }

    return dates;
  }
  async getWeeklyActiveUsers(): Promise<CombinedTrendLine[]> {
    const data: CombinedTrendLine[] = [];

    for (let i = 6; i >= 0; i--) {
      const day = new Date();
      day.setUTCDate(day.getUTCDate() - i);
      day.setUTCHours(0, 0, 0, 0);

      const nextDay = new Date(day);
      nextDay.setUTCDate(nextDay.getUTCDate() + 1);
      nextDay.setUTCHours(0, 0, 0, 0);

      const label = day.toISOString().split('T')[0];

      const count = await this.userJourneyPerDayModel.countDocuments({
        time: { $gte: day, $lt: nextDay },
        role: { $ne: 'admin' },
      });

      data.push({
        label,
        activeUsers: count,
        registeredUsers: 0,
      });
    }

    return data;
  }

  async getWeeklyRegisteredUsers(): Promise<CombinedTrendLine[]> {
    const dates = this.getPastDates(7).reverse();
    const data: CombinedTrendLine[] = [];

    for (const date of dates) {
      const start = new Date(date);
      const end = new Date(date);
      end.setUTCHours(23, 59, 59, 999);

      const count = await this.userModel.countDocuments({
        createdAt: { $gte: start, $lte: end },
        role: { $ne: 'admin' },
      });

      data.push({ label: date, registeredUsers: count, activeUsers: 0 });
    }

    return data;
  }

  async getMonthlyActiveUsers(): Promise<CombinedTrendLine[]> {
    const data: CombinedTrendLine[] = [];

    for (let i = 0; i < 4; i++) {
      const end = new Date();
      end.setUTCHours(0, 0, 0, 0);
      end.setDate(end.getDate() - i * 7);

      const start = new Date(end);
      start.setDate(start.getDate() - 6);

      const count = await this.userJourneyPerDayModel.countDocuments({
        time: { $gte: start, $lte: end },
        role: { $ne: 'admin' },
      });

      const label = `${start.toISOString().split('T')[0]} to ${end.toISOString().split('T')[0]}`;
      data.unshift({ label, activeUsers: count, registeredUsers: 0 });
    }

    return data;
  }

  async getMonthlyRegisteredUsers(): Promise<CombinedTrendLine[]> {
    function getStartOfDay(date: Date): Date {
      const d = new Date(date);
      d.setUTCHours(0, 0, 0, 0);
      return d;
    }

    function getEndOfDay(date: Date): Date {
      const d = new Date(date);
      d.setUTCHours(23, 59, 59, 999);
      return d;
    }
    const data: CombinedTrendLine[] = [];

    let currentEnd = getEndOfDay(new Date());

    for (let i = 0; i < 4; i++) {
      const start = getStartOfDay(new Date(currentEnd));
      start.setDate(start.getDate() - 6);

      const count = await this.userModel.countDocuments({
        createdAt: { $gte: start, $lte: currentEnd },
        isEmailVerified: true,
        role: { $ne: 'admin' },
        isDeleted: false,
      });

      const label = `${start.toISOString().split('T')[0]} to ${currentEnd.toISOString().split('T')[0]}`;
      data.unshift({ label, registeredUsers: count, activeUsers: 0 });

      currentEnd = getEndOfDay(new Date(start));
      currentEnd.setDate(currentEnd.getDate() - 1);
    }

    return data;
  }

  async getTotalRegisteredUsers(): Promise<number> {
    return this.userModel.countDocuments({ role: { $ne: 'admin' } });
  }
  
  getStartDateForPeriod(period: ACTIVITY_PERIOD): Date {
    const now = new Date();
    switch (period) {
      case ACTIVITY_PERIOD.CURRENT_DAY:
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case ACTIVITY_PERIOD.LAST_SEVEN_DAYS:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case ACTIVITY_PERIOD.LAST_THIRTY_DAYS:
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      default:
        throw new BadRequestException('Invalid period');
    }
  }

  buildAgeGroupAggregation(
    filter: any,
    isAccountCompletedFilter: boolean = false,
  ) {
    const matchStage: any = {
      ...filter,
      age: { $exists: true, $ne: null },
    };

    if (isAccountCompletedFilter) {
      matchStage.isAccountCompleted = true;
    }

    return [
      { $match: matchStage },
      {
        $group: {
          _id: '$age',
          doc: { $first: '$$ROOT' },
        },
      },
      {
        $replaceRoot: { newRoot: '$doc' },
      },
      {
        $addFields: {
          ageGroup: {
            $switch: {
              branches: [
                { case: { $lt: ['$age', 13] }, then: AGE_GROUP_LABEL.CHILD },
                { case: { $lt: ['$age', 18] }, then: AGE_GROUP_LABEL.TEEN },
                {
                  case: { $lt: ['$age', 30] },
                  then: AGE_GROUP_LABEL.YOUNG_ADULT,
                },
                { case: { $lt: ['$age', 60] }, then: AGE_GROUP_LABEL.ADULT },
              ],
              default: AGE_GROUP_LABEL.SENIOR,
            },
          },
        },
      },
      {
        $group: {
          _id: '$ageGroup',
          count: { $sum: 1 },
        },
      },
    ];
  }

  async calculateUserAgeGroupStats(
    period: ACTIVITY_PERIOD,
  ): Promise<UserAgeGroupStatsResDto> {
    const startDate = this.getStartDateForPeriod(period);

    const [registered, active] = await Promise.all([
      this.userModel.aggregate(
        this.buildAgeGroupAggregation({ createdAt: { $gte: startDate } }, true),
      ),
      this.userJourneyPerDayModel.aggregate(
        this.buildAgeGroupAggregation({ time: { $gte: startDate } }),
      ),
    ]);

    return this.mergeAgeGroupResults(registered, active);
  }

  mergeAgeGroupResults(
    registered: { _id: AGE_GROUP_LABEL; count: number }[],
    active: { _id: AGE_GROUP_LABEL; count: number }[],
  ): UserAgeGroupStatsResDto {
    const result = {} as UserAgeGroupStatsResDto;

    for (const group of Object.values(AGE_GROUP_LABEL)) {
      result[group] = { registeredUsers: 0, activeUsers: 0 };
    }

    // Merge the registered and active counts by age group
    for (const doc of registered) {
      result[doc._id].registeredUsers = doc.count;
    }

    for (const doc of active) {
      result[doc._id].activeUsers = doc.count;
    }

    return result;
  }
}
