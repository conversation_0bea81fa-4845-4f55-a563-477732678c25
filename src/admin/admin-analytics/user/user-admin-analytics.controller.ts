import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/middlewares';
import { ErrorResponse } from 'src/utils/responses';
import {
  GetUserEngagementTrendsResDto,
  USER_TREND_TYPE,
} from './dto/getActiveUsers-res.dto';
import {
  getDeviceUsageStatsResDto,
  DEVICE_USAGE_PERIOD,
} from './dto/getDeviceUsageStats-res.dto';
import {
  UserAgeGroupStatsResDto,
  ACTIVITY_PERIOD,
} from './dto/getUsersStatsByAge-res.dto';
import { AdminAnalyticsService } from './user-admin-analytics.service';

@ApiTags('Admin-Analytics')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('admin/analytics')
export class AdminAnalyticsController {
  constructor(private readonly adminAnalyticsService: AdminAnalyticsService) {}

  @ApiOperation({ summary: 'Get user engagement trends (weekly or monthly)' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved user engagement trends',
    type: GetUserEngagementTrendsResDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid type provided',
    type: ErrorResponse,
  })
  @Get('user_engagement_trends/:type')
  getUserEngagementTrends(@Param('type') type: USER_TREND_TYPE) {
    return this.adminAnalyticsService.getUserEngagementTrends(type);
  }

  @Get('user_age_groups/:period')
  @ApiParam({
    name: 'period',
    enum: ACTIVITY_PERIOD,
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved user stats by age group.',
    type: UserAgeGroupStatsResDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid period provided.',
    type: ErrorResponse,
  })
  getUserAgeGroupStats(
    @Param('period') period: ACTIVITY_PERIOD,
  ): Promise<UserAgeGroupStatsResDto> {
    return this.adminAnalyticsService.getUserAgeGroupStats(period);
  }
}
