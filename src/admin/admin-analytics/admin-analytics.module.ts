
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'models/user';
import { AuthModule } from 'src/auth/auth.module';
import { CommonModule } from 'src/common/common.module';
import { RepoModule } from 'src/repo/repo.module';
import {
  UserJourneyPerDay,
  UserJourneyPerDaySchema,
} from 'models/user-journey';
import {
  UserDeviceConnections,
  UserDeviceConnectionsSchema,
} from 'models/device';
import { AdminAnalyticsController } from './user/user-admin-analytics.controller';
import { AdminAnalyticsService } from './user/user-admin-analytics.service';
import { UserAdminAnalyticsUtilsService } from './user/user-admin-analytics-utils.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: UserJourneyPerDay.name, schema: UserJourneyPerDaySchema },
      { name: UserDeviceConnections.name, schema: UserDeviceConnectionsSchema },
    ]),
    AuthModule,
    CommonModule,
    RepoModule,
  ],
  controllers: [
    AdminAnalyticsController,
  ],
  providers: [
    AdminAnalyticsService,
    UserAdminAnalyticsUtilsService,
  ],
})
export class AdminAnalyticsModule {}