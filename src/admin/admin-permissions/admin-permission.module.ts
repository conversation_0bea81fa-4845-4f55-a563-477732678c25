import { Module } from '@nestjs/common';
import { AdminPermissionController } from './admin-permission.controller';
import { AdminPermissionService } from './admin-permission.service';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RedisModule } from 'src/redis/redis.module';
import { RepoModule } from 'src/repo/repo.module';

@Module({
    imports: [
        CommonModule,
        AuthModule,
        RedisModule,
        RepoModule,
    ],
    controllers: [AdminPermissionController],
    providers: [AdminPermissionService],
})

export class AdminPermissionModule {}
