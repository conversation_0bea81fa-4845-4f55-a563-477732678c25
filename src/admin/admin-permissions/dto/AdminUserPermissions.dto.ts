export enum PermissionCategory {
  DASHBOARD = 'dashboard',
  USERS = 'users',
  FAQ = 'faq',
  HELP = 'help',
  CONTACT_US = 'contact_us',
}

export class AdminUser_PermissionDTO {
  id: string;
  parentId: string;
  label: string;
  name: string;
  icon?: string;
  type: number;
  route: string;
  order?: number;
  component?: string;
  category: PermissionCategory;
  children?: AdminUser_PermissionDTO[];

  static transform(object: any): AdminUser_PermissionDTO {
    const transformedObj: AdminUser_PermissionDTO =
      new AdminUser_PermissionDTO();

    // Map simple properties
    transformedObj.id = object.id.toString();
    transformedObj.parentId = '';
    transformedObj.label = object.name;
    transformedObj.name = object.name;
    // transformedObj.icon = 'ic-analysis';
    transformedObj.type = 1;
    transformedObj.route = `/${object.category}/${object.value.toLowerCase()}`;
    transformedObj.order = object.id;
    transformedObj.category = object.category;

    const component = `/dashboard/${object.category}/index.tsx`;
    transformedObj.component = component;

    return transformedObj;
  }
}
