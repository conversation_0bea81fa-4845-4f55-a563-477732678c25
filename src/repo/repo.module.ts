import { Module } from '@nestjs/common';
import { UserRepoService } from './user-repo.service';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from 'models/user';
import { CommonModule } from 'src/common/common.module';
import { RedisModule } from 'src/redis/redis.module';
import { FileUploadModule } from 'src/file-upload/file-upload.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
    CommonModule,
    RedisModule,
    FileUploadModule,
  ],
  providers: [UserRepoService],
  exports: [UserRepoService],
})
export class RepoModule {}
