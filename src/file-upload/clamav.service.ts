import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as clamav from 'clamav.js';
import { Readable } from 'stream';

@Injectable()
export class ClamAVService {
  private readonly logger = new Logger(ClamAVService.name);
  private readonly CLAMAV_PORT: number;
  private readonly CLAMAV_HOST: string;

  constructor(private readonly configService: ConfigService) {
    this.CLAMAV_PORT = Number(this.configService.get('CLAMAV_PORT') || 3310);
    this.CLAMAV_HOST = this.configService.get('CLAMAV_HOST') || 'localhost';

    // Log ClamAV configuration for debugging
    this.logger.log(
      `ClamAV Configuration - Host: ${this.CLAMAV_HOST}, Port: ${this.CLAMAV_PORT}`,
    );
  }

  async scanBuffer(buffer: Buffer): Promise<boolean> {
    this.logger.log(
      `Attempting to scan buffer with ClamAV at ${this.CLAMAV_HOST}:${this.CLAMAV_PORT}`,
    );

    return new Promise((resolve, reject) => {
      // First ping to check if ClamAV is available
      clamav.ping(this.CLAMAV_PORT, this.CLAMAV_HOST, 1000, (err) => {
        if (err) {
          this.logger.error(`ClamAV ping failed: ${err.message}`);
          return reject(new Error('ClamAV not available'));
        }

        this.logger.log('ClamAV ping successful, proceeding with scan');

        // Create a scanner instance
        const scanner = clamav.createScanner(
          this.CLAMAV_PORT,
          this.CLAMAV_HOST,
        );

        // Convert buffer to readable stream
        const stream = new Readable();
        stream.push(buffer);
        stream.push(null); // End the stream

        // Scan the stream
        scanner.scan(stream, (err, object, malicious) => {
          if (err) {
            this.logger.error(`ClamAV scan failed: ${err.message}`);
            return reject(err);
          }

          const isInfected = !!malicious;
          this.logger.log(`ClamAV scan completed - Malicious: ${isInfected}`);
          if (isInfected) {
            this.logger.warn(`Virus detected: ${malicious}`);
          }

          resolve(isInfected);
        });
      });
    });
  }
}
