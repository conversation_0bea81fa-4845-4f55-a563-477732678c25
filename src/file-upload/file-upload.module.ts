import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FileUploadService } from './file-upload.service';

import { AwsS3Service } from 'src/third-party/aws';
import {
  UserFileUploads,
  UserFileUploadsSchema,
} from 'models/file-upload/file-upload.schema';
import { ClamAVService } from './clamav.service';
import { FileUploadController } from './file-upload.controller';
import { ClamAVConfigService } from 'config/clamav.config';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserFileUploads.name, schema: UserFileUploadsSchema },
    ]),
    CommonModule,
  ],
  controllers: [FileUploadController],
  providers: [
    FileUploadService,
    AwsS3Service,
    ClamAVService,
    ClamAVConfigService,
  ],
  exports: [FileUploadService],
})
export class FileUploadModule {}
