import {
  Controller,
  Post,
  UploadedFile,
  UseInterceptors,
  Get,
  Param,
  Query,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { FileUploadService } from './file-upload.service';
import { Express } from 'express';
import { Types } from 'mongoose';

@Controller('file')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Query('folder') folder: string,
  ) {
    const uploaded = await this.fileUploadService.uploadAndStoreFile(file,folder);
    return {
      message: 'File uploaded and scanned successfully',
      fileId: uploaded._id,
    };
  }

  @Get(':id/url')
  async getFileUrl(@Param('id') fileId: Types.ObjectId) {
    const url = await this.fileUploadService.getValidS3Url(fileId);
    return { url };
  }
}
