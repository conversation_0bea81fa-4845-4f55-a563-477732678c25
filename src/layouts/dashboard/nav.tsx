import { CaretLeftOutlined, CaretRightOutlined, MenuOutlined } from '@ant-design/icons';
import { Menu, MenuProps } from 'antd';
import Color from 'color';
import { m } from 'framer-motion';
import { CSSProperties, useEffect, useMemo, useState } from 'react';
import { Link, useLocation, useMatches, useNavigate } from 'react-router-dom';

import Nouriq_logo from 'public/images/app_logo.png';
import Nouriq_Icon from 'public/images/app_logo.png';
import MotionContainer from '@/components/animate/motion-container';
import { varSlide } from '@/components/animate/variants';
// import Logo from '@/components/logo';
import Scrollbar from '@/components/scrollbar';
import { useRouteToMenuFn, usePermissionRoutes, useFlattenedRoutes } from '@/router/hooks';
import { menuFilter } from '@/router/utils';
import { useSettingActions, useSettings } from '@/store/settingStore';
import { useThemeToken } from '@/theme/hooks';

import { NAV_COLLAPSED_WIDTH, NAV_WIDTH } from './config';

import { ThemeLayout } from '#/enum';

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

const slideInLeft = varSlide({ distance: 10 }).inLeft;

type Props = {
  closeSideBarDrawer?: () => void;
};

export default function Nav(props: Props) {
  const navigate = useNavigate();
  const matches = useMatches();
  const { pathname } = useLocation();

  const { colorPrimary, colorBgElevated, colorBorder } = useThemeToken();

  const settings = useSettings();
  const { themeLayout } = settings;
  const { setSettings } = useSettingActions();

  const menuStyle: CSSProperties = {
    background: colorBgElevated,
  };

  const routeToMenuFn = useRouteToMenuFn();
  const permissionRoutes = usePermissionRoutes();
  const menuList = useMemo(() => {
    const menuRoutes = menuFilter(permissionRoutes);
    return routeToMenuFn(menuRoutes);
  }, [routeToMenuFn, permissionRoutes]);

  // Retrieve the flattened route menu
  const flattenedRoutes = useFlattenedRoutes();

  const [collapsed, setCollapsed] = useState(false);
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [menuMode, setMenuMode] = useState<MenuProps['mode']>('inline');

  useEffect(() => {
    if (themeLayout === ThemeLayout.Vertical) {
      const openKeys = matches
        .filter((match) => match.pathname !== '/')
        .map((match) => match.pathname);
      setOpenKeys(openKeys);
    }
  }, [matches, themeLayout]);

  useEffect(() => {
    if (themeLayout === ThemeLayout.Vertical) {
      setCollapsed(false);
      setMenuMode('inline');
    }
    if (themeLayout === ThemeLayout.Mini) {
      setCollapsed(true);
      setMenuMode('inline');
    }
  }, [themeLayout]);

  const onOpenChange: MenuProps['onOpenChange'] = (keys) => {
    setOpenKeys(keys);
  };
  const onClick: MenuProps['onClick'] = ({ key }) => {
    const nextLink = flattenedRoutes?.find((el) => el.key === key);

    if (nextLink?.hideTab && nextLink?.frameSrc) {
      window.open(nextLink?.frameSrc, '_blank');
      return;
    }

    navigate(key);
    props?.closeSideBarDrawer?.();
  };

  const setThemeLayout = (themeLayout: ThemeLayout) => {
    setSettings({
      ...settings,
      themeLayout,
    });
  };

  const toggleCollapsed = () => {
    if (!collapsed) {
      setThemeLayout(ThemeLayout.Mini);
    } else {
      setThemeLayout(ThemeLayout.Vertical);
    }
    setCollapsed(!collapsed);
  };

  return (
    <div
      className="flex h-full flex-col"
      style={{
        width: collapsed ? NAV_COLLAPSED_WIDTH : NAV_WIDTH,
        borderRight: `1px dashed ${Color(colorBorder).alpha(0.6).toString()}`,
      }}
    >
      <div className="relative flex h-20 items-center justify-center bg-primary py-4 shadow-md px-7">
        <button
          onClick={toggleCollapsed}
          className="h-15 w-15 z-50  cursor-pointer select-none items-center justify-center rounded-full p-[3px] text-center text-[1.4rem] block lg:flex"
          style={{
            color: '#ffffff',
            paddingLeft: collapsed ? '5px' : '',
            paddingRight: collapsed ? '' : '5px',
          }}
        >
          {collapsed ? <MenuOutlined />: <MenuOutlined />}
        </button>
         <MotionContainer className="flex items-center">
          {/* <Logo /> */}
          {themeLayout !== ThemeLayout.Mini && (
            <m.div variants={slideInLeft}>
              <Link
                to={HOMEPAGE}
                className="flex items-center justify-center text-xl font-bold"
                style={{ color: colorPrimary }}
              >
                <img className="h-[3.2rem] w-[12rem]" src={Nouriq_logo} alt="nouriq logo" />
              </Link>
            </m.div>
          )}
          {/* {themeLayout === ThemeLayout.Mini && (
            <m.div variants={slideInLeft}>
              <Link
                to={HOMEPAGE}
                className="flex items-center justify-center text-xl font-bold"
                style={{ color: colorPrimary }}
              >
                <img className="h-8" src={Nouriq_Icon} alt="nouriq icon" />
              </Link>
            </m.div>
          )} */}
        </MotionContainer>
      </div>

      <Scrollbar
        style={{
          height: 'calc(100vh - 70px)',
        }}
      >
        {/* <!-- Sidebar Menu --> */}
        <Menu
          mode={menuMode}
          items={menuList}
          className="h-full !border-none"
          defaultOpenKeys={openKeys}
          defaultSelectedKeys={[pathname]}
          selectedKeys={[pathname]}
          openKeys={openKeys}
          onOpenChange={onOpenChange}
          onClick={onClick}
          style={menuStyle}
          inlineCollapsed={collapsed}
        />
      </Scrollbar>
    </div>
  );
}
