import { StyleProvider } from '@ant-design/cssinjs';
import { ConfigProvider, theme } from 'antd';
import 'antd/dist/reset.css';
import React from 'react';

import useLocale from '@/locales/useLocale';
import { useSettings } from '@/store/settingStore';

import {
  customThemeTokenConfig,
  themeModeToken,
  colorPrimarys,
  customComponentConfig,
} from './theme';

import { ThemeMode } from '#/enum';

type Props = {
  children: React.ReactNode;
};
export default function AntdConfig({ children }: Props) {
  const { themeMode, themeColorPresets } = useSettings();

  const { language } = useLocale();

  const algorithm = themeMode === ThemeMode.Light ? theme.defaultAlgorithm : theme.darkAlgorithm;
  const colorPrimary = colorPrimarys[themeColorPresets];

  return (
    <ConfigProvider
      locale={language.antdLocal}
      theme={{
        token: { colorPrimary, ...customThemeTokenConfig, ...themeModeToken[themeMode].token },
        components: {
          ...customComponentConfig,
          ...themeModeToken[themeMode].components,
          Menu: { colorItemBgSelected: '#2196F3', colorItemTextSelected: '#ffffff' },
          ...(themeMode === ThemeMode.Light
            ? {
                Select: {
                  // colorPrimary: colorPrimarys.darkblue, // Primary color for active/focus
                  // colorBgContainer: colorPrimarys.darkblue,
                  controlOutline: colorPrimarys.verylightBlue, // Outline on focus
                  colorBorder: colorPrimarys.verylightBlue, // Border color
                  colorPrimaryHover: colorPrimarys.verylightBlue,
                  controlItemBgHover: colorPrimarys.verylightBlue, // Option hover background
                  controlItemBgActive: colorPrimarys.blue, // Option selected background
                  colorBgElevated: '#ffffff',
                  colorText: colorPrimarys.white,
                  colorTextPlaceholder: colorPrimarys.blue,
                },
              }
            : {
                Select: {
                  controlOutlineWidth: 1,
                  controlOutline: colorPrimarys.darkblue, // Outline on focus
                  // colorBorder: colorPrimarys.verylightBlue, // Border color
                  colorPrimaryHover: colorPrimarys.darkblue,
                  controlItemBgHover: colorPrimarys.lightBlue, // Option hover background
                  controlItemBgActive: colorPrimarys.blue, // Option selected background
                  colorBgElevated: colorPrimarys.slate,
                  colorText: colorPrimarys.white,
                  colorTextPlaceholder: colorPrimarys.white,
                },
              }),
        },
        algorithm,
      }}
    >
      {/* https://ant.design/docs/react/compatible-style-cn#styleprovider */}
      <StyleProvider hashPriority="high">{children}</StyleProvider>
    </ConfigProvider>
  );
}
