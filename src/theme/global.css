/* editor */
@import url('react-quill/dist/quill.snow.css');

/* simplebar */
@import url('simplebar-react/dist/simplebar.min.css');
@import url('tailwindcss/base');
@import url('tailwindcss/components');
@import url('tailwindcss/utilities');

/* Control option text */
.ant-select-item-option:not(.ant-select-item-option-selected):hover {
  color: #fff !important;
}

.ant-select-item-option-active {
  color: #38BDF8 !important;
}

.ant-select-item-option-selected {
  color: #fff !important;
}
