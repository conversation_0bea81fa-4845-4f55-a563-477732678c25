import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import AppLayout from '../../navigation/components/Layouts/AppLayout';
import { useRoute } from '@react-navigation/native';

const SoftwareUpdate = () => {
  const route = useRoute();
  return (
    <AppLayout>
      <View>
        <Text>{route.name}</Text>
      </View>
    </AppLayout>
  )
}

export default SoftwareUpdate

const styles = StyleSheet.create({})