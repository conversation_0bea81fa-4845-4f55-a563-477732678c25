import { RefreshControl, ScrollView, StyleSheet, Text, View } from 'react-native'
import React, { useState } from 'react'
import { useNavigation } from '@react-navigation/native';
import { MainStackParamList } from '../../navigation/MainStack';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import AppLayout from '../../navigation/components/Layouts/AppLayout';
import InventoryCard from './components/InventoryCard';
import AddCartridgeModal from '../../components/Modals/Tablet/AddCartridgeModal';
import useGetUserStockData from './hooks/getUserStockData';
import PageLoader from '../../components/Loaders/PageLoader';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { useTheme } from '../../context/ThemeContext';


type RoutesNavigationProp = NativeStackNavigationProp<
  MainStackParamList,
  'Tablet Tracking'
>;

const InventoryScreen = () => {

  const { theme } = useTheme();
  const { stock, loading, error, refreshing, handleRefresh } = useGetUserStockData();
  
  const [shownCartridgeDetails, setShownCartridgeDetails] = useState<{ id: string, name: string, numTablets: number, cartidgeColor: string, tabletColor: string } | null>(null);

  return (
    <AppLayout>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.containerContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.button.primary]}
            progressViewOffset={24}
          />
        }
      >
        {
          loading ? (
            <View style={{flex:1,justifyContent:"center",alignItems:"center"}}>
              <PageLoader overlayBg='trasparent'/>
            </View>
          ) : (
            error ? (
              <View style={{flex:1,justifyContent:"center",alignItems:"center"}}>
                <Text style={[styles.errorText,{
                  color:theme.text.primary
                }]}>{error}</Text>
              </View>
            ) : (
              stock.map((item) => (
                <InventoryCard
                  key={item.cartridge.id}
                  tableName={item.cartridge.healthName}
                  tableDetails={item.cartridge.healthArea}
                  numTablets={item.quantity}
                  cartidgeColor={item.cartridge.colour.cartridge}
                  tabletColor={item.cartridge.colour.tablet}
                  onAddCartridge={() => {
                    setShownCartridgeDetails({
                      id: item.cartridge.id,
                      name: item.cartridge.healthName,
                      numTablets: item.quantity,
                      cartidgeColor: item.cartridge.colour.cartridge,
                      tabletColor: item.cartridge.colour.tablet
                    });
                  }}
                />
              ))
            )

          )
        }
        <AddCartridgeModal cartridgeDetails={shownCartridgeDetails || null} onClose={() => setShownCartridgeDetails(null)} onSuccess={() => handleRefresh()}/>
      </ScrollView>
    </AppLayout>
  )
}

export default InventoryScreen

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 16,
  },
  containerContent: {
    flexGrow: 1,
    gap: 12,
    paddingBottom: 90
  },
  errorText:{
    fontSize:20,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center"
  }
})