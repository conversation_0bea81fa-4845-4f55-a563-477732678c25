import { useEffect, useState } from 'react';
import cartridgeService from '../../../apis/cartridge/cartridgeService';
import { UserStockResponseType } from '../../../apis/cartridge/schema/userStockResponseSchema';

const useGetUserStockData = () => {
    const [stock, setStock] = useState<UserStockResponseType>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [refreshing, setRefreshing] = useState(false);

    const getUserStock = async (isRefreshing = false) => {
        if(!isRefreshing) setLoading(true);
        setError(null);

        try {
            const response = await cartridgeService.getUserStock();

            console.log(response);

            if (response.success) {
                setStock(response.data);
            } else {
                setError(response.error?.message || 'Failed to fetch data');
            }
        } catch (err: any) {
            setError(err.message || 'Unexpected error');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        await getUserStock(true);
    };

    useEffect(() => {
        getUserStock();
    }, []);

    return {
        stock,
        loading,
        error,
        refreshing,
        handleRefresh,
    };
};

export default useGetUserStockData;

