import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import SimpleBtn from '../../../components/Buttons/SimpleBtn'
import CartridgeUI from '../../../components/Icons/Cartridge/CartridgeUI'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import { useTheme } from '../../../context/ThemeContext'

interface InventoryCardProps {
  tableName:string,
  tableDetails:string,
  numTablets:number,
  cartidgeColor:string,
  tabletColor:string,
  onAddCartridge:()=>void,
}

const InventoryCard = ({tableName,tableDetails,numTablets,cartidgeColor,tabletColor,onAddCartridge}:InventoryCardProps) => {
  const { theme } = useTheme();
  return (
    <View style={[styles.container,
      {backgroundColor:theme.background.primary}
    ]}>
      <View>
        <Text style={[styles.tabletName,{
          color:cartidgeColor
        }]}>
            {tableName}
        </Text>
        <Text style={[styles.tabletDetailsText,{
          color:theme.text.tertiary
        }]}>
            {tableDetails}
        </Text>
        <Text style={[styles.tabletQuantityText,{
          color:theme.text.primary
        }]}>
            {numTablets.toString().padStart(2,'0')}
        </Text>
        <SimpleBtn
          title='Stock up>'
          onPress={onAddCartridge}
          containerBgColor='primary'
          titleTextColor='secondary'
          containerStyle={styles.stockUpBtnContainer}
        />
      </View>
      <View>
        <CartridgeUI cartidgeColor={cartidgeColor} tabletColor={tabletColor} numTablets={numTablets}/>
      </View>
    </View>
  )
}

export default InventoryCard

const styles = StyleSheet.create({
    container:{
      flexDirection:"row",
      justifyContent:"space-between",
      alignItems:"center",
      padding:24,
      borderRadius:16,
      elevation:4
    },
    tabletName:{
      fontSize:27,
      fontFamily:AppFonts.HelixaBold,
    },
    tabletDetailsText:{
      fontSize:14,
      fontFamily:AppFonts.HelixaBoldItalic,
    },
    tabletQuantityText:{
      fontSize:24,
      fontFamily:AppFonts.HelixaBlack,
      paddingVertical:6
    },
    stockUpBtnContainer:{
      width:"auto",
      alignSelf:"flex-start",
      paddingHorizontal:28
    }
})