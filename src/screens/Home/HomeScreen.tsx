import { Image, ScrollView, StyleSheet, Text, TouchableWithoutFeedback, View } from 'react-native';
import { useTheme } from '../../context/ThemeContext';
import AppLayout from '../../navigation/components/Layouts/AppLayout';
import useBluetoothStore from '../../store/BluetoothStore';
import DeviceInfoCard from './components/DeviceInfoCard';
import DoubleRings from './components/DoubleRings';
import PageLoader from '../../components/Loaders/PageLoader';
import { useScreenInteractable } from '../../hooks/misc/useScreenInteractable';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { MaterialCommunityIcons } from '@expo/vector-icons';
// import CarousalTabletDispenseCard from './components/TabletDispenseCardCarousal';
import TabletDispenseModal from '../../components/Modals/Tablet/TabletDispenseModal';
import { useEffect, useState } from 'react';
import useDispenseStore from '../../store/DispenseStore';
// import SkeletonItem from '../../components/SkeletonLoader/AdvancedSkeletonLoader';
import TabletDispenseCard from './components/TabletDispenseCard';

const BottleImg = require('../../../assets/full-bottle-img.png')

export default function HomeScreen() {
  const { theme } = useTheme();

  const isScreenLoaded = useScreenInteractable();

  const isConnected = useBluetoothStore(state => state.isConnected);
  const localDeviceName = useBluetoothStore(state => state.localDeviceName);
  const deviceName = useBluetoothStore(state => state.deviceName);
  
  // Cartridge details
  const cartrigeId1Details = useBluetoothStore(state => state.cartrigeId1Details);
  const loadingCartridgeId1Details = useBluetoothStore(state => state.loadingCartridgeId1Details);
  // const cartrigeId2Details = useBluetoothStore(state => state.cartrigeId2Details);
  // const loadingCartridgeId2Details = useBluetoothStore(state => state.loadingCartridgeId2Details); 

  const [ currentlyShownCartridgeDetails, setCurrentlyShownCartridgeDetails ] = useState<any | null>(cartrigeId1Details);

  // const cartridgeData = [
  //   {
  //     ...cartrigeId1Details, 
  //     id: 1, 
  //     isLoading: loadingCartridgeId1Details
  //   },
  //   {
  //     ...cartrigeId2Details, 
  //     id: 2, 
  //     isLoading: loadingCartridgeId2Details 
  //   }
  // ];

  useEffect(() => {
    setCurrentlyShownCartridgeDetails(cartrigeId1Details);
  }, [cartrigeId1Details]);
  
  // const hasError = !loadingCartridgeId1Details && !loadingCartridgeId2Details && !cartrigeId1Details && !cartrigeId2Details;

  const isFetchingTodayDoseStatus = useDispenseStore(state=>state.isFetchingTodayDoseStatus);
  const hasTakenToday = useDispenseStore(state=>state.hasTakenToday);
  const getTodayDoseStatus = useDispenseStore(state=>state.getTodayDoseStatus);

  useEffect(()=>{
    getTodayDoseStatus();
  },[])

  if(!isScreenLoaded) return (
    <View style={{flex:1,justifyContent:"center",alignItems:"center"}}>
      <PageLoader overlayBg='trasparent'/>
    </View>
  );

  return (
    <AppLayout>
      <TouchableWithoutFeedback>
        <ScrollView 
          contentContainerStyle={[styles.container, {
            backgroundColor: theme.background.secondary,
            paddingBottom:90
          }]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps='handled'
        >
          <View style={styles.innerContainer}>
            <DeviceInfoCard localName={localDeviceName} deviceName={deviceName} isConnected={isConnected}/>
            <View>
              <Image source={BottleImg} style={[styles.bottleImg]}/>
              <View style={styles.doubleRingsContainer}>
                <DoubleRings 
                  outerRingColor='primary' 
                  innerRingColor='quinary' 
                  currentlyShownCartridgeColor={currentlyShownCartridgeDetails?.cartridgeDetails?.colour?.cartridge}
                  hasTakenToday={hasTakenToday}
                />
              </View>
              {
                <View style={styles.dailyDoseContainer}>
                  <Text style={[styles.dailyDoseText,{color:theme.text.primary}]}>{
                    isFetchingTodayDoseStatus?"Fetching today's dose status...":"Daily dose"
}</Text>
                  {
                    !isFetchingTodayDoseStatus&&(
                      hasTakenToday ? (
                      <MaterialCommunityIcons name="checkbox-marked-circle" size={24} color={theme.toast.success} />
                    ) : (
                      <MaterialCommunityIcons name="close-circle" size={24} color={theme.toast.error} />
                    )
                    )
                  }
                </View>
                
              }
            </View>

            {/* <CarousalTabletDispenseCard 
              data={cartridgeData}
              error={hasError ? "No cartridges available" : null}
              setCurrentlyShownCartridgeDetails={(cartridgeDetails) => setCurrentlyShownCartridgeDetails(cartridgeDetails)}
            /> */}

            <TabletDispenseCard cartridgeId={cartrigeId1Details?.cartridgeId} loading={loadingCartridgeId1Details} cartridgeDetails={cartrigeId1Details}/>
              
            <TabletDispenseModal/>
          </View>
        </ScrollView>
      </TouchableWithoutFeedback>
    </AppLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    gap: 10,
  },
  innerContainer:{
    marginTop:8
  },
  bottleImg:{
    width:"100%",
    height:"auto",
    aspectRatio: 418/470,
    resizeMode: "contain",
    zIndex:20,
  },
  doubleRingsContainer:{
    position:"absolute",
    top:0,
    bottom:0,
    left:0,
    right:0,
    justifyContent:"center",
    alignItems:"center",
    zIndex:10
  },
  dailyDoseContainer:{
    flexDirection:"row",
    alignItems:"center",
    justifyContent:'center',
    gap:4,
    marginBottom:16
  },
  dailyDoseText:{
    fontSize:14,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center",
  }
});
