import React, { memo, useRef, useState } from 'react';
import { StyleSheet, Text, View, Dimensions, TouchableOpacity } from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import TabletDispenseCard from './TabletDispenseCard';
import { useTheme } from '../../../context/ThemeContext';
import { AppFonts } from '../../../constants/theme/fonts/fonts';

const { width } = Dimensions.get('window');

interface TabletDispenseCardCarousalProps {
    data: any[];
    error?: string | null;
    setCurrentlyShownCartridgeDetails: (cartridgeDetails: any) => void;
}

const TabletDispenseCardCarousal = ({ data,error = null,setCurrentlyShownCartridgeDetails }: TabletDispenseCardCarousalProps) => {
    const { theme } = useTheme();

    const [currentIndex, setCurrentIndex] = useState(0);
    const carouselRef = useRef<any>(null);
    
    const handlePrev = () => {
        carouselRef.current?.prev();
    };

    const handleNext = () => {
        carouselRef.current?.next();
    };
    
    return (
        <View style={styles.container}>
            <Carousel
                ref={carouselRef}
                width={width - 24}
                height={200}
                data={error ? [{}] : data}
                autoPlay= {false}
                enabled={!error}
                autoPlayInterval={3000}
                scrollAnimationDuration={800}
                onProgressChange={(progress, absoluteProgress) => {
                    if (!error && data.length > 0) {
                        setCurrentIndex(Math.round(absoluteProgress) % data.length);
                        setCurrentlyShownCartridgeDetails(data[Math.round(absoluteProgress) % data.length]);
                    }
                }}
                renderItem={({ item }) => {
                    const cartridgeId = (item as any).cartridgeId;
                    const isLoading = (item as any).isLoading;
                    
                    return (
                        error ? (
                            <View style={{ width:'100%',height:'auto', paddingHorizontal: 4,aspectRatio:16/7 }}>
                                <View style={[styles.card, { justifyContent: "center", alignItems: "center" }]}>
                                    <Text style={[styles.errorText,{color:theme.text.secondary}]}>{error}</Text>
                                    <Text style={[styles.errorText2,{color:theme.text.secondary}]}>Please insert the cartridge and try again. If it’s already inserted, kindly wait a moment before retrying.</Text>
                                </View> 
                            </View>
                        ) : (
                            <View style={{ flex: 1, paddingHorizontal: 4 }}>
                                <TabletDispenseCard
                                    cartridgeId={cartridgeId}
                                    loading={isLoading}
                                    cartridgeDetails={item as any}
                                />
                            </View>
                        )
                    );
                }}
                style={{ alignSelf: 'center' }}
            />
        </View>
    );
};

export default memo(TabletDispenseCardCarousal);

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        height: 200,
    },
    card: {
        flex: 1,
        backgroundColor: '#A0CA97',
        borderRadius: 16,
        padding: 16,
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorText: {
        color: '#FFFFFF',
        fontSize: 18,
        fontFamily:AppFonts.HelixaBlack,
        textAlign:"center"
    },
    errorText2: {
        color: '#FFFFFF',
        fontSize: 14,
        fontFamily:AppFonts.HelixaBoldItalic,
        textAlign:"center"
    }
});
