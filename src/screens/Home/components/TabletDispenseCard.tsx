import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import { useTheme } from '../../../context/ThemeContext';
import SimpleBtn from '../../../components/Buttons/SimpleBtn';
import { Feather, FontAwesome6 } from '@expo/vector-icons';
import useBluetoothStore from '../../../store/BluetoothStore';
import Toast from 'react-native-toast-message';
import SkeletonItem from '../../../components/SkeletonLoader/AdvancedSkeletonLoader';
import TabletRemainingIndicator from './TabletRemainingIndicator';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../../../navigation/MainStack';


type MainStackNavigationProp = NativeStackNavigationProp<MainStackParamList>;
interface TabletDispenseCardProps {
    cartridgeId:string,
    loading:boolean,
    cartridgeDetails:any,
}

const TabletDispenseCard = ({ cartridgeId,loading,cartridgeDetails }:TabletDispenseCardProps) => {
    const navigation = useNavigation<MainStackNavigationProp>();

    const { theme } = useTheme();
    const isConnected = useBluetoothStore(state => state.isConnected);
    const setDispensingTabletId = useBluetoothStore(state => state.setDispensingTabletId);

    if (loading) {
        return (
            <View style={styles.container}>
                {/* Title skeleton - "Vitals" */}
                <SkeletonItem
                    width="45%"
                    height={24}
                    borderRadius={6}
                    style={{ marginBottom: 6,marginTop:6 }}
                    isLoading={true}
                />
                
                {/* Subtitle skeleton - "Super Greens" */}
                <SkeletonItem
                    width="35%"
                    height={16}
                    borderRadius={4}
                    style={{ marginBottom: 3 }}
                    isLoading={true}
                />
                
                {/* Tablet indicators skeleton - 5 squares in a row */}
                <View style={[styles.cartridgeContainer, { 
                    flexDirection: 'row', 
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    marginBottom: 16,
                    gap: 8
                }]}>
                    {Array.from({ length: 5 }).map((_, index) => (
                        <SkeletonItem
                            key={index}
                            width={16}
                            height={16}
                            borderRadius={3}
                            isLoading={true}
                        />
                    ))}
                </View>
                
                {/* Dispense button skeleton */}
                <SkeletonItem
                    width="100%"
                    height={34}
                    borderRadius={22}
                    isLoading={true}
                />
            </View>
        );
    }
    
    return (
        <View style={[styles.container,
            {backgroundColor:cartridgeDetails?.cartridgeDetails?.colour?.tablet || '#c0b9b9ff'},
            {opacity:isConnected ? 1 : .6}
        ]}>
            <Text style={[styles.tabletName,{color:theme.text.secondary}]}>{cartridgeDetails?.cartridgeDetails?.healthName || "---"}</Text>
            <Text style={[styles.cartridgeText,{color:cartridgeDetails?.cartridgeDetails?.colour?.cartridge || theme.text.quinary}]}>{cartridgeDetails?.cartridgeDetails?.healthArea || "No cartridge detected"}</Text>
            <TabletRemainingIndicator numTablets={cartridgeDetails?.currentCount} color={cartridgeDetails?.cartridgeDetails?.colour?.cartridge}/>
            <SimpleBtn 
                title='Dispense' 
                onPress={()=>{
                    // if(!isConnected) {
                    //     Toast.show({
                    //         type: 'error',
                    //         text1: 'Connect Device',
                    //         text2: "Device not connected",
                    //         position: 'bottom'
                    //     });
                    //     return;
                    // };
                    // if(!cartridgeId) {
                    //     Toast.show({
                    //         type: 'error',
                    //         text1: 'No Cartridge',
                    //         text2: "Please select a valid cartridge",
                    //         position: 'bottom'
                    //     });
                    //     return;
                    // }
                    // setDispensingTabletId(cartridgeId);
                    navigation.navigate('Tablet Tracking',{cartridgeModalId:'68d2447c8fe5706e17bffb6b'});
                }} 
                containerBgColor='primary' titleTextColor='secondary'
                titleTextStyle={styles.dispenseBtnText}
                containerStyle={styles.dispenseBtnContainer}
                icon={(
                    <View style={[styles.dispenseIconContainer,{borderColor:theme.text.secondary}]}>
                        <FontAwesome6 name="arrow-down-long" size={8} color={theme.text.secondary} style={styles.dispenseArrowIcon }/>
                    </View>
                )}
                // disabled={cartridgeDetails?.cartridgeDetails==null}
                // disabledBgColor='tertiary'
                // disabledTitleColor='secondary'
            />
            <View style={[styles.scheduleTabletContainer,{
                backgroundColor:cartridgeDetails?.cartridgeDetails==null?theme.background.tertiary:theme.background.primary
            }]}>
                <Feather name="clock" size={22} color={cartridgeDetails?.cartridgeDetails?.colour?.cartridge || theme.text.secondary} />
            </View>
        </View>
    )
}

export default TabletDispenseCard

const styles = StyleSheet.create({
    container:{
        borderRadius:16,
        padding:8,
        paddingHorizontal:16,
        overflow: 'hidden',
        backgroundColor:'#A0CA97',
        alignItems:'center',
        paddingBottom:12,
        elevation:2
    },
    tabletName:{
        fontSize:24,
        fontFamily:AppFonts.HelixaBlack,
    },
    cartridgeText:{
        fontSize:14,
        fontFamily:AppFonts.HelixaBlackItalic,
        marginTop:0
    },
    cartridgeContainer:{
        flexDirection:"row",
        gap:4,
        justifyContent:"center",
        marginVertical:14
    },
    cartridge:{
        height:16,
        width:"auto",
        aspectRatio:1,
        borderRadius:4,
        backgroundColor:"#578D4C",
        borderWidth:1,
        borderColor:'white'
    },
    dispenseIconContainer:{
        width:14,
        height:14,
        borderWidth:1.5,
        borderRadius:2
    },
    dispenseArrowIcon:{
        position:"absolute",
        top:"90%",
        left:"50%",
        transform: [{ translateX: '-50%' }],
    },
    dispenseBtnContainer:{
        width:"100%",
        paddingVertical:3,
    },
    dispenseBtnText:{
        fontSize:15,
        fontFamily:AppFonts.HelixaBold,
    },
    scheduleTabletContainer:{
        position:"absolute",
        top:8,
        right:8,
        padding:5,
        borderRadius:50,
        elevation:2,
    }
})