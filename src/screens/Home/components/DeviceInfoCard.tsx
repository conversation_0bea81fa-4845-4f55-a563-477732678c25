import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { useTheme } from '../../../context/ThemeContext';
import { AppFonts } from '../../../constants/theme/fonts/fonts';
import { LinearGradient } from 'expo-linear-gradient';
import useBluetoothStore from '../../../store/BluetoothStore';

interface DeviceInfoCardProps {
    localName:string,
    deviceName:string,
    isConnected:boolean,
}

const DeviceInfoCard = ({
    localName,
    deviceName,
    isConnected
}:DeviceInfoCardProps) => {
    const { theme } = useTheme();
    const deviceBattery = useBluetoothStore(state => state.batteryPercentage) || 0;

    return (
        <View style={[styles.container,{
            backgroundColor:theme.background.primary,
            opacity:isConnected ? 1 : .6
        }]}>
            <View style={styles.innerContainer}>
                <View style={styles.deviceNameContainer}>
                    <Text style={[styles.localNameText,{color:theme.text.primary}]}>{localName}</Text>
                    <Text style={[styles.deviceNameText]}>{deviceName}</Text>
                </View>
                <View style={[styles.deviceBatteryContainer,]}>
                    <View style={[
                        styles.batteryTextWrapper,
                        {
                            borderColor:theme.border.senary
                        }
                    ]}>
                        <LinearGradient
                            colors={['transparent',theme.border.senary]}
                            locations={[deviceBattery/100,deviceBattery/100]}
                            start={{x: 0, y: 0}}
                            end={{x: 1, y: 0}}                     
                            style={[[styles.batteryTextContainer]]}
                        >
                            <Text style={[styles.deviceBatteryText,{color:theme.text.primary,zIndex:0,opacity:0}]}>{`100%`}</Text>

                            <View style={{position:"absolute",top:'0%',left:'0%',right:0,bottom:0,alignItems:"center",justifyContent:"center",
                                zIndex:10}}>
                            <Text style={[styles.deviceBatteryText,{color:theme.text.primary}]}>{`${deviceBattery}%`}</Text>

                            </View>

                        </LinearGradient>
                    </View>
                    <View>
                        <View style={[styles.batteryTip,{
                            backgroundColor:theme.border.senary
                        }]}/>
                    </View>
                </View>
            </View>
        </View>
    )
}

export default DeviceInfoCard

const styles = StyleSheet.create({
    container:{
        borderRadius:1000,
        padding:8,
        paddingHorizontal:16,
        gap:8,
        overflow: 'hidden',
    },
    innerContainer:{
        flexDirection:"row",
        justifyContent:"space-between",
        alignItems:"center",
        elevation:4,
    },
    deviceNameContainer:{
        gap:4
    },
    localNameText:{
        fontSize:16,
        fontFamily:AppFonts.HelixaBold,
    },
    deviceNameText:{
        fontSize:11,
        fontFamily:AppFonts.HelixaRegular,
    },
    deviceBatteryContainer:{
        flexDirection:"row",
        alignItems:"center",
    },
    batteryTextWrapper:{
        overflow:"hidden",
        borderRadius:12,
        borderWidth:2,
        // width:60
    },
    batteryTextContainer:{
        padding:8,
        paddingHorizontal:16,
    },
    deviceBatteryText:{
        fontSize:12,
        fontFamily:AppFonts.HelixaBold,
        textAlign:"center",
        // left:-2
    },
    batteryTip: {
        width: 6,
        height: 16,
        borderTopRightRadius: 4,
        borderBottomRightRadius: 4,
        marginLeft: -2,
    },
})