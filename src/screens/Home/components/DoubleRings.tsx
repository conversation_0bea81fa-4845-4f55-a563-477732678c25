import { StyleSheet, View } from 'react-native'
import React from 'react'
import { Theme } from '../../../constants/theme/colors'
import { useTheme } from '../../../context/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';

interface DoubleRingsProps {
  outerRingColor:keyof Theme["border"],
  innerRingColor:keyof Theme["border"],
  currentlyShownCartridgeColor:string | null,
  hasTakenToday:boolean
}
const DoubleRings = ({outerRingColor, innerRingColor, currentlyShownCartridgeColor,hasTakenToday}:DoubleRingsProps) => {
  const { theme } = useTheme();

  return (
    <View style={[
      styles.container,{
        // borderColor:theme.border[outerRingColor]
        borderColor:'#51634E'
      }
    ]}>
      <View style={[styles.innerContainer,{
        borderColor:hasTakenToday? theme.border[innerRingColor] : theme.border[innerRingColor] + '4D',
      }]}>
        {currentlyShownCartridgeColor ? (
          <LinearGradient
            colors={['transparent', currentlyShownCartridgeColor, 'transparent']}
            locations={[0, 0.5, 1]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.currentlyShownCartridgeColor}
          />
        ) : (
          <View style={styles.currentlyShownCartridgeColor} />
        )}
      </View>
      
    </View>
  )
}

export default DoubleRings

const styles = StyleSheet.create({
  container:{
    width:"80%",
    height:"auto",
    aspectRatio: 1,
    resizeMode: "contain",
    borderWidth:5,
    borderRadius:1000,
    justifyContent:"center",
    alignItems:"center",
    backgroundColor:"transparent"
  },
  innerContainer:{
    width:"98%",
    height:"auto",
    aspectRatio: 1,
    resizeMode: "contain",
    borderWidth:5,
    borderRadius:1000,
    backgroundColor:"transparent",
    overflow:"hidden"
  },
  currentlyShownCartridgeColor:{
    width:"100%",
    height:"100%",
    opacity:0.3
  }
})