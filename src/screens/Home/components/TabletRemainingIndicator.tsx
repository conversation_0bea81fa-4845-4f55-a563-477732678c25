import { StyleSheet, Text, View } from 'react-native'
import React from 'react'

interface TabletRemainingIndicatorProps {
    numTablets:number,
    color:string,
}

const TabletRemainingIndicator = ({numTablets,color}:TabletRemainingIndicatorProps) => {
  return (
    <View style={styles.cartridgeContainer}>
        {
            Array.from({ length: 5 }, (_, index) => (
                <View key={index} style={[styles.cartridge,
                    {backgroundColor:color?color:'transparent'},
                    {
                        opacity:numTablets>=index+1?1:0.5
                    }
                ]}/>
            ))
        }
    </View>
  )
}

export default TabletRemainingIndicator

const styles = StyleSheet.create({
    
    cartridgeContainer:{
        flexDirection:"row",
        gap:4,
        justifyContent:"center",
        marginVertical:14
    },
    cartridge:{
        height:16,
        width:"auto",
        aspectRatio:1,
        borderRadius:4,
        borderWidth:1,
        borderColor:'white'
    },
})