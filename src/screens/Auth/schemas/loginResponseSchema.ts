import { z, infer as zInfer } from 'zod';

const userSchema = z.object({
    id: z.string(),
    email: z.email(),
    firstName: z.string(),
    lastName: z.string(),
    isEmailVerified: z.boolean(),
    isAccountCompleted: z.boolean(),
    isDeleted: z.boolean(),
    dob: z.string().optional(),
    gender: z.string().optional(),
    height: z.number().optional(),
    weight: z.number().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    country: z.string().nullable(),
    activityLevel: z.string().optional(),
    profilePic: z.string().optional().nullable(),
});

const loginResponseSchema = z.object({
    error: z.boolean(),
    statusCode: z.number(),
    msg: z.string(),
    accessToken: z.string(),
    refreshToken: z.string(),
    expiry: z.iso.datetime().transform((val) => new Date(val)),
    user: userSchema,
});

export default loginResponseSchema;

export type userSchemaType = zInfer<typeof userSchema>;
export type loginResponseSchemaType = zInfer<typeof loginResponseSchema>;