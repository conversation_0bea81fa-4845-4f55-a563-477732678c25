import { Image, Keyboard, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import InputBoxWithLabel from '../../components/FormFields/InputBoxWithLabel';
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthStackParamList } from '../../navigation/AuthStack';
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import useUserStore from '../../store/UserStore';
import Toast from 'react-native-toast-message';
import PageLoader from '../../components/Loaders/PageLoader';

const AppLogo = require('../../../assets/app_logo_resized.png')

type RoutesNavigationProp = NativeStackNavigationProp<
  AuthStackParamList, 
  'Login' | 'Register' | 'ForgotPassword'| 'ResendVerificationEmail'
>;

type ValidationErrorType = {
  email?:string,
  password?:string
}

const LoginScreen = () => {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<RoutesNavigationProp>();

  const isLoggingUser = useUserStore(state=>state.isLoggingUser);
  const userError = useUserStore(state=>state.userError);
  const signIn = useUserStore(state=>state.signIn);
  const clearUserError = useUserStore(state=>state.clearUserError);

  const { theme } = useTheme();

  const [ email,setEmail ] = useState('');
  const [ password,setPassword ] = useState('');
  const [ validationError,setValidationError ] = useState<ValidationErrorType | null>(null);

  const validatePassword = (password:string) => {
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*\W)[A-Za-z\d\W]{8,}$/;
    return regex.test(password);
  };

  const validateEmail = (email:string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const validateLogin = () => {
    setValidationError(null);
    if(!email || !validateEmail(email)) {
      setValidationError({
        email:"Please enter valid email."
      })
      return false ;
    }
    else if(!password || !validatePassword(password)){
      setValidationError({
        password:"Please enter valid password."
      })
      return false;
    }

    return true;
  }

  const handleSubmit = async () => {
    Keyboard.dismiss();
    if(validateLogin()){
      await signIn(email,password);
    }
  }

  const handleRegister = () => {
    Keyboard.dismiss();
    navigation.navigate("Register");
    setValidationError(null);
    setEmail("");
    setPassword("");
  }

  const handleForgotPassword = () => {
    Keyboard.dismiss();
    navigation.navigate("ForgotPassword",{email});
  }

  useEffect(() => {
    if(userError){
      if(userError.status === 403){
        Toast.show({
          type: 'customWithAction',
          text1: "Error",
          text2: userError.message,
          position: 'bottom',
          props: {
            type:"error",
            onPress: ()=>{
              navigation.navigate("ResendVerificationEmail",{email});
              clearUserError();
              Toast.hide();
            },
            linkText: 'Resend verification email',
          },
        })
      }
      else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: userError.message,
          position: 'bottom'
        });
      }
      clearUserError();
    }
  }, [userError]);

  return (
    <View 
      style={[styles.container,{
          paddingTop: insets.top+32,
          paddingBottom: insets.bottom+24,
          paddingLeft: insets.left+16,
          paddingRight: insets.right+16,
          backgroundColor:theme.background.secondary
      }]}
    >
      <View style={styles.heading}> 
        <Image source={AppLogo} style={styles.appLogoImage} resizeMode='contain'/>
      </View>
      <View style={[styles.innerContainer,{
        backgroundColor:theme.background.primary
      }]}>
        <PageLoader visible={isLoggingUser} overlayBg='trasparent'/>
        <Text style={styles.loginHeading}>Log in</Text>
        <InputBoxWithLabel 
          label='Email' 
          selected={email} 
          setSelected={(value)=>{
            setEmail(value)
          }}
          error={validationError?.email}
          clearValidationError={()=>{
            setValidationError(prev=>({
              ...prev,
              email:undefined
            }))
          }}
        />
        <InputBoxWithLabel 
          label='Password' 
          selected={password} 
          setSelected={(value)=>{
            setPassword(value)
          }}
          error={validationError?.password}
          containerStyle={{
            marginTop:16
          }}
          secure={true}
          clearValidationError={()=>{
            setValidationError(prev=>({
              ...prev,
              password:undefined
            }));
          }}
        />
        <TouchableOpacity
          activeOpacity={.5}
          onPress={handleForgotPassword}
          style={styles.forgotPassBtn}
        >
          <Text style={styles.forgotPassText}>Forgot password?</Text>
        </TouchableOpacity>
        <View style={styles.authBtnContainer}>
          <SimpleBtn 
            title='Register' 
            onPress={handleRegister}
            containerBgColor='secondary'
            titleTextColor='primary'
            containerStyle={{
              flex:1,
            }}
            disabled={isLoggingUser}
            disabledBgColor='secondary'
            disabledTitleColor='primary'
          />
          <SimpleBtn 
            title='Login' 
            onPress={handleSubmit} 
            containerStyle={{
              flex:1,
            }}
            disabled={isLoggingUser}
            disabledBgColor='primary'
            disabledTitleColor='secondary'
          />
        </View>
      </View>
    </View>
  )
}

export default LoginScreen

const styles = StyleSheet.create({
  container : {
    flex:1,
  },
  heading : {
    paddingTop:48,
    paddingBottom:16,
    justifyContent:"center",
    alignItems:"center",
  },
  appLogoImage : {
    width:'50%',
    height:"auto",
    aspectRatio:201/49,
    resizeMode:"contain",
    marginBottom:2
  },
  innerContainer: {
    flex:1,
    borderRadius:20,
    padding:24,
  },
  loginHeading : {
    fontSize:24,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center",
    marginBottom:16
  },
  forgotPassBtn: {
    alignSelf:"flex-end",
    marginTop:4
  },
  forgotPassText:{
    fontSize:13,
    textAlign:"right",
    textDecorationLine:'underline',
    fontFamily:AppFonts.HelixaBoldItalic
  },
  authBtnContainer:{
    flexDirection:"row",
    marginTop:32,
    gap:32
  },
})