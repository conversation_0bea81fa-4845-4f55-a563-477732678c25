import { Image, Keyboard, KeyboardAvoidingView, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import InputBoxWithLabel from '../../components/FormFields/InputBoxWithLabel';
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthStackParamList } from '../../navigation/AuthStack';
import { useTheme } from '../../context/ThemeContext';
import BackBtn from '../../components/Buttons/BackBtn';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import CustomModal from '../../components/Modals/CustomModal';
import { Fontisto } from '@expo/vector-icons';
import authService from '../../apis/auth/authService';
import Toast from 'react-native-toast-message';
import PageLoader from '../../components/Loaders/PageLoader';
const AppLogo = require('../../../assets/app_logo_resized.png')

type RoutesNavigationProp = NativeStackNavigationProp<
  AuthStackParamList, 
  'Login'|'ForgotPassword'
>;

type ValidationErrorType = {
  email?:string,
}

const ForgotPasswordScreen = () => {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<RoutesNavigationProp>();
  const { email } = useRoute<RouteProp<AuthStackParamList, 'ForgotPassword'>>().params;

  const { theme } = useTheme();

  const [ userEmail,setUserEmail ] = useState( email || "" );
  const [ validationError,setValidationError ] = useState<ValidationErrorType | null>(null);

  const [ isSendingEmail,setIsSendingEmail ] = useState(false);
  const [ isModalVisible,setIsModalVisible ] = useState(false);

  const validateEmail = (email:string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const validateForgotPassword = () => {
    if(!userEmail || !validateEmail(userEmail)) {
      setValidationError({
        email:"Please enter valid email."
      })
      return false ;
    }
    return true;
  }

  const handleSubmit = async () => {
    Keyboard.dismiss();
    if(validateForgotPassword()){
      setIsSendingEmail(true);
      const response = await authService.forgotPassword({email:userEmail});
      setIsSendingEmail(false);

      if(response.success){
        setIsModalVisible(true);
      }
      else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: response.error.message,
          position: 'bottom'
        });
      }
      
    }
  }

  return (
    <KeyboardAvoidingView 
      enabled
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'} 
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -38}
      style={[styles.wrapper,{
        paddingTop: insets.top+32,
        paddingBottom: insets.bottom+24,
        paddingLeft: insets.left+16,
        paddingRight: insets.right+16,
        backgroundColor:theme.background.secondary
      }]}
    >
      <ScrollView 
        keyboardShouldPersistTaps='handled'
        style={[styles.container,{
          backgroundColor:theme.background.secondary
        }]}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{flexGrow:1}}
      >
        <View style={styles.heading}> 
          <Image source={AppLogo} style={styles.appLogoImage} resizeMode='contain'/>
        </View>
        <View style={[styles.innerContainer,{
          backgroundColor:theme.background.primary
        }]}>
          <PageLoader visible={isSendingEmail} overlayBg='trasparent'/>
          <View style={styles.innerContainerHeader}>
            <BackBtn onPress={navigation.goBack}/>
            <Text style={styles.forgotPassTextHeading}>Forgot Password</Text>
          </View>
          <InputBoxWithLabel 
            label='Email' 
            selected={userEmail} 
            setSelected={(value)=>{
              setUserEmail(value)
            }}
            error={validationError?.email}
            containerStyle={{
              marginTop:16
            }}
            clearValidationError={()=>{
              setValidationError(prev=>({
                ...prev,
                email:undefined
              }))
            }}
          />
          <View style={styles.submitBtnContainer}>
            <SimpleBtn 
              title='Submit' 
              onPress={handleSubmit} 
              containerBgColor='primary'
              titleTextColor='secondary'
              disabled={isSendingEmail}
            />
          </View>
          <CustomModal
            visible={isModalVisible}
            onClose={() => setIsModalVisible(false)}
            title="Forgot Password"
            subtitle="Password Reset"
            icon={
              <Fontisto name="email" size={120} color={theme.icon.primary}
                style={{
                  alignSelf: "center"
                }}
              />
            }
            desc="Please check your email for password reset instructions."
            buttons={
              <View style={{}}>
                <SimpleBtn
                  title='OK'
                  onPress={() => {
                    setIsModalVisible(false)
                    navigation.goBack();
                  }}
                  containerStyle={{
                    // flex: 1,
                  }}
                />
              </View>
            }
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}

export default ForgotPasswordScreen

const styles = StyleSheet.create({
  wrapper:{
    flex:1,
  },
  container : {
    flex:1,
  },
  heading : {
    paddingTop:48,
    paddingBottom:16,
    justifyContent:"center",
    alignItems:"center",
  },
  appLogoImage : {
    width:'50%',
    height:"auto",
    aspectRatio:201/49,
    resizeMode:"contain",
    marginBottom:2
  },
  innerContainer: {
    flex:1,
    borderRadius:19,
    padding:24,
  },
  innerContainerHeader: {
    marginBottom:16
  },
  forgotPassTextHeading : {
    fontSize:24,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center",
  },
  forgotPassText:{
    fontSize:13,
    fontWeight:400,
    textAlign:"right",
    fontStyle: "italic",
    textDecorationLine:'underline'
  },
  submitBtnContainer:{
    flexDirection:"row",
    marginTop:48,
    gap:32
  }
})