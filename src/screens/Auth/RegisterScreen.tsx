import { Image, KeyboardAvoidingView, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import InputBoxWithLabel from '../../components/FormFields/InputBoxWithLabel';
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthStackParamList } from '../../navigation/AuthStack';
import CheckBoxWithText from '../../components/FormFields/CheckBoxWithText';
import { useTheme } from '../../context/ThemeContext';
import BackBtn from '../../components/Buttons/BackBtn';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import authService from '../../apis/auth/authService';
import Toast from 'react-native-toast-message';
import PageLoader from '../../components/Loaders/PageLoader';
const AppLogo = require('../../../assets/app_logo_resized.png')

type RoutesNavigationProp = NativeStackNavigationProp<
  AuthStackParamList, 
  'Login'
>;

type ValidationErrorType = {
  firstName?:string,
  lastName?:string,
  email?:string,
  password?:string,
  confirmPassword?:string,
  hasAcceptedTerms?:string
}

const RegisterScreen = () => {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<RoutesNavigationProp>();

  const { theme } = useTheme();

  const [ firstName,setFirstName ] = useState('');
  const [ lastName,setLastName ] = useState('');
  const [ email,setEmail ] = useState('');
  const [ password,setPassword ] = useState('');
  const [ confirmPassword,setConfirmPassword ] = useState('');
  const [ hasAcceptedTerms,setHasAcceptedTerms ] = useState(false);

  const [ validationError,setValidationError ] = useState<ValidationErrorType | null>(null);

  const [ isRegisteringUser,setIsRegisteringUser ] = useState(false);

  const validateFirstName = (firstName:string) => {
    return  firstName.length >= 3;
  };

  const validateLastName = (lastName:string) => {
    return  lastName.length >= 3;
  };

  const validateEmail = (email:string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };
  
  const validatePassword = (password:string) => {
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*\W)[A-Za-z\d\W]{8,}$/;
    return regex.test(password);
  };


  const validateRegister = () => {
    if(!firstName || !validateFirstName(firstName)) {
      setValidationError({
        firstName:"Please enter valid first name."
      })
      return false ;
    }
    if(!lastName || !validateLastName(lastName)) {
      setValidationError({
        lastName:"Please enter valid last name."
      })
      return false ;
    }
    if(!email || !validateEmail(email)) {
      setValidationError({
        email:"Please enter valid email."
      })
      return false ;
    }
    else if(!password){
      setValidationError({
        password:"Please enter valid password."
      })
      return false;
    }
    else if(validatePassword(password) === false){
      setValidationError({
        password:"Password should contain at least one uppercase, one lowercase, one number, one special character and should be minimum 8 characters long."
      })
      return false;
    }
    else if(password !== confirmPassword){
      setValidationError({
        confirmPassword:"Password and confirm password should be same."
      })
      return false;
    }
    else if(!hasAcceptedTerms){
      setValidationError({
        hasAcceptedTerms:"Please accept terms and conditions."
      })
      return false;
    }

    return true;
  }

  const handleSubmit = async () => {
    if(validateRegister()){
      setIsRegisteringUser(true);
      const response = await authService.register({ firstName, lastName, email, password, acceptTerms: hasAcceptedTerms, timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone });

      if(response.success){
        Toast.show({
          type: 'success',
          text1: 'Success',
          text2: 'User registered successfully. Please check your email to verify your account.',
          position: 'bottom'
        });
        navigation.goBack();
      }
      else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: response.error.message,
          position: 'bottom'
        });
      }
      setIsRegisteringUser(false);
    }
  }

  return (
    <KeyboardAvoidingView 
      enabled
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'} 
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -38}
      style={[styles.wrapper,{
        paddingTop: insets.top+32,
        paddingBottom: insets.bottom+24,
        paddingLeft: insets.left+16,
        paddingRight: insets.right+16,
        backgroundColor:theme.background.secondary
      }]}
    >
      <ScrollView 
        keyboardShouldPersistTaps='handled'
        style={[styles.container,{
          backgroundColor:theme.background.secondary
        }]}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{flexGrow:1}}
      >
        <View style={styles.heading}> 
          <Image source={AppLogo} style={styles.appLogoImage} resizeMode='contain'/>
        </View>
        {
          <View style={[styles.innerContainer,{
            backgroundColor:theme.background.primary
          }]}>
            <PageLoader visible={isRegisteringUser} overlayBg='trasparent'/>
            <View style={styles.innerContainerHeader}>
              <BackBtn onPress={navigation.goBack}/>
              <Text style={styles.registerHeading}>Register</Text>
            </View>
            <InputBoxWithLabel 
              label='First Name' 
              selected={firstName} 
              setSelected={(value)=>{
                setFirstName(value)
              }}
              error={validationError?.firstName}
              clearValidationError={()=>{
                setValidationError(prev=>({
                  ...prev,
                  firstName:undefined
                }))
              }}
              containerStyle={{
                marginTop:8
              }}
            />
            <InputBoxWithLabel 
              label='Last Name' 
              selected={lastName} 
              setSelected={(value)=>{
                setLastName(value)
              }}
              error={validationError?.lastName}
              containerStyle={{
                marginTop:16
              }}
              clearValidationError={()=>{
                setValidationError(prev=>({
                  ...prev,
                  lastName:undefined
                }));
              }}
            />
            <InputBoxWithLabel 
              label='Email' 
              selected={email} 
              setSelected={(value)=>{
                setEmail(value)
              }}
              error={validationError?.email}
              containerStyle={{
                marginTop:16
              }}
              clearValidationError={()=>{
                setValidationError(prev=>({
                  ...prev,
                  email:undefined
                }))
              }}
            />

            <InputBoxWithLabel 
              label='Password' 
              selected={password} 
              setSelected={(value)=>{
                setPassword(value)
              }}
              error={validationError?.password}
              containerStyle={{
                marginTop:16
              }}
              secure={true}
              clearValidationError={()=>{
                setValidationError(prev=>({
                  ...prev,
                  password:undefined
                }));
              }}
            />

            <InputBoxWithLabel 
              label='Confirm Password' 
              selected={confirmPassword} 
              setSelected={(value)=>{
                setConfirmPassword(value)
              }}
              error={validationError?.confirmPassword}
              containerStyle={{
                marginTop:16
              }}
              secure={true}
              clearValidationError={()=>{
                setValidationError(prev=>({
                  ...prev,
                  confirmPassword:undefined
                }));
              }}
            />
            <CheckBoxWithText  
              title='I have read and agreed to the terms and conditions.'
              checked={hasAcceptedTerms}  
              setChecked={setHasAcceptedTerms}
              containerStyle={{
                marginTop:16
              }}
              error={validationError?.hasAcceptedTerms}
              clearValidationError={()=>{
                setValidationError(prev=>({
                  ...prev,
                  hasAcceptedTerms:undefined
                }));
              }}
            />
            <View style={styles.confirmBtnContainer}>
              <SimpleBtn 
                title='Create Account & Verify' 
                onPress={handleSubmit} 
                containerBgColor='primary'
                titleTextColor='secondary'
                disabled={isRegisteringUser}
              />
            </View>
          </View>
        }
      </ScrollView>
    </KeyboardAvoidingView>
  )
}

export default RegisterScreen

const styles = StyleSheet.create({
  wrapper:{
    flex:1,
  },
  container : {
    flex:1,
  },
  heading : {
    paddingTop:48,
    paddingBottom:16,
    justifyContent:"center",
    alignItems:"center",
  },
  appLogoImage : {
    width:'50%',
    height:"auto",
    aspectRatio:201/49,
    resizeMode:"contain",
    marginBottom:2
  },
  innerContainer: {
    flex:1,
    borderRadius:19,
    padding:24,
  },
  innerContainerHeader: {
    marginBottom:16
  },
  registerHeading : {
    fontSize:24,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center",
    paddingHorizontal:48,
  },
  forgotPassText:{
    fontSize:13,
    fontWeight:400,
    textAlign:"right",
    fontStyle: "italic",
    textDecorationLine:'underline'
  },
  confirmBtnContainer:{
    flexDirection:"row",
    marginTop:48,
    gap:32
  },
})