import { Image, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { useTheme } from '../../context/ThemeContext'
import useUserStore from '../../store/UserStore'
import convertDOBtoAge from '../../utils/convertDOBtoAge'
import { AppFonts } from '../../constants/theme/fonts/fonts'
import UserDetailCard from './components/UserDetailCard'
import { getActivityLevelLabel } from '../Onboarding/enums/ActivityLevelEnum'

const ProfilePicPlacehlder = require('../../../assets/profile-pic-placeholder.jpg')

const ViewProfileScreen = () => {
    const { theme } = useTheme();
    const user = useUserStore(state => state.user);
    return (
        <View>
        <View style={styles.profilePicContainer}>
            <Image source={
                user?.profilePic ? { uri: user?.profilePic } : ProfilePicPlacehlder
            } style={styles.profilePic} />
        </View>
        <Text style={[styles.userBasicInfoText,{
            color:theme.text.primary
        }]}>
            {`${user?.firstName} ${user?.lastName.toLowerCase()}, ${convertDOBtoAge(user?.dob)}yo`}
        </Text>
        <Text style={[styles.userEmailText,{
            color:theme.text.tertiary
        }]}>
            {`${user?.email}`}
        </Text>
        <View style={[styles.userDetailCardsContainer,{
            marginTop:48
        }]}>
            <UserDetailCard label='Gender' value={user?.gender?user?.gender[0].toUpperCase(): "-"} borderRight/>
            <UserDetailCard label='Height' value={user?.height ? `${user?.height} cm` : "-"} borderRight containerStyle={{
                flex:1.5
            }}/>
            <UserDetailCard label='Weight' value={user?.weight ? `${user?.weight} kg` : "-"} />
        </View>
        <View style={styles.userDetailCardsContainer}>
            <UserDetailCard label='City' value={user?.city || "-"} borderRight/>
            <UserDetailCard label='State' value={user?.state || "-"} />
        </View>
        <View style={styles.userDetailCardsContainer}>
            <UserDetailCard label='Activity Level' value={getActivityLevelLabel(user?.activityLevel)} />
        </View>
        </View>
    )
}

export default ViewProfileScreen

const styles = StyleSheet.create({
    profilePicContainer:{
        width:'70%',
        height:"auto",
        aspectRatio:1,
        alignSelf:"center",
    },
    profilePic:{
        width:"100%",
        height:"100%",
        borderRadius:1000,
        resizeMode:"cover"
    },
    userBasicInfoText:{
        fontSize:16,
        fontFamily:AppFonts.HelixaBold,
        textAlign:"center",
        marginTop:16,
    },
    userEmailText:{
        fontSize:12,
        fontFamily:AppFonts.HelixaRegular,
        textAlign:"center",
        marginTop:2,
    },
    userDetailCardsContainer:{
        marginTop:16,
        flexDirection:"row",
        flexWrap:"wrap",
        justifyContent:"space-between",
    }
})