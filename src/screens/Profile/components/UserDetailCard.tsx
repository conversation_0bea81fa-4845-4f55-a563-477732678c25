import { StyleSheet, Text, View, ViewStyle } from 'react-native'
import React from 'react'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import { useTheme } from '../../../context/ThemeContext';
import { ThemedStyleProp } from '../../../types/ThemeStyleType';

interface UserDetailCardProps {
    label:string,
    value:string,
    borderRight?:boolean,
    containerStyle?:ThemedStyleProp<ViewStyle,"backgroundColor">
}

const UserDetailCard = ({
    label,
    value,
    borderRight=false,
    containerStyle={}
}:UserDetailCardProps) => {
    const { theme } = useTheme();
  return (
    <View style={[styles.container,containerStyle,{
        borderRightWidth:borderRight ? 1 : 0,
        borderColor:theme.border.primary
    }]}>
        <Text style={[styles.label,{
            color:theme.text.tertiary
        }]}>
            {label}
        </Text>
        <Text style={[styles.value,{
            color:theme.text.primary
        }]}>
            {value}
        </Text>
    </View>
  )
}

export default UserDetailCard

const styles = StyleSheet.create({
    container:{
        flex:1,
        flexDirection:"column",
        alignItems:"center",
        justifyContent:"center",
        marginBottom:8
    },
    label:{
        fontSize:12,
        fontFamily:AppFonts.HelixaBold,
    },
    value:{
        fontSize:16,
        fontFamily:AppFonts.HelixaBold,
    }
})