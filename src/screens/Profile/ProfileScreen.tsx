import { Image, RefreshControl, ScrollView, StyleSheet, Text, TouchableWithoutFeedback, View } from 'react-native'
import React, { useEffect, useRef, useState } from 'react'
import AppLayout from '../../navigation/components/Layouts/AppLayout';
import { useTheme } from '../../context/ThemeContext';
import useUserStore from '../../store/UserStore';
import PageLoader from '../../components/Loaders/PageLoader';
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import ViewProfileScreen from './ViewProfileScreen';
import EditProfileScreen from './EditProfileScreen';
import CustomModal from '../../components/Modals/CustomModal';
import { useScreenInteractable } from '../../hooks/misc/useScreenInteractable';
import Toast from 'react-native-toast-message';
import MailSentModal from '../../components/Modals/Others/MailSentModal';

const AppIcon = require('../../../assets/icon.png')
   
type ValidationErrorType = {
  firstName?: string,
  lastName?: string,
  email?: string,
  dob?: string,
  gender?: string,
  city?: string,
  state?: string,
  height?: string,
  weight?: string,
  activityLevel?: string
}

const ProfileScreen = () => {
  const { theme } = useTheme();

  const isScreenLoaded  = useScreenInteractable();

  const user = useUserStore(state => state.user);
  const isLoggingOut = useUserStore(state => state.isLoggingOut);
  const isUpdatingUserProfile = useUserStore(state => state.isUpdatingUserProfile);
  const signOut = useUserStore(state => state.signOut);
  const updateUserProfile = useUserStore(state => state.updateUserProfile);

  const isLoadingUserProfile = useUserStore(state => state.isLoadingUserProfile);
  const getUserProfile = useUserStore(state => state.getUserProfile);
  const [ isFirstLoad,setIsFirstLoad ] = useState(true);

  const scrollViewRef = useRef<ScrollView>(null);

  const [ editingProfile,setEditingProfile ] = useState(false);
  const [ showLogoutModal, setShowLogoutModal ] = useState(false);
  const [ showMailSentModal, setShowMailSentModal ] = useState(false);

  const [localData, setLocalData] = useState<{
    image:string,
    firstName: string,
    lastName: string,
    email: string,
    dob?: string,
    gender?: string,
    city?: string | null,
    state?: string | null,
    country?: string | null,
    height?: number | string,
    weight?: number | string,
    activityLevel?: string
  }>({
    image: user?.profilePic || "",
    firstName:user?.firstName || "",
    lastName:user?.lastName || "",
    email:user?.email || "",
    dob:user?.dob || "",
    gender:user?.gender,
    city:user?.city,
    state:user?.state,
    country:user?.country,
    height:user?.height,
    weight:user?.weight,
    activityLevel:user?.activityLevel
  });

  const [ validationError, setValidationError ] = useState<ValidationErrorType>({});

  const [currentDropdownId, setCurrentDropdownId] = useState<string | null>(null);

  const cancelEdit = () => {
    setLocalData({
      image: user?.profilePic || "",
      firstName:user?.firstName || "",
      lastName:user?.lastName || "",
      email:user?.email || "",
      dob:user?.dob || "",
      gender:user?.gender,
      city:user?.city,
      state:user?.state,
      country:user?.country,
      height:user?.height,
      weight:user?.weight,
      activityLevel:user?.activityLevel
    });
    setEditingProfile(false);
    setValidationError({});
  }

  const validateForm = (): boolean => {
    const errors: ValidationErrorType = {};

    // Validate first name
    if (!localData.firstName || localData.firstName.trim() === '') {
      errors.firstName = 'First name is required';
      setValidationError(errors);
      return false;
    }

    // Validate last name
    if (!localData.lastName || localData.lastName.trim() === '') {
      errors.lastName = 'Last name is required';
      setValidationError(errors);
      return false;
    }

    // Validate email
    if (!localData.email || localData.email.trim() === '') {
      errors.email = 'Email is required';
      setValidationError(errors);
      return false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(localData.email)) {
      errors.email = 'Please enter a valid email address';
      setValidationError(errors);
      return false;
    }

    // Validate date of birth
    if (!localData.dob || localData.dob.trim() === '') {
      errors.dob = 'Date of birth is required';
      setValidationError(errors);
      return false;
    }

    // Validate gender
    if (!localData.gender || localData.gender.trim() === '') {
      errors.gender = 'Gender is required';
      setValidationError(errors);
      return false;
    }

    // Validate height
    if (!localData.height || localData.height === '') {
      errors.height = 'Height is required';
      setValidationError(errors);
      return false;
    } else if (typeof localData.height === 'number' && (localData.height < 50 || localData.height > 300)) {
      errors.height = 'Enter valid height';
      setValidationError(errors);
      return false;
    }

    // Validate weight
    if (!localData.weight || localData.weight === '') {
      errors.weight = 'Weight is required';
      setValidationError(errors);
      return false;
    } else if (typeof localData.weight === 'number' && (localData.weight < 20 || localData.weight > 500)) {
      errors.weight = 'Enter valid weight';
      setValidationError(errors);
      return false;
    }

    // Validate activity level
    if (!localData.activityLevel || localData.activityLevel.trim() === '') {
      errors.activityLevel = 'Activity level is required';
      setValidationError(errors);
      return false;
    }

    setValidationError(errors);
    return true;
  };

  const hasEditedDate = () => {
    if(localData.image!==user?.profilePic) return true;
    if(localData.firstName!==user?.firstName) return true;
    if(localData.lastName!==user?.lastName) return true;
    if(localData.email!==user?.email) return true;
    if(localData.dob!==user?.dob) return true;
    if(localData.gender!==user?.gender) return true;
    if(localData.city!==user?.city) return true;
    if(localData.state!==user?.state) return true;
    if(localData.country!==user?.country) return true;
    if(localData.height!==user?.height) return true;
    if(localData.weight!==user?.weight) return true;
    if(localData.activityLevel!==user?.activityLevel) return true;
    return false;
  }

  const handleSaveProfile = async () => {

    if(!hasEditedDate()) {
      setEditingProfile(false);
      return;
    };
    
    // Validate form first
    if (!validateForm()) {
      return;
    }

    const hasChangeMail = localData.email!==user?.email;

    // Prepare the data using the schema type
    const updateData = {
      firstName: localData.firstName,
      lastName: localData.lastName,
      email: localData.email,
      dob: localData.dob,
      gender: localData.gender,
      city: localData.city || undefined,
      state: localData.state || undefined,
      country: localData.country,
      height: typeof localData.height === 'number' ? localData.height : (localData.height ? Number(localData.height) : undefined),
      weight: typeof localData.weight === 'number' ? localData.weight : (localData.weight ? Number(localData.weight) : undefined),
      activityLevel: localData.activityLevel,
      // Add profile picture if selected
      profilePic: localData.image && localData.image !== '' ? {
        uri: localData.image,
        name: 'profile_pic.jpg',
        type: 'image/jpeg',
      } as any : undefined
    };

    const success = await updateUserProfile(updateData);
    
    if (success) {
      // Update local data with the latest user data from store
      const updatedUser = useUserStore.getState().user;
      if (updatedUser) {
        setLocalData({
          image: updatedUser?.profilePic || "",
          firstName: updatedUser.firstName || "",
          lastName: updatedUser.lastName || "",
          email: updatedUser.email || "",
          dob: updatedUser.dob || "",
          gender: updatedUser.gender,
          city: updatedUser.city,
          state: updatedUser.state,
          country: updatedUser.country,
          height: updatedUser.height,
          weight: updatedUser.weight,
          activityLevel: updatedUser.activityLevel
        });
      }
      setEditingProfile(false);

      if(hasChangeMail){
        setShowMailSentModal(true);
      }
      
      // Show success message
      // Toast.show({
      //   type: 'success',
      //   text1: 'Success',
      //   text2: 'Profile updated successfully',
      //   position: 'bottom'
      // });
    }
  }

  const scrollToOffset = (y: number) => {
    scrollViewRef.current?.scrollTo({ y: y, animated: true });
  };

  useEffect(() => {
    if (user) {
      setLocalData({
        image: user?.profilePic || "",
        firstName:user?.firstName || "",
        lastName:user?.lastName || "",
        email:user?.email || "",
        dob:user?.dob || "",
        gender:user?.gender,
        city:user?.city,
        state:user?.state,
        country:user?.country,
        height:user?.height,
        weight:user?.weight,
        activityLevel:user?.activityLevel
      });
    }
  }, [user]);

  useEffect(() => {
    if (isScreenLoaded && isFirstLoad) {
      (
        async () => {
          await getUserProfile();
          setIsFirstLoad(false);
        }
      )();
    }
  }, [isScreenLoaded]);

  if(!isScreenLoaded || (isLoadingUserProfile && isFirstLoad)) return <PageLoader/>

  return (
    <AppLayout>
      <View style={styles.wrapper}>
        <View style={[styles.container, {
          backgroundColor: theme.background.primary
        }]}>
          <PageLoader visible={isUpdatingUserProfile} overlayBg='trasparent'/>
          <TouchableWithoutFeedback onPress={() => {
            setCurrentDropdownId(null);
          }}>
            <ScrollView 
              ref={scrollViewRef}
              keyboardShouldPersistTaps='handled' 
              showsVerticalScrollIndicator={false}
              style={{flex:1}}
              contentContainerStyle={[styles.innerContainerWrapper,{
                paddingBottom:editingProfile ? 200 : 0
              }]}
              refreshControl={
                <RefreshControl
                  refreshing={isLoadingUserProfile && !isFirstLoad}
                  onRefresh={getUserProfile}
                  colors={[theme.button.primary]}
                  progressViewOffset={24}
                />
              }
            >
              <View style={[styles.innerContainer]}>
                <Text style={[
                  styles.mainHeading, {
                    color: theme.text.primary
                  }
                ]}>
                  Profile
                </Text>
                {editingProfile ? <EditProfileScreen localData={localData} setLocalData={setLocalData} currentDropdownId={currentDropdownId} setCurrentDropdownId={setCurrentDropdownId} scrollToOffset={scrollToOffset} validationError={validationError} setValidationError={setValidationError}/> : <ViewProfileScreen/>}
              </View>
            </ScrollView>
          </TouchableWithoutFeedback>
          {
            !editingProfile ? (
              <View style={styles.btnsContainer}>
                <SimpleBtn
                  title='Logout'
                  onPress={() => {
                    setShowLogoutModal(true);
                  }}
                  disabled={isLoggingOut}
                  containerBgColor='secondary'
                  titleTextColor='primary'
                  containerStyle={{
                    flex:1
                  }}
                />
                <SimpleBtn
                  title='Edit Profile'
                  onPress={() => {
                    setEditingProfile(true);
                  }}
                  containerStyle={{
                    flex:1
                  }}
                />
              </View>
            ):(
              <View style={styles.btnsContainer}>
                <SimpleBtn
                  title='Cancel'
                  onPress={cancelEdit}
                  containerBgColor='secondary'
                  titleTextColor='primary'
                  containerStyle={{
                    flex:1
                  }}
                />
                <SimpleBtn
                  title='Save'
                  onPress={handleSaveProfile}
                  disabled={isUpdatingUserProfile}
                  containerStyle={{
                    flex:1
                  }}
                />
              </View>
            )
          }
          <CustomModal
            isLoading={isLoggingOut}
            visible={showLogoutModal}
            onClose={() => setShowLogoutModal(false)}
            showBackBtn={false}
            title="Logout"
            subtitle="Confirmation"
            desc="Are you sure you want to logout from 'NOURIQ' app?You can login again with required credentials."
            icon={<Image source={AppIcon} style={[styles.logoutModalImage,{borderColor:theme.border.primary}]}/>}
            buttons={
              <View style={styles.logoutModalBtnsContainer}>
                <SimpleBtn
                  title='Cancel'
                  onPress={() => {
                    setShowLogoutModal(false);
                  }}
                  containerStyle={{
                    flex: 1,
                  }}
                  containerBgColor='secondary'
                  titleTextColor='primary'
                />
                <SimpleBtn
                  title='Confirm'
                  onPress={signOut}
                  containerStyle={{
                    flex: 1,
                  }}
                />
              </View>
            }
          />
        </View>
      </View>
      <MailSentModal
        visible={showMailSentModal}
        onClose={() => setShowMailSentModal(false)}
        title="Email Verification"
        subtitle="Sent"
        desc='A verification link has been sent to your new email. Please verify your email to continue.'
      />
    </AppLayout>
  )
}

export default ProfileScreen

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    padding:4,
    paddingTop:12,
    paddingBottom:90
  },
  container: {
    flex: 1,
    elevation:2,
    borderRadius:19,
    padding:18,
    paddingTop:20
  },
  innerContainerWrapper:{
    flexGrow:1,
  },
  innerContainer: {
    flex:1,
  },
  mainHeading: {
    fontSize: 24,
    fontFamily: AppFonts.HelixaBlack,
    textAlign: "left",
    marginBottom: 16,
  },
  btnsContainer:{
    flexDirection:"row",
    gap:16,
    marginTop:32
  },
  logoutModalBtnsContainer:{
    flexDirection:"row",
    gap:16,
    marginTop:6
  },
  logoutModalImage:{
    width:84,
    height:84,
    resizeMode:"contain",
    alignSelf:"center",
    borderWidth:1,
    borderRadius:20,
    marginVertical:12
  }
})