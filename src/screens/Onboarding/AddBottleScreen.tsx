import { Di<PERSON><PERSON>, FlatList, Image, KeyboardAvoidingView, Linking, Platform, RefreshControl, ScrollView, StyleSheet, Text, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import { useTheme } from '../../context/ThemeContext';
import BackBtn from '../../components/Buttons/BackBtn';
import useOnboardingStore from './store/OnboardingStore';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import SwitchBtn from '../../components/Buttons/SwitchBtn';
import { Device } from 'react-native-ble-plx';
import PageLoader from '../../components/Loaders/PageLoader';
import DeviceInputCard from '../../components/FormFields/DeviceInputCard';
import useBluetoothStore from '../../store/BluetoothStore';
import useUserStore from '../../store/UserStore';
import Toast from 'react-native-toast-message';
import { BluetoothStateManager } from 'react-native-bluetooth-state-manager';
import ScannedDevicesModal from '../../components/Modals/Bluetooth/ScannedDevicesModal';
import LocationPermissionModal from '../../components/Modals/Bluetooth/LocationPermissionModal';
import BluetoothPermissionModal from '../../components/Modals/Bluetooth/BluetoothPermissionModal';

const AppLogo = require('../../../assets/app_logo_resized.png')
const BTPermissionMobile = require('../../../assets/BT_permission_mobile.png')
const BottleImg = require('../../../assets/full-bottle-img.png')

const AddBottleScreen = () => {
    const insets = useSafeAreaInsets();

    const { theme } = useTheme();
    const dob = useOnboardingStore(state => state.dob);
    const gender = useOnboardingStore(state => state.gender);
    const country = useOnboardingStore(state => state.country);
    const city = useOnboardingStore(state => state.city);
    const state = useOnboardingStore(state => state.state);
    const height = useOnboardingStore(state => state.height);
    const weight = useOnboardingStore(state => state.weight);
    const activityLevel = useOnboardingStore(state => state.activityLevel);
    const completeOnboarding = useUserStore(state => state.completeOnboarding);
    const isCompletingOnboarding = useUserStore(state => state.isCompletingOnboarding);
    const clearUserDetails = useOnboardingStore(state => state.clearUserDetails);

    const goToPreviousStep = useOnboardingStore(state => state.goToPreviousStep);

    const bleManager = useBluetoothStore(state => state.bleManager);
    const isBluetoothConnected = useBluetoothStore(state => state.isBluetoothConnected);
    const setIsBluetoothConnected = useBluetoothStore(state => state.setIsBluetoothConnected);

    const setIsBluetoothPermissionModalVisible = useBluetoothStore(state => state.setIsBluetoothPermissionModalVisible);
    const setIsLocationPermissionModalVisible = useBluetoothStore(state => state.setIsLocationPermissionModalVisible);
    const requestBluetoothPermission = useBluetoothStore(state => state.requestBluetoothPermission);

    const handleGetAllDevices = useBluetoothStore(state => state.handleGetAllDevices);

    const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
    const connectingDevice = useBluetoothStore(state => state.connectingDevice);
    const connectedDevice = useBluetoothStore(state => state.connectedDevice);
    const removeDevice = useBluetoothStore(state => state.removeDevice);

    const localDeviceName = useBluetoothStore(state => state.localDeviceName);
    const editConnectDeviceLocalName = useBluetoothStore(state => state.editConnectDeviceLocalName);
    const [editingBottleName, setEditingBottleName] = useState(false);
    
    useEffect(() => {
        const subscription = bleManager.onStateChange((state) => {
            if (state === 'PoweredOn') {
                setIsBluetoothConnected(true);
            }
            else {
                setIsBluetoothConnected(false)
            }
        }, true);

        requestBluetoothPermission()

        return () => {
            subscription.remove()
        }
    }, [])

    const handleAddBottle = async () => {
        if(!isBluetoothConnected) return;

        await handleGetAllDevices();
    }

    const handleRemoveDevice = async () => {
        await removeDevice();
        setEditingBottleName(false);
        setSelectedDevice(null);
    }

    const handleOnboarding = async () => {
        if(!connectedDevice) return;
        if(!dob || !gender || !country || !city || !state || !height || !weight || !activityLevel) {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: 'Please fill all the details.',
                position: 'bottom'
            });
            return;
        }
        const success = await completeOnboarding({
            dob,
            gender,
            country,
            city,
            state,
            height,
            weight,
            activityLevel,
            deviceData: {
                deviceId: connectedDevice.id,
                deviceName: connectedDevice.name || connectedDevice.localName || connectedDevice.id,
                localName: localDeviceName,
            },
            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
        })

        if(success) {
            clearUserDetails();
        }
    }

    return (
        <KeyboardAvoidingView
            enabled
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -38}
            style={[styles.wrapper, {
                paddingTop: insets.top + 32,
                paddingBottom: insets.bottom + 24,
                paddingLeft: insets.left + 16,
                paddingRight: insets.right + 16,
                backgroundColor: theme.background.secondary
            }]}
        >
            <View style={styles.heading}>
                <Image source={AppLogo} style={styles.appLogoImage} resizeMode='contain' />
            </View>
            <View style={[styles.innerContainer, {
                backgroundColor: theme.background.primary
            }]}>
                <PageLoader visible={isCompletingOnboarding} overlayBg='trasparent'/>
                <ScrollView
                    keyboardShouldPersistTaps='handled'
                    style={[styles.container]}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ flexGrow: 1, padding: 24 }}
                >
                    <View style={styles.mainContainer}>
                        <View style={styles.innerContainerHeader}>
                            <BackBtn onPress={goToPreviousStep} containerStyle={{left:0,right:"auto"}} />
                            <Text style={styles.bluetoothHeading}>Bluetooth</Text>
                            <Text style={styles.bluetoothSubHeading}>
                                {isBluetoothConnected ? "connected" : "switched off"}
                            </Text>
                            <View style={{
                                position: "absolute",
                                top: "50%",
                                right: 0,
                                transform: [{ translateY: '-50%' }],
                                zIndex: 10,
                            }}>
                                <SwitchBtn
                                    checked={isBluetoothConnected}
                                    onToggle={async() => {
                                        if(Platform.OS === 'android'){
                                            if(isBluetoothConnected){
                                                await BluetoothStateManager.requestToDisable();
                                            }
                                            else {
                                                await BluetoothStateManager.requestToEnable();
                                            }
                                        }
                                    }}
                                />
                            </View>
                        </View>

                        {
                            (connectedDevice || connectingDevice) ? (
                                <View>
                                    <Image
                                        source={BottleImg}
                                        style={styles.bottleConnectedImg}
                                    />
                                    <DeviceInputCard
                                        loading={connectingDevice}
                                        placeholder='Bottle Name'
                                        bottleModal={
                                            connectedDevice?.name || connectedDevice?.localName || connectedDevice?.id || "Bottle id"
                                        }
                                        selectedBottleName={localDeviceName || ""}
                                        setSelectedBottleName={(value) => {
                                            editConnectDeviceLocalName(value);
                                        }}
                                        editingName={editingBottleName}
                                        setEditingName={(value: boolean) => setEditingBottleName(value)}
                                    />
                                    <SimpleBtn
                                        title='Remove device'
                                        onPress={handleRemoveDevice}
                                        containerBgColor='trasparent'
                                        titleTextColor='primary'
                                        containerStyle={{
                                            width: 'auto',
                                            alignSelf: "center"
                                        }}
                                        titleTextStyle={{
                                            fontSize: 16,
                                            textDecorationLine: "underline"
                                        }}
                                        elevation={0}
                                        disabled={connectingDevice || !connectedDevice || editingBottleName}
                                        disabledBgColor='trasparent'
                                        disabledTitleColor='primary'
                                    />
                                    <SimpleBtn
                                        title='Proceed'
                                        onPress={handleOnboarding}
                                        containerBgColor='primary'
                                        titleTextColor='secondary'
                                        containerStyle={{
                                            width: 'auto',
                                            paddingHorizontal: 32,
                                            alignSelf: "center",
                                            marginVertical: 32
                                        }}
                                        disabled={editingBottleName || connectingDevice}
                                    />
                                </View>
                            ) : (
                                <View style={styles.connectionContainer}>
                                    <Image
                                        source={AppLogo}
                                        style={styles.bottleInstructionImg}
                                    />
                                    <SimpleBtn
                                        title='Add Bottle'
                                        onPress={handleAddBottle}
                                        containerBgColor='primary'
                                        titleTextColor='secondary'
                                        containerStyle={{
                                            width: 'auto',
                                            paddingHorizontal: 32,
                                            marginBottom: 4
                                        }}
                                        disabled={!isBluetoothConnected}
                                    />
                                </View>
                            )
                        }

                    </View>
                </ScrollView>
            </View>
            <ScannedDevicesModal
                selectedDevice={selectedDevice}
                setSelectedDevice={setSelectedDevice}
            />
            <BluetoothPermissionModal />
            <LocationPermissionModal />
        </KeyboardAvoidingView>
    )
}

export default AddBottleScreen

const styles = StyleSheet.create({
    wrapper: {
        flex: 1,
    },
    container: {
        flex: 1,
    },
    heading: {
        paddingTop: 48,
        paddingBottom: 16,
        justifyContent: "center",
        alignItems: "center",
    },
    appLogoImage: {
        width: '50%',
        height: "auto",
        aspectRatio: 201 / 49,
        resizeMode: "contain",
        marginBottom: 2
    },
    innerContainer: {
        flex: 1,
        borderRadius: 19,
        justifyContent: 'space-between',
    },
    mainContainer: {
        // flex:1,
    },
    innerContainerHeader: {
        marginBottom: 32,
    },
    bluetoothHeading: {
        fontSize: 24,
        fontFamily: AppFonts.HelixaBold,
        textAlign: "center",
    },
    bluetoothSubHeading: {
        fontSize: 14,
        fontFamily: AppFonts.HelixaBold,
        textAlign: "center",
        lineHeight: 12.5,
    },
    connectionContainer: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center"
    },
    bottleInstructionImg: {
        width: "100%",
        height: "auto",
        aspectRatio: 13 / 10,
        resizeMode: "contain",
        marginBottom: 24,
        borderRadius: 19,
        overflow: "hidden"
    },
    bottleConnectedImg: {
        width: "100%",
        height: "auto",
        aspectRatio: 1,
        resizeMode: "contain",
        marginBottom: 24,
        borderRadius: 19,
        overflow: "hidden"
    },
})