import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import GenderEnum from '../enums/GenderEnum';
import ActivityLevelEnum from '../enums/ActivityLevelEnum';
import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEY = "onboarding-store";

type OnboardingStoreType = {
    currentStep: number,
    dob?: string,
    gender?: GenderEnum,
    country?: string | null,
    city?: string,
    state?: string,
    height?: number,
    weight?: number,
    activityLevel?: ActivityLevelEnum,
    goToPreviousStep: () => void,
    goToNextStep: () => void,
    setUserDetails: (
        dob: string,
        gender: GenderEnum,
        country: string | null,
        city: string,
        state: string,
        height: number,
        weight: number,
        activityLevel: ActivityLevelEnum
    ) => void,
    clearUserDetails: () => void,
}

const useOnboardingStore = create<OnboardingStoreType>()(
    persist(
        (set) => ({
            currentStep: 1,
            dob: undefined,
            gender: undefined,
            country: undefined,
            city: undefined,
            state: undefined,
            height: 0,
            weight: 0,
            activityLevel: undefined,

            goToPreviousStep: () => {
                set((state) => ({ currentStep: state.currentStep>1 ? state.currentStep - 1 : 1 }));
            },
            goToNextStep: () => {
                set((state) => ({ currentStep: state.currentStep<3 ? state.currentStep + 1 : 2 }));
            },

            setUserDetails: (dob,gender,country,city,state,height,weight,activityLevel) => {
                set({ dob, gender, country, city, state, height, weight, activityLevel, currentStep: 2 });
            },
            clearUserDetails: () => {
                set({ dob: undefined, gender: undefined, country: undefined, city: undefined, state: undefined, height: 0, weight: 0, activityLevel: undefined, currentStep: 1 });
            }

        }),
        {
            name: STORAGE_KEY,
            version: 1,
            storage: createJSONStorage(() => AsyncStorage)
        }
    )
)

export default useOnboardingStore;