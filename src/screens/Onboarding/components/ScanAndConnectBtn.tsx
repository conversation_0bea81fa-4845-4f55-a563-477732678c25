import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import SimpleBtn from '../../../components/Buttons/SimpleBtn';
import { Device } from 'react-native-ble-plx';

interface ScanAndConnectBtnInterface {
  devices:Map<string, Device>,
  connectToDevice:()=>void,
  startScan:()=>void,
  disabled ?:boolean
}

const ScanAndConnectBtn = ({
  devices,
  connectToDevice,
  startScan,
  disabled = false
}:ScanAndConnectBtnInterface) => {
  return (
    <SimpleBtn
      title={
        devices.size>0 ? "Connect" : "Scan"
      }
      onPress={()=>{
        if(devices.size>0){
          connectToDevice();
        }
        else{
          startScan();
        }
      }}
      containerStyle={{
        width:"auto",
        paddingHorizontal:32
      }}
      disabled={disabled}
      containerBgColor={disabled? "quaternary" : "primary"}
      titleTextColor={disabled? "secondary" : "secondary"}
      borderColor={disabled? "quaternary" : "trasparent"}
    />
  )
}

export default ScanAndConnectBtn

const styles = StyleSheet.create({})