
import { z, infer as zInfer } from 'zod';
import ActivityLevelEnum from '../enums/ActivityLevelEnum';

const completeUserDetailsResSchema = z.object({
    error: z.boolean(),
    statusCode: z.number(),
    msg: z.string(),
    user: z.object({
        id: z.string(),
        email: z.string(),
        firstName: z.string(),
        lastName: z.string(),
        isEmailVerified: z.boolean(),
        isAccountCompleted: z.boolean(),
        isDeleted: z.boolean(),
        dob: z.string().optional(),
        gender: z.string().optional(),
        height: z.number().optional(),
        weight: z.number().optional(),
        city: z.string().optional(),
        state: z.string().optional(),
        country: z.string().nullable(),
        activityLevel: z.enum([ActivityLevelEnum.SEDENTARY, ActivityLevelEnum.LIGHTLY_ACTIVE, ActivityLevelEnum.MODERATELY_ACTIVE, ActivityLevelEnum.VERY_ACTIVE, ActivityLevelEnum.EXTREMELY_ACTIVE]),
    }),
});

export default completeUserDetailsResSchema;

export type CompleteUserDetailsResType = zInfer<typeof completeUserDetailsResSchema>;