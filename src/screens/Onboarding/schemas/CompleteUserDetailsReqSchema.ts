
import { z, infer as zInfer } from 'zod';
import ActivityLevelEnum from '../enums/ActivityLevelEnum';
import GenderEnum from '../enums/GenderEnum';

const completeUserDetailsReqSchema = z.object({
    dob: z.string(),
    gender: z.enum([GenderEnum.MALE, GenderEnum.FEMALE, GenderEnum.OTHER]),
    country: z.string().nullable(),
    city: z.string(),
    state: z.string(),
    height: z.number(),
    weight: z.number(),
    activityLevel: z.enum([ActivityLevelEnum.SEDENTARY, ActivityLevelEnum.LIGHTLY_ACTIVE, ActivityLevelEnum.MODERATELY_ACTIVE, ActivityLevelEnum.VERY_ACTIVE, ActivityLevelEnum.EXTREMELY_ACTIVE]),
    deviceData: z.object({
        deviceId: z.string(),
        deviceName: z.string(),
        localName: z.string(),
    }),
    timeZone: z.string(),
});

export type CompleteUserDetailsReqType = zInfer<typeof completeUserDetailsReqSchema>;