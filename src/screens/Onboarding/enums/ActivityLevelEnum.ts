const enum ActivityLevelEnum {
    SEDENTARY = 'sedentary',
    LIGHTLY_ACTIVE = 'lightlyActive',
    MODERATELY_ACTIVE = 'moderatelyActive',
    VERY_ACTIVE = 'veryActive',
    EXTREMELY_ACTIVE = 'extremelyActive'
}

const activityLevelOptionsMapping = {
    [ActivityLevelEnum.SEDENTARY]: "Sedentary",
    [ActivityLevelEnum.LIGHTLY_ACTIVE]: "Lightly active",
    [ActivityLevelEnum.MODERATELY_ACTIVE]: "Moderately active",
    [ActivityLevelEnum.VERY_ACTIVE]: "Very active",
    [ActivityLevelEnum.EXTREMELY_ACTIVE]: "Extremely active"
}

const getActivityLevelLabel = (activityLevel?: string): string => {
  if (activityLevel && activityLevel in activityLevelOptionsMapping) {
    return activityLevelOptionsMapping[activityLevel as keyof typeof activityLevelOptionsMapping];
  }
  return "-";
};

export { getActivityLevelLabel };

export default ActivityLevelEnum;