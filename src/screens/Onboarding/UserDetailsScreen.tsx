import { Image, Keyboard, KeyboardAvoidingView, Linking, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import useOnboardingStore from './store/OnboardingStore';
import SelectBoxWithText from '../../components/FormFields/SelectBoxWithText';
import DOBPickerWithLabel from '../../components/FormFields/DOBPickerWithText';
import InputBoxWithLabel from '../../components/FormFields/InputBoxWithLabel';
import ActivityLevelEnum from './enums/ActivityLevelEnum';
import { useTheme } from '../../context/ThemeContext';
import GenderEnum from './enums/GenderEnum';
import { getUserLocation, getAddressFromCoordinates } from '../../utils/getUserLocation';
import Toast from 'react-native-toast-message';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import CustomModal from '../../components/Modals/CustomModal';
import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import { AppFonts } from '../../constants/theme/fonts/fonts';

const AppLogo = require('../../../assets/app_logo_resized.png')

type ValidationErrorType = {
  dob?: string,
  gender?: string,
  city?: string,
  state?: string,
  height?: string,
  weight?: string,
  activityLevel?: string
}

const RegisterScreen = () => {
  const insets = useSafeAreaInsets();

  const { theme } = useTheme();

  const dob = useOnboardingStore(state => state.dob);
  const gender = useOnboardingStore(state => state.gender);
  const city = useOnboardingStore(state => state.city);
  const state = useOnboardingStore(state => state.state);
  const country = useOnboardingStore(state => state.country);
  const height = useOnboardingStore(state => state.height);
  const weight = useOnboardingStore(state => state.weight);
  const activityLevel = useOnboardingStore(state => state.activityLevel);

  const setUserDetails = useOnboardingStore(state => state.setUserDetails);

  const [localData, setLocalData] = React.useState<{
    dob?: string,
    gender?: string,
    city?: string | null,
    state?: string | null,
    country?: string | null,
    height?: number | string,
    weight?: number | string,
    activityLevel?: string
  }>({
    dob,
    gender,
    city,
    state,
    country,
    height,
    weight,
    activityLevel
  });

  const [isFetchingLocation, setIsFetchingLocation] = useState(false);
  const [isLocationModalVisible, setIsLocationModalVisible] = useState(false);

  const genderOptions = [
    { label: "Male", value: GenderEnum.MALE },
    { label: "Female", value: GenderEnum.FEMALE },
    { label: "Other", value: GenderEnum.OTHER }
  ]

  const [currentDropdownId, setCurrentDropdownId] = useState<string | null>(null);

  const [validationError, setValidationError] = useState<ValidationErrorType | null>(null);

  const fetchUserLocation = async () => {
    setIsFetchingLocation(true);
    const rawLocation = await getUserLocation();
    if (rawLocation.success) {
      const { data: location } = rawLocation;
      if (!location) return;

      const { latitude, longitude } = location.coords;
      const rawAddress = await getAddressFromCoordinates(latitude, longitude);
      if (rawAddress.success) {
        const { data: address } = rawAddress;

        setLocalData(prev => ({
          ...prev,
          city: address.city,
          state: address.region,
          country: address.country
        }))
        setIsFetchingLocation(false);
      }
      else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: rawAddress.error.message
        });
        setIsLocationModalVisible(true);
      }
    } else {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: rawLocation.error.message
      });
      setIsLocationModalVisible(true);
    }
    setIsFetchingLocation(false);
  }

  const handleOpenSetting = () => {
    Linking.openSettings();
    setIsLocationModalVisible(false);
  }

  useEffect(() => {
    if (!city || !state) {
      fetchUserLocation();
    }
  }, [])

  const calculateAge = (dob: string) => {
    const today = new Date();
    const birthDate = new Date(dob.split('-').reverse().join('-'));
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();
    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  const validateUserDetails = () => {
    setValidationError(null);
    if (!localData.dob) {
      setValidationError(prev => ({
        ...prev,
        dob: "Please select date of birth."
      }))
      return false;
    }
    if(calculateAge(localData.dob) < 8) {
      setValidationError(prev => ({
        ...prev,
        dob: "Age should be greater than 8 years."
      }))
      return false;
    }
    if (!localData.gender) {
      setValidationError(prev => ({
        ...prev,
        gender: "Please select gender."
      }))
      return false;
    }
    if (!localData.height) {
      setValidationError(prev => ({
        ...prev,
        height: "Please enter height."
      }))
      return false;
    }
    if (Number(localData.height) < 100 || Number(localData.height) > 200) {
      setValidationError(prev => ({
        ...prev,
        height: "Height should be between 100cm and 200cm."
      }))
      return false;
    }
    if (!localData.weight) {
      setValidationError(prev => ({
        ...prev,
        weight: "Please enter weight."
      }))
      return false;
    }
    if (Number(localData.weight) < 30 || Number(localData.weight) > 300) {
      setValidationError(prev => ({
        ...prev,
        weight: "Weight should be between 30kg and 300kg."
      }))
      return false;
    }
    if (!localData.city) {
      setValidationError(prev => ({
        ...prev,
        city: "Please enter city."
      }))
      return false;
    }
    if (!localData.state) {
      setValidationError(prev => ({
        ...prev,
        state: "Please enter state."
      }))
      return false;
    }
    if (!localData.activityLevel) {
      setValidationError(prev => ({
        ...prev,
        activityLevel: "Please select activity level."
      }))
      return false;
    }
    return true;
  }

  const handleSubmit = () => {
    if (!isFetchingLocation && validateUserDetails()) {
      setUserDetails(
        localData.dob as string,
        localData.gender as GenderEnum,
        localData.country as string,
        localData.city as string,
        localData.state as string,
        Number(localData.height),
        Number(localData.weight),
        localData.activityLevel as ActivityLevelEnum
      )
    }
  }
  return (
    <KeyboardAvoidingView
      enabled
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -38}
      style={[styles.wrapper, {
        paddingTop: insets.top + 32,
        paddingBottom: insets.bottom + 24,
        paddingLeft: insets.left + 16,
        paddingRight: insets.right + 16,
        backgroundColor: theme.background.secondary
      }]}
    >
      <TouchableWithoutFeedback
        onPress={() => {
          setCurrentDropdownId(null);
          Keyboard.dismiss();
        }}
      >
        <View style={{ flex: 1 }}>
          <View style={styles.heading}>
            <Image source={AppLogo} style={styles.appLogoImage} resizeMode='contain' />
          </View>
          <View style={[styles.innerContainer, {
            backgroundColor: theme.background.primary
          }]}>
            <ScrollView
              keyboardShouldPersistTaps='handled'
              style={[styles.container, {
                backgroundColor: theme.background.primary
              }]}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ flexGrow: 1, paddingBottom: 16 }}
            >
              <TouchableWithoutFeedback
                onPress={() => {
                  setCurrentDropdownId(null);
                  Keyboard.dismiss();
                }}
              >
                <View style={{ flex: 1 }}>
                  <View style={styles.mainContainer}>
                    <View style={styles.innerContainerHeader}>
                      <Text style={styles.registerHeading}>Register</Text>
                    </View>
                    <View style={styles.rowInputContainer}>
                      <DOBPickerWithLabel
                        value={localData.dob?.split('-').reverse().join('-')}
                        label='Date of Birth'
                        placeholder="Choose date"
                        onSelectDate={(date, age) => {
                          setLocalData(prev => ({
                            ...prev,
                            dob: date.split('-').reverse().join('-')
                          }))
                        }}
                        error={validationError?.dob}
                        clearValidationError={() => {
                          setValidationError(prev => ({
                            ...prev,
                            dob: undefined
                          }))
                        }}
                        wrapperStyle={{
                          flex: 1
                        }}
                      />
                      <SelectBoxWithText
                        label='Gender'
                        placeholder='Choose gender'
                        options={genderOptions}
                        selectedValue={localData.gender}
                        onValueChange={(value) => {
                          setLocalData(prev => ({
                            ...prev,
                            gender: value as GenderEnum
                          }))
                        }}
                        error={validationError?.gender}
                        clearValidationError={() => {
                          setValidationError(prev => ({
                            ...prev,
                            gender: undefined
                          }))
                        }}
                        dropdownId={"gender"}
                        setCurrentOpenDropdown={(id) => {
                          setCurrentDropdownId(id);
                        }}
                        maxOptionsToShow={3}
                        changeBG={true}
                        currentOpenDropdown={currentDropdownId}
                        triggerZ={8}
                        listZ={9}
                        wrapperStyle={{
                          flex: 1
                        }}
                      />
                    </View>

                    <View style={styles.rowInputContainer}>
                      <InputBoxWithLabel
                        label='Height'
                        placeholder='Enter height (cm)'
                        selected={localData.height ? localData.height.toString() : ''}
                        setSelected={(value) => {
                          setLocalData(prev => ({
                            ...prev,
                            height: value
                          }))
                        }}
                        error={validationError?.height}
                        clearValidationError={() => {
                          setValidationError(prev => ({
                            ...prev,
                            height: undefined
                          }))
                        }}
                        numericOnly={true}
                        containerStyle={{
                          flex: 1
                        }}
                      />

                      <InputBoxWithLabel
                        label='Weight'
                        placeholder='Enter weight (kg)'
                        selected={localData.weight ? localData.weight.toString() : ''}
                        setSelected={(value) => {
                          setLocalData(prev => ({
                            ...prev,
                            weight: value
                          }))
                        }}
                        error={validationError?.weight}
                        clearValidationError={() => {
                          setValidationError(prev => ({
                            ...prev,
                            weight: undefined
                          }))
                        }}
                        numericOnly={true}
                        containerStyle={{
                          flex: 1
                        }}
                      />
                    </View>

                    <InputBoxWithLabel
                      label='City'
                      selected={localData.city ? localData.city : ''}
                      placeholder='Enter city'
                      setSelected={(value) => {
                        setLocalData(prev => ({
                          ...prev,
                          city: value
                        }))
                      }}
                      error={validationError?.city}
                      clearValidationError={() => {
                        setValidationError(prev => ({
                          ...prev,
                          city: undefined
                        }))
                      }}
                      containerStyle={{
                        marginBottom: 16
                      }}
                      icon={<MaterialIcons name="my-location" size={24} color={theme.icon.primary} />}
                      onIconPress={fetchUserLocation}
                      loading={isFetchingLocation}
                    />

                    <InputBoxWithLabel
                      label='State'
                      selected={localData.state ? localData.state : ''}
                      placeholder='Enter state'
                      setSelected={(value) => {
                        setLocalData(prev => ({
                          ...prev,
                          state: value
                        }))
                      }}
                      error={validationError?.state}
                      clearValidationError={() => {
                        setValidationError(prev => ({
                          ...prev,
                          state: undefined
                        }))
                      }}
                      containerStyle={{
                        marginBottom: 16
                      }}
                      loading={isFetchingLocation}
                    />

                    <SelectBoxWithText
                      label='Activity level'
                      placeholder='Choose activity level'
                      options={[
                        { label: "Sedentary", value: String(ActivityLevelEnum.SEDENTARY) },
                        { label: "Lightly active", value: String(ActivityLevelEnum.LIGHTLY_ACTIVE) },
                        { label: "Moderately active", value: String(ActivityLevelEnum.MODERATELY_ACTIVE) },
                        { label: "Very active", value: String(ActivityLevelEnum.VERY_ACTIVE) },
                        { label: "Extremely active", value: String(ActivityLevelEnum.EXTREMELY_ACTIVE) }
                      ]}
                      selectedValue={localData.activityLevel}
                      onValueChange={(value) => {
                        setLocalData(prev => ({
                          ...prev,
                          activityLevel: value as ActivityLevelEnum
                        }))
                      }}
                      error={validationError?.activityLevel}
                      clearValidationError={() => {
                        setValidationError(prev => ({
                          ...prev,
                          activityLevel: undefined
                        }))
                      }}
                      wrapperStyle={{
                        marginBottom: 16
                      }}
                      dropdownId={"activityLevel"}
                      setCurrentOpenDropdown={(id) => {
                        setCurrentDropdownId(id);
                      }}
                      maxOptionsToShow={5}
                      changeBG={true}
                      currentOpenDropdown={currentDropdownId}
                      triggerZ={6}
                      listZ={7}
                    />
                  </View>
                  <SimpleBtn
                    title='Next'
                    onPress={handleSubmit}
                    containerBgColor='secondary'
                    titleTextColor='primary'
                    containerStyle={{
                      width: '50%',
                      alignSelf: "center"
                    }}
                  />
                </View>
              </TouchableWithoutFeedback>
            </ScrollView>

          </View>
        </View>
      </TouchableWithoutFeedback>
      <CustomModal
        visible={isLocationModalVisible}
        onClose={() => setIsLocationModalVisible(false)}
        title="Location"
        subtitle="Permission"
        icon={
          <Ionicons name="location-sharp" size={120} color={theme.icon.primary}
            style={{
              alignSelf: "center"
            }}
          />
        }
        desc="Please grant location permission to continue"
        buttons={
          <View style={styles.locationModalBtnsContainer}>
            <SimpleBtn
              title='Open settings'
              onPress={handleOpenSetting}
              containerStyle={{
                flex: 1,
              }}
            />
          </View>
        }
      />
    </KeyboardAvoidingView>
  )
}

export default RegisterScreen

const styles = StyleSheet.create({
  wrapper: {
    flex: 1
  },
  container: {
    flex: 1
  },
  heading: {
    paddingTop: 48,
    paddingBottom: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  appLogoImage: {
    width: '50%',
    height: "auto",
    aspectRatio: 201 / 49,
    resizeMode: "contain",
    marginBottom: 2
  },
  innerContainer: {
    flex: 1,
    borderRadius: 19,
    padding: 24,
    paddingBottom: 32,
    justifyContent: "space-between"
  },
  mainContainer: {
    flex: 1,
    paddingBottom: 170
  },
  innerContainerHeader: {
    marginBottom: 16
  },
  registerHeading: {
    fontSize: 24,
    textAlign: "center",
    paddingHorizontal: 48,
    fontFamily: AppFonts.HelixaBold
  },
  rowInputContainer: {
    flexDirection: "row",
    gap: 16,
    marginBottom: 16,
  },
  locationModalBtnsContainer: {
    flexDirection: "row",
    gap: 16,
  }
})