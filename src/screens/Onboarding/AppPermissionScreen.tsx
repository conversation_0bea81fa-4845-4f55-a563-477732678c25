import { Image, KeyboardAvoidingView, Platform, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import { useTheme } from '../../context/ThemeContext';
import BackBtn from '../../components/Buttons/BackBtn';
import useOnboardingStore from './store/OnboardingStore';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { AppPermissionCard } from '../../components/Cards/AppPermissionCard';
const AppLogo = require('../../../assets/app_logo_resized.png')
const GoogleHealthLogo = require('../../../assets/google_health.png')
const AppleHealthLogo = require('../../../assets/apple_health.png')

const AppPermissionScreen = () => {
    const insets = useSafeAreaInsets();

    const { theme } = useTheme();
    const goToPreviousStep = useOnboardingStore(state => state.goToPreviousStep);
    const goToNextStep = useOnboardingStore(state => state.goToNextStep);

    const handleSubmit = () => {
        goToNextStep();
    }

    return (
        <KeyboardAvoidingView
            enabled
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -38}
            style={[styles.wrapper, {
                paddingTop: insets.top + 32,
                paddingBottom: insets.bottom + 24,
                paddingLeft: insets.left + 16,
                paddingRight: insets.right + 16,
                backgroundColor: theme.background.secondary
            }]}
        >
            <ScrollView
                keyboardShouldPersistTaps='handled'
                style={[styles.container, {
                    backgroundColor: theme.background.secondary
                }]}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ flexGrow: 1 }}
            >
                <View style={styles.heading}>
                    <Image source={AppLogo} style={styles.appLogoImage} resizeMode='contain' />
                </View>
                <View style={[styles.innerContainer, {
                    backgroundColor: theme.background.primary
                }]}>
                    <View style={styles.mainContainer}>
                        <View style={styles.innerContainerHeader}>
                            <BackBtn onPress={goToPreviousStep}  containerStyle={{left:0,right:"auto"}} />
                            <Text style={styles.AppPermissionHeading}>App Permission</Text>
                        </View>
                        
                        <AppPermissionCard
                            icon={Platform.OS === 'ios' ? AppleHealthLogo : GoogleHealthLogo}
                            onToggle={() => {}}
                            checked={false}
                            iconWrapperStyle={
                                Platform.OS === 'ios' ? {} : {padding:7,width:"100%",height:"100%"}
                            }
                        />
                    </View>
                    <SimpleBtn
                        title='Next'
                        onPress={handleSubmit}
                        containerBgColor='secondary'
                        titleTextColor='primary'
                        containerStyle={{
                            width: '50%',
                            alignSelf: "center"
                        }}
                    />
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    )
}

export default AppPermissionScreen

const styles = StyleSheet.create({
    wrapper: {
        flex: 1,
    },
    container: {
        flex: 1,
    },
    heading: {
        paddingTop: 48,
        paddingBottom: 16,
        justifyContent: "center",
        alignItems: "center",
    },
    appLogoImage: {
        width: '50%',
        height: "auto",
        aspectRatio: 201 / 49,
        resizeMode: "contain",
        marginBottom: 2
    },
    innerContainer: {
        flex: 1,
        borderRadius: 19,
        padding: 24,
        justifyContent: 'space-between',
    },
    mainContainer: {
    },
    innerContainerHeader: {
        marginBottom: 32
    },
    AppPermissionHeading: {
        fontSize: 24,
        fontFamily: AppFonts.HelixaBold,
        textAlign: "center",
        paddingHorizontal:48,
    },
    forgotPassText: {
        fontSize: 13,
        fontWeight: 400,
        textAlign: "right",
        fontStyle: "italic",
        textDecorationLine: 'underline'
    },
    confirmBtnContainer: {
        flexDirection: "row",
        marginTop: 48,
        gap: 32
    }
})