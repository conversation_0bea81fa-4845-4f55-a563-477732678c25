import { BackHandler, StyleSheet, Text, View } from 'react-native'
import React, { useEffect } from 'react'
import useOnboardingStore from './store/OnboardingStore';
import UserDetailsScreen from './UserDetailsScreen';
import AppPermissionScreen from './AppPermissionScreen';
import AddBottleScreen from './AddBottleScreen';

const OnboardingScreen = () => {
  const currentStep = useOnboardingStore(state=>state.currentStep);
  const goToPreviousStep = useOnboardingStore(state=>state.goToPreviousStep);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if(currentStep==1){
        return false;
      }
      else{
        goToPreviousStep();
        return true;
      }
    });

    return () => {
      backHandler.remove();
    };
  }, []);
  
  if(currentStep==1){
    return <UserDetailsScreen/>
  }
  else if(currentStep==2){
    return <AppPermissionScreen/>
  }
  else if(currentStep==3){
    return <AddBottleScreen/>
  }
  return null;
}

export default OnboardingScreen

const styles = StyleSheet.create({})