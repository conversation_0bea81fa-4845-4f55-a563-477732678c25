import { Animated, Easing, Image, StyleSheet, Text } from 'react-native'
import React, { useEffect, useRef } from 'react'
import { LinearGradient } from 'expo-linear-gradient'
import { AppFonts } from '../../constants/theme/fonts/fonts'
import useAppStateStore from '../../store/AppStateStore'
// import { useTheme } from '../../context/ThemeContext'

type CustomSplashScreenProps = {
  hideSplashScreen?:boolean
}

const CustomSplashScreen = ({hideSplashScreen=true}:CustomSplashScreenProps) => {
  const appReady = useAppStateStore(state=>state.appReady);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const setSplashDone = useAppStateStore(state=>state.setSplashDone);
  // const { theme } = useTheme();

  useEffect(() => {
    if (appReady && hideSplashScreen) {
      Animated.timing(fadeAnim, {
        toValue: 0.5,
        duration: 1000,
        easing:Easing.ease,
        useNativeDriver: true,
      }).start(() => {
        setSplashDone(true);
      });
    }
  }, [appReady,hideSplashScreen]);

  return (
    <Animated.View style={[styles.splash,{ opacity: fadeAnim }]}>
        <LinearGradient
          colors={['#EAEAEA','#797979']}
          // locations={[.1,.85]}
          style={styles.background}
          start={{ x: -.8, y: 0.8 }}
          // end={{ x: 1, y: .9 }}
        />
        <Image
          source={require("../../../assets/bottle-img.png")}
          style={[styles.bottleImg]}
        />
        <Image
          source={require("../../../assets/app_logo_resized.png")}
          style={[styles.logo]}
        />
        <Text style={[styles.text, { color: '#FFFFFF'}]}>Unleashing your health potential</Text>
    </Animated.View>
  )
}

export default CustomSplashScreen


const styles = StyleSheet.create({
  splash: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex:10,
    opacity:.7
  },
  bottleImg: {
    position:"absolute",
    bottom:0,
    width:"100%",
    height:"77%",
    resizeMode:"contain",
    zIndex:9
  },
  logo: { 
    width:"90%",
    height:"auto",
    aspectRatio:201/49,
    resizeMode:"contain",
    zIndex:11
  },
  text: { 
    fontSize: 21,
    zIndex:11,
    fontFamily:AppFonts.HelixaBook
  },
  container: { 
    flex: 1,
    justifyContent: "center",
    alignItems: "center"
  }
});