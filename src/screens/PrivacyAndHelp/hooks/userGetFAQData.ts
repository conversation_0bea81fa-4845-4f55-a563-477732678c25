import { useInfiniteQuery } from '@tanstack/react-query';
import helpFAQService from '../apis/helpFAQService';
import { FAQSchemaType, FAQResponseSchemaType } from '../apis/schemas/FAQResponseSchema';

const useGetFAQData = (searchQuery: string) => {
  const {
    data,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    refetch,
    isFetchingNextPage,
    isRefetching,
  } = useInfiniteQuery({
    queryKey: ['faqs', searchQuery],
    queryFn: async ({ pageParam }: { pageParam: number }) => {
      const response = await helpFAQService.getFAQs({
        page: pageParam,
        searchQuery,
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch FAQs');
      }

      return response.data;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage: FAQResponseSchemaType, allPages: FAQResponseSchemaType[]) => {
      const totalLoadedItems = allPages.reduce((sum, page) => sum + page.faqs.length, 0);
      
      // If we've loaded all items, return undefined to stop pagination
      if (totalLoadedItems >= lastPage.total) {
        return undefined;
      }
      
      // Otherwise, return the next page number
      return allPages.length + 1;
    },
  });

  const FAQData: FAQSchemaType[] = data?.pages.flatMap((page) => page.faqs) ?? [];

  return {
    FAQData,
    isLoading,
    isError,
    error: (error as Error)?.message,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    isRefetching,
  };
};

export default useGetFAQData;