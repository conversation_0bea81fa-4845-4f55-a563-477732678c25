import { useEffect, useState } from 'react';
import { PrivacyPolicyResponseType } from '../apis/schemas/PrivacyPolicyResponseSchema';
import helpFAQService from '../apis/helpFAQService';

const useGetPrivacyData = () => {
    const [privacyData, setPrivacyData] = useState<PrivacyPolicyResponseType | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [refreshing, setRefreshing] = useState(false);

    const getPrivacyData = async (isRefreshing = false) => {
        if(!isRefreshing) setLoading(true);
        setError(null);

        try {
            const response = await helpFAQService.getPrivacyPolicy({});

            if (response.success) {
                setPrivacyData(response.data);
            } else {
                setError(response.error?.message || 'Failed to fetch privacy data');
            }
        } catch (err: any) {
            setError(err.message || 'Unexpected error');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        await getPrivacyData(true);
    };

    useEffect(() => {
        getPrivacyData();
    }, []);

    return {
        privacyData,
        loading,
        error,
        refreshing,
        handleRefresh,
    };
};

export default useGetPrivacyData;