import { RefreshControl, ScrollView, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import AppLayout from '../../navigation/components/Layouts/AppLayout';
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import useGetPrivacyData from './hooks/useGetPrivacyData';
import PageLoader from '../../components/Loaders/PageLoader';

const PrivacyScreen = () => {
  const { theme } = useTheme();
  const { privacyData, loading, error, refreshing, handleRefresh, } = useGetPrivacyData();

  return (
    <AppLayout>
        <View style={styles.wrapper}>
          <ScrollView 
            style={[styles.container, {
              backgroundColor: theme.background.primary
            }]} 
            contentContainerStyle={styles.containerContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[theme.button.primary]}
                progressViewOffset={24}
              />
            }
          >
            {
              loading?(
                <View style={styles.centerContainer}>
                  <PageLoader overlayBg='trasparent'/>
                </View>
              ) : (
                error ? (
                  <View style={styles.centerContainer}>
                    <Text style={[styles.errorText,{
                      color:theme.text.primary
                    }]}>{error}</Text>
                  </View>
                ) : (
                  privacyData?.helps.length === 0 ? (
                    <View style={{flex:1,justifyContent:"center",alignItems:"center"}}>
                      <Text style={[styles.errorText,{
                        color:theme.text.primary
                      }]}>No data found</Text>
                      </View>
                  ) : (
                    <View>
                      {
                        privacyData?.helps.map((item,index) => (
                          <View key={index} style={styles.itemContainer}>
                            <Text style={[styles.titleText,{
                              color:theme.text.primary
                            }]}>{`${item.title?.[0].toUpperCase()}${item.title?.slice(1)}`}</Text>
                            <Text style={[styles.contentText,{
                              color:theme.text.quaternary
                            }]}>{
                              `${item.description?.[0].toUpperCase()}${item.description?.slice(1)}`
                            }</Text>
                          </View>
                        ))
                      }
                    </View>
                  )
                )
              )
            }
          </ScrollView>
        </View>
    </AppLayout>
  )
}

export default PrivacyScreen

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    padding:4,
    paddingTop:12,
    paddingBottom: 90
  },
  container: {
    flex: 1,
    elevation:2,
    borderRadius:19,
  },
  containerContent:{
    flexGrow:1,
    gap:24,
    padding:24,
    paddingVertical:40
  },
  centerContainer:{
    flex:1,
    justifyContent:"center",
    alignItems:"center",
  },
  itemContainer:{
    marginBottom:16
  },
  titleText:{
    fontSize:24,
    fontFamily:AppFonts.HelixaBold,
    marginBottom:4,
  },
  contentText:{
    fontSize:14,
    fontFamily:AppFonts.HelixaBold,
    lineHeight:18,
    marginTop:8
  },
  errorText:{
    fontSize:20,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center"
  }
})