import apiClient, { createErrorResponse } from "../../../apis/axios/axiosInstance";
import { ResponseErrorType } from "../../../types/ResponseErrorType";
import { ResponseSuccessType } from "../../../types/ResponseSuccessType";
import { FAQResponseSchema, FAQResponseSchemaType } from "./schemas/FAQResponseSchema";
import { PrivacyPolicyResponseSchema, PrivacyPolicyResponseType } from "./schemas/PrivacyPolicyResponseSchema";

const helpFAQService = {
    getPrivacyPolicy: async ({}): Promise<ResponseSuccessType<PrivacyPolicyResponseType> | ResponseErrorType> => {
        try {
            const response = await apiClient.get(`/help`);

            console.log(response.data?.data);

            const privacyPolicyResponse = PrivacyPolicyResponseSchema.safeParse(response.data?.data);

            if (!privacyPolicyResponse.success) {
                throw new Error("Unable to fetch privacy policy. Please try again later.");
            }

            return {
                success: true,
                data: privacyPolicyResponse.data
            };

        } catch (error) {
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    getFAQs: async ({page=1, searchQuery}: {page: number; searchQuery: string}):Promise<ResponseSuccessType<FAQResponseSchemaType> | ResponseErrorType> => {
        try {
            const response = await apiClient.get(`/faq`, {
                params: {
                    page,
                    question: searchQuery
                }
            });

            const faqResponse = FAQResponseSchema.safeParse(response.data?.data);

            if (!faqResponse.success) {
                console.log(faqResponse.error);
                throw new Error("Unable to fetch FAQs. Please try again later.");
            }

            return {
                success: true,
                data: faqResponse.data
            };
        } catch (error) {
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    sendContactQuery: async ({formData}: {formData: FormData}): Promise<ResponseSuccessType<any> | ResponseErrorType> => {
        try {
            const response = await apiClient.post(`/contact`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            return {
                success: true,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    }
}

export default helpFAQService;