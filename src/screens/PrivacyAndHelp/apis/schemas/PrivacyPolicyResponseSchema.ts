import { z, infer as zInfer } from 'zod';

const PrivacyPolicyFAQSchema = z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
});

export const PrivacyPolicyResponseSchema = z.object({
    helps: z.array(PrivacyPolicyFAQSchema),
    nbHits: z.number(),
    total: z.number(),
});

export type PrivacyPolicyFAQType = zInfer<typeof PrivacyPolicyFAQSchema>;
export type PrivacyPolicyResponseType = zInfer<typeof PrivacyPolicyResponseSchema>;