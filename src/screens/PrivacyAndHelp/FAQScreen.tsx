import React, { useState, useEffect } from "react";
import {
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
  RefreshControl,
  View,
  TouchableWithoutFeedback,
  Linking,
} from "react-native";
import AppLayout from "../../navigation/components/Layouts/AppLayout";
import FAQCard from "./components/FAQCard";
import PageLoader from "../../components/Loaders/PageLoader";
import FlatListBottomLoader from "../../components/Loaders/FlatListBottomLoader";
import { useTheme } from "../../context/ThemeContext";
import { AppFonts } from "../../constants/theme/fonts/fonts";
import InputBox from "../../components/FormFields/InputBox";
import { Fontisto } from "@expo/vector-icons";
import SimpleBtn from "../../components/Buttons/SimpleBtn";
import ContactUsModal from "./components/ContactUsModal";
import useGetFAQData from "./hooks/userGetFAQData";
import { useScreenInteractable } from "../../hooks/misc/useScreenInteractable";
import { useDebounce } from "../../hooks/misc/useDebounce";

const HelpScreen = () => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedQuestion, setExpandedQuestion] = useState<number | null>(null);
  const [showContactUsModal, setShowContactUsModal] = useState(false);
  const hasScreenLoaded = useScreenInteractable();

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  const {
    FAQData,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    isRefetching,
  } = useGetFAQData(debouncedSearchQuery);

  if (!hasScreenLoaded) {
    return (
      <AppLayout>
        <View style={styles.loaderContainer}>
          <PageLoader />
        </View>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <View style={styles.wrapper}>
        <View
          style={[
            styles.faqContainer,
            {
              backgroundColor: theme.background.primary,
            },
          ]}
        >
          <ScrollView
            style={[styles.container]}
            showsVerticalScrollIndicator={false}
            scrollEventThrottle={16}
            contentContainerStyle={styles.containerContent}
            onScroll={(e) => {
              let paddingToBottom = 90;
              paddingToBottom += e.nativeEvent.layoutMeasurement.height;
              if (
                e.nativeEvent.contentOffset.y >=
                e.nativeEvent.contentSize.height - paddingToBottom
              ) {
                if (hasNextPage && !isFetchingNextPage) {
                  fetchNextPage();
                }
              }
            }}
            refreshControl={
              <RefreshControl
                refreshing={isRefetching}
                onRefresh={() => refetch()}
                colors={[theme.button.primary]}
                progressViewOffset={24}
              />
            }
          >
            <TouchableWithoutFeedback>
              <View style={{ flex: 1 }}>
                <Text
                  style={[
                    styles.headerText,
                    {
                      color: theme.text.primary,
                    },
                  ]}
                >
                  FAQs
                </Text>
                <InputBox
                  selected={searchQuery}
                  setSelected={setSearchQuery}
                  placeholder="Search For FAQs"
                  icon={
                    <Fontisto
                      name="search"
                      size={18}
                      color={theme.icon.quaternary}
                    />
                  }
                  containerStyle={{
                    paddingVertical: 16,
                  }}
                />
                {isLoading && FAQData.length === 0 ? (
                  <View style={styles.centerLoader}>
                    <PageLoader overlayBg='trasparent'/>
                  </View>
                ) : FAQData.length === 0 ? (
                  <Text
                    style={[
                      styles.noDataText,
                      {
                        color: theme.text.primary,
                      },
                    ]}
                  >
                    {isError ? error || "Error loading FAQs" : "No FAQs Found"}
                  </Text>
                ) : (
                  <FlatList
                    data={FAQData}
                    keyExtractor={(item) => item.id.toString()}
                    scrollEnabled={false}
                    renderItem={({ item, index }) => (
                      <FAQCard
                        index={index}
                        id={item.id}
                        question={item.question}
                        answer={item.answer}
                        expandedQuestion={expandedQuestion}
                        setExpandedQuestion={(value) => {
                          setExpandedQuestion(value);
                        }}
                      />
                    )}
                    ListFooterComponent={
                      isFetchingNextPage ? <FlatListBottomLoader overlayBg={"trasparent"}/> : null
                    }
                  />
                )}
              </View>
            </TouchableWithoutFeedback>
          </ScrollView>
          <View style={styles.contactUsBtnContainer}>
            <View style={styles.contactNumberContainer}>
              <Text
                style={[
                  styles.contactUsText,
                  {
                    color: theme.text.primary,
                  },
                ]}
              >
                Contact Support :
              </Text>
              <SimpleBtn
                title="225-6478-905"
                onPress={() => {
                  Linking.openURL(`tel:225-6478-905`);
                }}
                containerBgColor="trasparent"
                titleTextColor="senary"
                elevation={0}
                containerStyle={styles.contactNumberBtn}
                titleTextStyle={styles.contactNumberText}
              />
            </View>
            <SimpleBtn
              title="Send a query"
              onPress={() => {
                setShowContactUsModal(true);
              }}
              containerBgColor="primary"
              titleTextColor="secondary"
              containerStyle={styles.contactUsBtn}
            />
          </View>
        </View>
      </View>
      <ContactUsModal
        isVisible={showContactUsModal}
        onClose={() => {
          setShowContactUsModal(false);
        }}
      />
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    padding: 4,
    paddingTop: 12,
    paddingBottom: 90,
  },
  faqContainer: {
    flex: 1,
    elevation: 2,
    borderRadius: 19,
  },
  container: {
    flex: 1,
  },
  containerContent: {
    flexGrow: 1,
    gap: 24,
    padding: 16,
    paddingVertical: 40,
  },
  headerText: {
    fontSize: 32,
    textAlign: "left",
    fontFamily: AppFonts.HelixaBold,
    paddingHorizontal: 8,
    marginBottom: 8,
  },
  loaderContainer: {
    flex: 1,
    bottom: 50,
    justifyContent: "center",
    alignItems: "stretch",
  },
  centerLoader: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 40,
  },
  noDataText: {
    textAlign: "center",
    marginTop: 20,
    fontSize: 16,
    fontFamily: "Exo_400Regular",
  },
  contactUsBtnContainer: {
    padding: 16,
    paddingTop: 0,
  },
  contactUsBtn: {
    paddingVertical: 2,
  },
  contactNumberContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  contactNumberBtn: {
    width: "auto",
    padding: 0,
  },
  contactUsText: {
    fontSize: 14,
    fontFamily: AppFonts.HelixaBold,
  },
  contactNumberText: {
    fontSize: 14,
    textDecorationLine: "underline",
    textDecorationStyle: "solid",
  },
});

export default HelpScreen;