import { Pressable, StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { useTheme } from '../../../context/ThemeContext';
import { AppFonts } from '../../../constants/theme/fonts/fonts';
import { Entypo } from '@expo/vector-icons';

interface FAQCardProps {
    index:number,
    id:string,
    question:string,
    answer:string,
    expandedQuestion:number | null,
    setExpandedQuestion:(index:number | null) => void,
}

const FAQCard = ({ index, id, question, answer, expandedQuestion, setExpandedQuestion }:FAQCardProps) => {
    const { theme } = useTheme();
    return (
        <View style={[styles.container,{
            borderBottomColor:theme.border.primary
        }]}>
            <Pressable
                style={[styles.questionContainer,{
                    backgroundColor:theme.background.primary,
                }]}
                onPress={() => setExpandedQuestion(expandedQuestion === index ? null : index)}
            >
                <View style={styles.questionHeader}>
                    <Text style={[styles.questionNumber,{
                        color:theme.text.primary
                    }]}>{`Q${index + 1})`}</Text>
                    <Text style={[styles.questionText,{
                        color:theme.text.primary
                    }]}>{question}</Text>
                    {
                        expandedQuestion === index ? (
                            <Entypo
                                name={'chevron-thin-up'}
                                size={20}
                                color={theme.text.primary}
                            />
                        ) : (
                            <Entypo
                                name={'chevron-thin-down'}
                                size={20}
                                color={theme.text.primary}
                            />
                        )
                    }
                </View>

                {expandedQuestion === index && (
                    <View style={styles.answerContainer}>
                        <Text style={[styles.answerText,{
                            color:theme.text.primary
                        }]}>{answer}</Text>
                    </View>
                )}
            </Pressable>
        </View>
    )
}

export default FAQCard

const styles = StyleSheet.create({
    container: {
        borderBottomWidth: 1.5,
        borderStyle: 'solid',
    },
    questionContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
        paddingHorizontal:8,
    },
    questionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 16,
        paddingTop:12,
        width: '100%',
    },
    questionNumber: {
        fontSize: 21,
        fontFamily: AppFonts.HelixaBold,
        marginRight: 8,
    },
    questionText: {
        fontSize: 13,
        fontFamily: AppFonts.HelixaBold,
        flex: 1,
        marginRight: 4,
    },
    answerContainer: {
        width: '100%',
        paddingHorizontal: 4,
        paddingVertical: 10,
    },
    answerText: {
        fontSize: 14,
        textAlign: 'left',
        lineHeight: 20,
        fontFamily: AppFonts.HelixaBold,
    },
})