import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import CustomModal from '../../../components/Modals/CustomModal'
import SimpleBtn from '../../../components/Buttons/SimpleBtn'
import SelectBoxWithLabel from '../../../components/FormFields/SelectBoxWithText'
import TextAreaWithLabel from '../../../components/FormFields/TextAreaWithLabel'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import { AntDesign, Entypo, EvilIcons, FontAwesome6, Fontisto, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons'
import { useTheme } from '../../../context/ThemeContext'
import Toast from 'react-native-toast-message'
import { DocumentPickerAsset ,getDocumentAsync} from "expo-document-picker";
import toastConfig from '../../../config/CustomToastConfig'
import helpFAQService from '../apis/helpFAQService'


type ContactUsModalProps = {
  isVisible:boolean,
  onClose:()=>void,
}

const ContactUsModal = ({isVisible,onClose}:ContactUsModalProps) => {

  const { theme } = useTheme();

  const [ currentSelectedDropdownId, setCurrentSelectedDropdownId ] = useState<string | null>(null);

  const [ category,setCategory ] = useState("");
  const [ description,setDescription ] = useState("");
  const [ attachments,setAttachments ] = useState<DocumentPickerAsset[]>([]);

  const [ isSendingQuery, setIsSendingQuery ] = useState(false);

  const [ showSuccessullySentModal,setShowSuccessullySentModal ] = useState(false);

  const handleSelectAttachment = async () => {

    setCurrentSelectedDropdownId(null);

    if(attachments.length>=3) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: "You can attach up to 3 files only.",
        position: 'bottom'
      });
      return;
    };

    try {

      const result = await getDocumentAsync({
        type: ["image/*", "application/pdf"],
        copyToCacheDirectory: true,
        multiple: true,
      });

      if (!result.canceled) {
        const newFiles = Array.isArray(result.assets)
          ? result.assets
          : result.assets
            ? [result.assets]
            : [];

        if (attachments.length + newFiles.length > 3) {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: "You can attach up to 3 files only.",
            position: 'bottom',
          });
          // Add only enough files to reach the limit
          const availableSlots = 3 - attachments.length;
          if (availableSlots > 0) {
            setAttachments([
              ...attachments,
              ...newFiles.slice(0, availableSlots),
            ]);
          }
        } else {
          setAttachments([
            ...attachments,
            ...newFiles,
          ]);
        }
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: "Failed to select document. Please try again.",
        position: 'bottom'
      });
    }
  }

  const handleDeleteAttachment = (uriToDelete: string) => {
    setAttachments(
      attachments.filter((file) => file.uri !== uriToDelete)
    );
  };

  const renderAttachments = () => {
    if (attachments.length === 0) return null;

    return attachments.map((file) => (
      <View key={file.uri} style={[styles.attachedFileContent,{
        backgroundColor:theme.background.secondary,
        borderColor:theme.border.primary
      }]}>
        {
          file.mimeType?.startsWith("image/") ? (
            <Entypo name="image" size={20} color={theme.icon.secondary} />
          ) : <Ionicons name="document-text"size={20} color={theme.icon.secondary} />
        }
        <View style={styles.fileInfoContainer}>
          <Text
            style={[styles.attachedFileName,{
              color:theme.text.primary
            }]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {file.name}
          </Text>
        </View>
        <TouchableOpacity onPress={() => handleDeleteAttachment(file.uri)} disabled={isSendingQuery}>
          <FontAwesome6 name="trash-alt" size={16} color={theme.icon.secondary} />
        </TouchableOpacity>
      </View>
    ));
  };

  const handleSendQuery = async () => {
    setIsSendingQuery(true);
    const formData = new FormData();
    formData.append("category", category);
    formData.append("description", description);

    attachments.forEach((file, index) => {
      const fileToUpload = {
        uri: file.uri,
        name: file.name || `file_${index}.${file.uri.split(".").pop()}`,
        type: file.mimeType || "application/octet-stream",
      } as unknown as Blob;
      
      formData.append("attachments", fileToUpload);
    });

    const response = await helpFAQService.sendContactQuery({formData});
    setIsSendingQuery(false);
    if(response.success){
      setShowSuccessullySentModal(true);
      onClose();
    }
    else {
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: response.error.message,
        position: 'bottom'
      });
    }
  }

  if(showSuccessullySentModal) return (
    <CustomModal
      visible={showSuccessullySentModal}
      onClose={() => setShowSuccessullySentModal(false)}
      title="Query sent"
      subtitle=""
      desc='Thanks for reaching out, we will get back to you shortly through your mail.'
      showBackBtn={false}
      icon={
        <View style={styles.querySentIconContainer}>
          <Fontisto name="email" size={120} color={theme.icon.primary}
            style={{
              alignSelf: "center"
            }}
          />
          <View style={styles.sendMessageContainer}>
            <Text style={[styles.sentText, { color: theme.text.primary }]}>
              Sent
            </Text>
            <MaterialCommunityIcons name="checkbox-marked-circle" size={18} color={theme.toast.success} />
          </View>
        </View>
      }
      buttons={
        <SimpleBtn
          title='Okay'
          onPress={() => {
            setShowSuccessullySentModal(false)
            setIsSendingQuery(false);
            setCategory("");
            setDescription("");
            setAttachments([]);
            onClose();
          }}
          containerStyle={{
            width:"auto",
            alignSelf:"center",
            paddingHorizontal:48
          }}
        />
      }
      innerContainerStyle={{width:"85%"}}
    />
  )

  return (
    <CustomModal
      visible={isVisible}
      onClose={onClose}
      onContentPress={() => {
        setCurrentSelectedDropdownId(null);
      }}
      title="Send a query"
      subtitle="write to us"
      isLoading={isSendingQuery}
      icon={
        <View style={styles.mainContainer}>
            <SelectBoxWithLabel
                label='Category'
                placeholder='Select category'
                options={[
                  { label: "Device problem", value: "Device problem" },
                  { label: "Data inaccuracies", value: "Data inaccuracies" },
                  { label: "App suggestions", value: "App suggestions" },
                  { label: "Lagging and glitches", value: "Lagging and glitches" },
                  { label: "Other", value: "Other" }
                ]}
                selectedValue={category}
                onValueChange={(value) => {
                    setCategory(value as string);
                }}
                dropdownId={"category"}
                currentOpenDropdown={currentSelectedDropdownId}
                setCurrentOpenDropdown={setCurrentSelectedDropdownId}
                triggerZ={8}
                listZ={9}
                maxOptionsToShow={5}
            />
            <TextAreaWithLabel
                label='Description'
                placeholder='Enter description'
                selected={description}
                setSelected={setDescription}
                numberOfLines={3}
            />
            <View style={styles.attachmentsContainer}>
                <Text style={styles.attachmentsText}>Attachments</Text>
                <SimpleBtn
                  title='Choose files'
                  onPress={handleSelectAttachment}
                  containerBgColor='quinary'
                  titleTextColor='tertiary'
                  containerStyle={styles.addAttachmentBtn}
                  titleTextStyle={styles.addAttachmentBtnText}
                  icon={<Entypo name="attachment" size={20} color={theme.icon.secondary} />}
                  iconPosition='right'
                />
                <View style={styles.attachmentsListContainer}>
                  {renderAttachments()}
                </View>
            </View>
        </View>
      }
      buttons={
        <View style={styles.btnContainer}>
          <SimpleBtn
            title='Send'
            onPress={handleSendQuery}
            containerStyle={styles.sendBtn}
          />
        </View>
      }
      innerContainerStyle={{width:"85%"}}
    />
  )
}

export default ContactUsModal

const styles = StyleSheet.create({
    btnContainer:{
        flexDirection:"row",
        marginTop:8,
        justifyContent:"flex-end"
    },
    sendBtn: {
        width:"auto",
        paddingHorizontal:32,
    },
    mainContainer:{
        gap:16
    },
    attachmentsContainer:{
      // gap:8
    },
    attachmentsText:{
      fontSize:16,
      fontFamily:AppFonts.HelixaBold,
      marginLeft:8,
    },
    addAttachmentBtn:{
      paddingHorizontal:8,
      paddingVertical:2,
      borderRadius:8,
      justifyContent:"space-between",
      alignItems:"flex-start"
    },
    addAttachmentBtnText:{
      flex:1,
      textAlign:"left",
      // fontSize:14,
      fontFamily:AppFonts.HelixaBoldItalic,
    },
    attachmentsListContainer:{
      marginTop:8
    },
    attachedFileContent: {
      marginTop: 5,
      flexDirection: "row",
      alignItems: "center",
      // padding: 5,
      paddingHorizontal: 4,
      paddingVertical: 6,
      borderWidth: 2,
      borderColor: 'red',
      borderRadius: 10,
    },
    fileInfoContainer: {
      flex: 1,
      marginHorizontal: 10,
    },
    attachedFileName: {
      fontSize: 12,
      fontFamily: AppFonts.HelixaBold,
      marginLeft: -1,
    },
    querySentIconContainer:{
    },
    sendMessageContainer:{
      flexDirection:"row",
      justifyContent:"center",
      alignItems:"center",
      gap:4
    },
    sentText:{
      fontSize:16,
      fontFamily:AppFonts.HelixaBold,
      textAlign:"center"
    }
})