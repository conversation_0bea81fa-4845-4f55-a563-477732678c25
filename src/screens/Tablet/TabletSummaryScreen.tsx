import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import AppLayout from '../../navigation/components/Layouts/AppLayout';
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import { MainStackParamList } from '../../navigation/MainStack';
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import BulletPointText from './components/BulletPointText';
import DateIntervalSelector from '../../components/Graphs/DateIntervalSelector';
import PeriodSelector from '../../components/Graphs/PeriodSelector';
import HorizontalSelector from '../../components/Graphs/HorizontalSelector';
import WeeklyTabletGraph from './components/WeeklyTabletGraph';

type MainStackNavigationProp = NativeStackNavigationProp<MainStackParamList>;

const tablets = [
  {
    label:'Vitals',
    value:'Vitals'
  },
  {
    label:'Tablet A',
    value:'Tablet A'
  },
  {
    label:'Tablet B',
    value:'Tablet B'
  },
  {
    label:'Tablet C',
    value:'Tablet C'
  },
  {
    label:'Tablet D',
    value:'Tablet D'
  },
  {
    label:'Tablet E',
    value:'Tablet E'
  },
  {
    label:'Tablet F',
    value:'Tablet F'
  },
  {
    label:'Tablet G',
    value:'Tablet G'
  },
  {
    label:'Tablet H',
    value:'Tablet H'
  },
]

const TabletSummaryScreen = () => {
  const { theme } = useTheme();

  const [ startDate, setStartDate ] = useState(new Date());
  const [ selectedPeriod, setSelectedPeriod ] = useState<'Week' | 'Month' | '6 Month'>('Week');
  const [ selectedTablet, setSelectedTablet ] = useState('Vitals');

  const tabletDetails = {
    cartridgeId: 'CART001',
    tablets: 30,
    cartridgeDetails: {
      id: '507f1f77bcf86cd799439012',
      healthName: 'Vitamin D3',
      healthArea: 'Vitals',
      flavour: 'Natural',
      colour: {
        cartridge: '#6B9E78',
        tablet: '#A8D5B0'
      },
      nutrients: [
        {
          name: 'Vitamin D3',
          rda: {
            quantity: 1000,
            unit: 'IU',
            percentage: 125
          }
        },
        {
          name: 'Calcium',
          rda: {
            quantity: 1000,
            unit: 'mg',
            percentage: null
          }
        }
      ]
    },
    currentCount: 5
  }

  return (
    <AppLayout>
      <ScrollView style={styles.container} contentContainerStyle={styles.containerContent}>
        <View style={[styles.card, { backgroundColor:theme.background.primary }]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerDetails}>
              <Text style={[styles.title,{color:tabletDetails.cartridgeDetails.colour.cartridge}]}>Summary</Text>
              <Text style={[styles.subtitle,{color:theme.text.quaternary}]} numberOfLines={1} ellipsizeMode='tail'>{`(${tabletDetails.cartridgeDetails.healthArea})`}</Text>
            </View>
            <DateIntervalSelector startDate={startDate} setStartDate={setStartDate} selectedPeriod={selectedPeriod}/>
          </View>
          
          <View style={styles.graphDetailsWrapper}>
            <HorizontalSelector options={tablets} selectedOption={selectedTablet} setSelectedOption={setSelectedTablet}/>
            <WeeklyTabletGraph/>
            <PeriodSelector selectedPeriod={selectedPeriod} setSelectedPeriod={setSelectedPeriod}/>
          </View>
        </View>
    
      {/* Nutrients Section */}
      <View style={[styles.highlightSection, { backgroundColor:theme.background.primary }]}>
        <Text style={[styles.highlightTitle, { color: theme.text.primary }]}>Highlights</Text>
        <View style={styles.highlightsContainer}>
            <BulletPointText text="Vitamin D3 is essential for bone health, immune function, and mood regulation."/>
            <BulletPointText text="Calcium is crucial for maintaining strong bones and teeth."/>
        </View>
      </View>
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  containerContent:{
    flexGrow:1,
    padding: 8,
    gap:12,
    paddingBottom:90
  },
  card: {
    borderRadius: 20,
    padding: 16,
    paddingHorizontal:20,
    paddingTop:20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerDetails:{
    flex:1,
    flexDirection:"row",
    alignItems:"center",
    gap:8,
    flexWrap:"wrap"
  },
  title: {
    fontSize: 24,
    fontFamily: AppFonts.HelixaBold,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: AppFonts.HelixaRegular,
    marginTop: 2,
  },
  currTimeIntervalWrapper:{
    flex:1,
    flexDirection:"row",
    alignItems:"center",
    justifyContent:"flex-end",
    // gap:2
  },
  currTimeText:{
    fontSize: 10,
    fontFamily: AppFonts.HelixaRegular,
  },
  graphDetailsWrapper:{
    gap:12,
  },
  summaryIconWrapper:{
    elevation:2,
    left:6
  },
  summaryIconButton: {
    padding: 8,
    borderRadius:100,
    overflow:"hidden",
    elevation:2
  },
  summaryIconContainer:{
    padding:2,
    borderWidth:1.5,
    borderRadius:8,
  },
  summaryIcon: {
    borderRadius:4
  },
  cartridgeSection: {
    alignItems: 'center',
    marginVertical: 28,
    marginTop:8
  },
  cartridgeContainer: {
    width: 41,
    height: 139,
    borderRadius: 8,
    borderWidth: 4,
    paddingBottom: 12,
  },
  tabletContainer: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    padding: 2,
    gap: 2,
  },
  tablet: {
    flex: 1,
    width: '100%',
  },
  cartridgeInfo: {
    alignItems: 'center',
    marginBottom: 16,
  },
  highlightSection: {
    padding:16,
    borderRadius:16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  highlightTitle: {
    fontSize: 24,
    fontFamily: AppFonts.HelixaBold,
    marginBottom: 12,
  },
  highlightsContainer:{
    flexDirection:"column",
    flexWrap:"wrap",
    gap:4,
    marginTop:4
  },
  highlightContainer:{
    flexDirection:"row",
    gap:8,
  }
});

export default TabletSummaryScreen;
