import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, RefreshControl, FlatList } from 'react-native';
import CartridgeUI from '../../components/Icons/Cartridge/CartridgeUI';
import AppLayout from '../../navigation/components/Layouts/AppLayout';
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import AddCartridgeModal from '../../components/Modals/Tablet/AddCartridgeModal';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { MainStackParamList } from '../../navigation/MainStack';
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import useGetCartridgeModalDetailsData from './hooks/getCartridgeModalDetailsData';
import PageLoader from '../../components/Loaders/PageLoader';

type MainStackNavigationProp = NativeStackNavigationProp<MainStackParamList>;

const TabletTrackingScreen = () => {
  const { params:{cartridgeModalId} }  = useRoute<RouteProp<MainStackParamList, 'Tablet Tracking'>>();
  const navigation = useNavigation<MainStackNavigationProp>();

  const { theme } = useTheme();

  const { loading,refreshing,tabletDetails,error,getCartridgeModalDetails } = useGetCartridgeModalDetailsData(cartridgeModalId);
  
  const [ stockUpModalVisible, setStockUpModalVisible ] = React.useState(false);

  if(loading) return (
    <View style={[styles.centerContainer,{paddingBottom:90}]}>
      <PageLoader overlayBg='trasparent'/>
    </View>
  );

  if(error || !tabletDetails) return (
    <AppLayout>
      <View style={[styles.centerContainer,{paddingHorizontal:24,paddingBottom:90}]}>
        <Text style={styles.errorMessageText}>{error || "Something went wrong. Please try again."}</Text>
        <SimpleBtn
          title='Retry' 
          onPress={() => {getCartridgeModalDetails()}} 
          containerBgColor='primary' 
          titleTextColor='secondary' 
          containerStyle={styles.viewMoreButton} 
        />
      </View>
    </AppLayout>
  );

  return (
    <AppLayout>
      <ScrollView 
        style={styles.container} contentContainerStyle={styles.containerContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={()=>getCartridgeModalDetails({refreshing:true})}
            colors={[theme.button.primary]}
            progressViewOffset={24}
          />
        }
      >
        <View style={[styles.card, { backgroundColor:theme.background.primary }]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerDetails}>
              <Text style={[styles.title,{color:tabletDetails.cartridge.colour.cartridge}]}>{tabletDetails.cartridge.healthName}</Text>
              <Text style={[styles.subtitle,{color:theme.text.quaternary}]}>{`(${tabletDetails.cartridge.healthArea})`}</Text>
            </View>
            <View style={styles.summaryIconWrapper}>
              <TouchableOpacity 
                onPress={() => {
                  navigation.navigate('Tablet Summary');
                }}
                style={[styles.summaryIconButton,{
                  backgroundColor:theme.background.primary
                }]}
              >
              <View style={[styles.summaryIconContainer,{ backgroundColor: theme.button.secondary,borderColor:theme.border.tertiary }]}>
                <MaterialCommunityIcons name="text-long" size={20} color="black" style={[styles.summaryIcon, { backgroundColor: theme.icon.quinary }]} />
              </View>
            </TouchableOpacity>
            </View>
          </View>

          {/* Cartridge Display */}
          <View style={styles.cartridgeSection}>
            <CartridgeUI 
              cartidgeColor={tabletDetails.cartridge.colour.cartridge}
              tabletColor={tabletDetails.cartridge.colour.tablet}
              numTablets={tabletDetails.quantity}
              containerStyle={{height:150,width:41,borderRadius:6}}
              tabletContainerStyle={{padding:1,borderRadius:6,gap:1}}
              borderTopEndRadius={6}
              borderTopStartRadius={6}
              borderBottomEndRadius={6}
              borderBottomStartRadius={6}
            />
          </View>

          {/* Cartridge Info */}
          <View style={styles.cartridgeInfo}>
            <View style={styles.cartridgeDetails}>
              <Text style={styles.cartridgeLabel}>CARTRIDGE</Text>
              <Text style={styles.cartridgeName}>{tabletDetails.cartridge.healthName}</Text>
            </View>
            <Text style={styles.cartridgeQuantity}>{`${tabletDetails.quantity.toString().padStart(2,'0')} / 05`}</Text>
          </View>

          {/* Status Bar */}
          <View style={styles.statusBar}>
            <View style={styles.statusLeft}>
              <CartridgeUI 
                cartidgeColor={tabletDetails.cartridge.colour.cartridge}
                tabletColor={tabletDetails.cartridge.colour.tablet} 
                numTablets={tabletDetails.quantity} 
                containerStyle={{width:12,height:44,borderWidth:2,paddingBottom:2,borderRadius:4,elevation:0}}
                tabletContainerStyle={{padding:1,borderRadius:2,gap:1}}
                borderTopEndRadius={2}
                borderTopStartRadius={2}
                borderBottomEndRadius={2}
                borderBottomStartRadius={2}
              />
              <View style={styles.statusDetailsContainer}>
                <Text style={styles.statusNumber}>{tabletDetails.quantity}</Text>
                <View style={styles.statusTextContainer}>
                  <Text style={[styles.statusLabel,{color:theme.text.tertiary}]}>Cartridges</Text>
                  <Text style={[styles.statusLabel,{color:theme.text.tertiary}]}>remaining</Text>
                </View>
              </View>
            </View>
            <SimpleBtn 
              title='Stock up' 
              onPress={() => {setStockUpModalVisible(true)}} 
              containerBgColor='primary' 
              titleTextColor='secondary' 
              containerStyle={styles.stockButton} 
              icon={<MaterialCommunityIcons name="cart-outline"size={20} color={theme.icon.tertiary} />}
            />
          </View>
        </View>
    
      {/* Nutrients Section */}
      <View style={[styles.nutrientsSection, { backgroundColor:theme.background.primary }]}>
        <Text style={[styles.nutrientsTitle, { color: theme.text.primary }]}>About Nutrients</Text>
        
        <View style={styles.nutrientsHeader}>
          <Text style={[styles.nutrientsHeaderText, { flex: 1,color: theme.text.primary }]}>Nutrients</Text>
          <Text style={[styles.nutrientsHeaderText, { flex: 1,color: theme.text.primary, textAlign: 'center' }]}>Quantity</Text>
          <Text style={[styles.nutrientsHeaderText, { flex: 1,color: theme.text.primary, textAlign: 'right' }]}>RDA's</Text>
        </View>

        <FlatList
          data={tabletDetails.cartridge.nutrients.slice(0,3)}
          keyExtractor={(item) => item.name}
          scrollEnabled={false}
          renderItem={({ item }) => (
            <View style={styles.nutrientRow}>
              <Text style={[styles.nutrientText, { flex: 1, color: theme.text.quaternary }]} numberOfLines={1}>
                {item.name}
              </Text>
              <Text style={[styles.nutrientText, { flex: 1,color: theme.text.quaternary, textAlign: 'center' }]}>
                {item.rda.quantity} {item.rda.unit}
              </Text>
              <Text style={[styles.nutrientText, { flex: 1,color: theme.text.quaternary, textAlign: 'right' ,paddingHorizontal:item.rda.percentage ? 0 : 0 }]}>
                {item.rda.percentage ? item.rda.percentage + '%' : '  -     '}
              </Text>
            </View>
          )}
        />

        <SimpleBtn
          title='View More >' 
          onPress={() => {
            navigation.navigate('Tablet All Details',{cartridgeModalId:'68d2447c8fe5706e17bffb6b'});
          }} 
          containerBgColor='secondary' 
          titleTextColor='primary' 
          containerStyle={styles.viewMoreButton} 
        />
        <AddCartridgeModal 
          cartridgeDetails={stockUpModalVisible?{
            id:tabletDetails.cartridge.id,
            name:tabletDetails.cartridge.healthName,
            numTablets:tabletDetails.quantity,
            cartidgeColor:tabletDetails.cartridge.colour.cartridge,
            tabletColor:tabletDetails.cartridge.colour.tablet
          }:null}  
          onClose={() => setStockUpModalVisible(false)} 
          onSuccess={() => {
            getCartridgeModalDetails({refreshing:true});
            setStockUpModalVisible(false);
          }}
        />
      </View>
      </ScrollView>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  centerContainer : {
    flex:1,
    justifyContent:"center",
    alignItems:"center",
  },
  errorMessageText:{
    fontSize:20,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center"
  },
  container: {
    flex: 1,
  },
  containerContent:{
    flexGrow:1,
    padding: 8,
    gap:12,
    paddingBottom:90
  },
  card: {
    borderRadius: 20,
    padding: 16,
    paddingHorizontal:20,
    paddingTop:20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerDetails:{
    flexDirection:"row",
    alignItems:"center",
    gap:8
  },
  title: {
    fontSize: 24,
    fontFamily: AppFonts.HelixaBold,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: AppFonts.HelixaRegular,
    marginTop: 2,
  },
  summaryIconWrapper:{
    elevation:2,
    left:6
  },
  summaryIconButton: {
    padding: 8,
    borderRadius:100,
    overflow:"hidden",
    elevation:2
  },
  summaryIconContainer:{
    padding:2,
    borderWidth:1.5,
    borderRadius:8,
  },
  summaryIcon: {
    borderRadius:4
  },
  cartridgeSection: {
    alignItems: 'center',
    marginVertical: 28,
    marginTop:8
  },
  cartridgeContainer: {
    width: 41,
    height: 139,
    borderRadius: 8,
    borderWidth: 4,
    paddingBottom: 12,
  },
  tabletContainer: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    padding: 2,
    gap: 2,
  },
  tablet: {
    flex: 1,
    width: '100%',
  },
  cartridgeInfo: {
    alignItems: 'center',
    marginBottom: 16,
  },
  cartridgeDetails:{
    flexDirection:"row",
    justifyContent:"center",
    alignItems:"center",
    gap:4,
  },
  cartridgeLabel: {
    fontSize: 12,
    fontFamily: AppFonts.HelixaRegular,
    lineHeight: 14,
  },
  cartridgeName: {
    fontSize: 14,
    fontFamily: AppFonts.HelixaBold,
    textTransform:"uppercase",
    textAlign:"center"
  },
  cartridgeQuantity: {
    fontSize: 16,
    fontFamily: AppFonts.HelixaBold,
    marginTop:4
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth:2,
    borderColor:"#6B9E78"
  },
  statusLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconText: {
    fontSize: 20,
  },
  statusDetailsContainer:{
    flexDirection:"row",
    justifyContent:"center",
    alignItems:"center",
    gap:4
  },
  statusNumber: {
    fontSize: 28,
    fontFamily: AppFonts.HelixaBold,
  },
  statusTextContainer:{
    flexDirection:"column",
    justifyContent:"center",
    alignItems:"flex-start",
  },
  statusLabel: {
    fontSize: 12,
    fontFamily: AppFonts.HelixaRegular,
    lineHeight: 14,
  },
  stockButton: {
    width:'auto',
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  stockButtonText: {
    color: '#FFF',
    fontFamily: AppFonts.HelixaBold,
    fontSize: 14,
  },
  nutrientsSection: {
    padding:16,
    borderRadius:16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  nutrientsTitle: {
    fontSize: 18,
    fontFamily: AppFonts.HelixaBlack,
    marginBottom: 12,
  },
  nutrientsHeader: {
    flexDirection: 'row',
    paddingHorizontal:4
  },
  nutrientsHeaderText: {
    fontSize: 14,
    fontFamily: AppFonts.HelixaBold,
    paddingBottom:4
  },
  nutrientRow: {
    flexDirection: 'row',
    paddingBottom: 4,
    paddingHorizontal:4,
  },
  nutrientText: {
    fontSize: 12,
    fontFamily: AppFonts.HelixaRegular,
  },
  viewMoreButton: {
    width:'auto',
    paddingHorizontal: 32,
    alignSelf: 'center',
    marginTop: 8,
  },
});

export default TabletTrackingScreen;
