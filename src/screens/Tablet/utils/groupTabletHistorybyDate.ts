import { format, isToday, isYesterday } from 'date-fns';
import { DispenseHistoryRecordType } from '../../../apis/dispense/schema/dispenseHIstoryResponseSchema';

export type GroupedDispenseHistory = {
  title: string;
  data: DispenseHistoryRecordType[];
};

export function groupDispenseHistoryByDate(
  records: DispenseHistoryRecordType[]
): GroupedDispenseHistory[] {
  const groups: Record<string, DispenseHistoryRecordType[]> = {};

  records.forEach((record) => {
    const date = new Date(record.dispenseDate);
    let key: string;

    if (isToday(date)) {
      key = 'Today';
    } else if (isYesterday(date)) {
      key = 'Yesterday';
    } else {
      key = format(date, 'do MMMM, yyyy'); // e.g. "19th August, 2025"
    }

    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(record);
  });

  const groupedArray = Object.entries(groups).map(([title, data]) => ({
    title,
    data,
  }));

  // Explicit sort order for Today/Yesterday
  const order: { Today: number; Yesterday: number } = { Today: 0, Yesterday: 1 };

  return groupedArray.sort((a, b) => {
    const aInOrder = a.title in order;
    const bInOrder = b.title in order;

    // Both are Today/Yesterday → compare with map
    if (aInOrder && bInOrder) {
      return (order as Record<string, number>)[a.title] - (order as Record<string, number>)[b.title];
    }

    // One of them is Today/Yesterday → that one goes first
    if (aInOrder) return -1;
    if (bInOrder) return 1;

    // Otherwise compare actual dates (descending)
    const parseTitle = (title: string) =>
      new Date(title.replace(/(\d+)(st|nd|rd|th)/, '$1'));

    return parseTitle(b.title).getTime() - parseTitle(a.title).getTime();
  });
}
