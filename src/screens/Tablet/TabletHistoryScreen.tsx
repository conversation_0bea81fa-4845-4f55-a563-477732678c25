import { RefreshControl, ScrollView, StyleSheet, Text, View } from 'react-native'
import React, { useState } from 'react'
import AppLayout from '../../navigation/components/Layouts/AppLayout';
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import TabletHistorySection from './components/TabletHistorySection';
import CustomModal from '../../components/Modals/CustomModal';
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import { Ionicons } from '@expo/vector-icons';
import useGetDispenseHistoryData from './hooks/getDispenseHistoryData';
import PageLoader from '../../components/Loaders/PageLoader';
import dispenseService from '../../apis/dispense/dispenseService';


const TabletHistoryScreen = () => {
  const { theme } = useTheme();

  const { dispenseHistory,loading,error,refreshing,handleRefresh,} = useGetDispenseHistoryData();

  const [ deleteLogId,setDeleteLogId ] = useState<string | null>(null);
  const [ isDeletingLog,setIsDeletingLog ] = useState(false);

  const handleDeleteLog = async () => {
    if(!deleteLogId) return;

    setIsDeletingLog(true);
    const response = await dispenseService.deleteDispenseRecord(deleteLogId);

    if(response.success){
      await handleRefresh();
    }
    setIsDeletingLog(false);
    setDeleteLogId(null);
  }

  return (
    <AppLayout>
        <View style={styles.wrapper}>
          <ScrollView 
            style={[styles.container, {
              backgroundColor: theme.background.primary
            }]} 
            contentContainerStyle={styles.containerContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[theme.button.primary]}
                progressViewOffset={24}
              />
            }
          >
            <Text style={[styles.headingText,{
              color:theme.text.primary
            }]}>Recent Dispense:</Text>
            <View style={styles.mainContainer}>
              {
                loading?(
                  <PageLoader overlayBg='trasparent'/>
                ) : (
                  error ? (
                    <View style={{flex:1,justifyContent:"center",alignItems:"center"}}>
                      <Text style={[styles.errorText,{
                        color:theme.text.primary
                      }]}>{error}</Text>
                    </View>
                  ) : (
                    dispenseHistory.length === 0 ? (
                      <View style={{flex:1,justifyContent:"center",alignItems:"center"}}>
                        <Text style={[styles.errorText,{
                          color:theme.text.primary
                        }]}>No records found</Text>
                      </View>
                    ) : (
                      <TabletHistorySection dispenseHistory={dispenseHistory} setDeleteLogId={(value)=>{
                        if(refreshing) return;
                        setDeleteLogId(value);
                      }}/>
                    )
                  )
                )
              }

            </View>
          </ScrollView>
          <CustomModal
            visible={deleteLogId!=null}
            onClose={() => setDeleteLogId(null)}
            title="Delete Log"
            subtitle=""
            desc="Are you sure you want to delete this log?"
            icon = {
              <Ionicons name="warning-outline" size={120} color={theme.icon.primary}
                style={{
                  alignSelf: "center"
                }}
              />
            }
            isLoading={isDeletingLog}
            buttons={
              <View style={styles.deleteLogModalBtnsContainer}>
                <SimpleBtn
                  title='Cancel'
                  onPress={() => {
                    setDeleteLogId(null);
                  }}
                  containerStyle={{
                    flex: 1,
                  }}
                  containerBgColor='secondary'
                  titleTextColor='primary'
                  disabled={isDeletingLog}
                  disabledBgColor='quinary'
                  disabledTitleColor='primary'
                />
                <SimpleBtn
                  title='Delete'
                  onPress={() => {
                    handleDeleteLog();
                  }}
                  containerStyle={{
                    flex: 1,
                  }}
                  disabled={isDeletingLog}
                />
              </View>
            }
          />
        </View>
    </AppLayout>
  )
}

export default TabletHistoryScreen

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    padding:4,
    paddingTop:12,
    paddingBottom:90
  },
  container: {
    flex: 1,
    elevation:2,
    borderRadius:19,
  },
  containerContent:{
    flexGrow:1,
    gap:8,
    padding:24,
  },
  mainContainer:{
    flex:1,
  },
  itemContainer:{
  },
  headingText:{
    fontSize:24,
    fontFamily:AppFonts.HelixaBold,
  },
  deleteLogModalBtnsContainer:{
    flexDirection:"row",
    gap:16,
  },
  errorText:{
    fontSize:20,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center"
  }
})