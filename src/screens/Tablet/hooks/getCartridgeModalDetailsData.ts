
import { useEffect, useState } from 'react';
import cartridgeService from '../../../apis/cartridge/cartridgeService';
import { CartridgeModalDetailsResponseType } from '../../../apis/cartridge/schema/cartridgeModalDetailsResponseSchema';

const useGetCartridgeModalDetailsData = (cartridgeModalId:string) => {
    const [tabletDetails, setTabletDetails] = useState<CartridgeModalDetailsResponseType | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [refreshing, setRefreshing] = useState(false);
    
    const getCartridgeModalDetails = async ({refreshing}:{refreshing?:boolean}={refreshing:false}) => {
        if(!cartridgeModalId) return;
        refreshing?setRefreshing(true):setLoading(true);

        setError(null);

        try {
            const response = await cartridgeService.getCartridgeModalDetails(cartridgeModalId);

            if (response.success) {
                setTabletDetails(response.data);
            } else {
                setError(response.error?.message || 'Failed to fetch data');
            }
        } catch (err: any) {
            setError(err.message || 'Unexpected error');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useEffect(() => {
        getCartridgeModalDetails();
    }, [cartridgeModalId]);

    return {
        tabletDetails,
        loading,
        refreshing,
        error,
        getCartridgeModalDetails
    };
};

export default useGetCartridgeModalDetailsData;