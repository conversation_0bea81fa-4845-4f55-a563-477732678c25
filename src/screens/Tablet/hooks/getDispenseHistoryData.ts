import { useEffect, useState } from 'react';
import dispenseService from '../../../apis/dispense/dispenseService';
import { groupDispenseHistoryByDate, GroupedDispenseHistory } from '../utils/groupTabletHistorybyDate';

const useGetDispenseHistoryData = () => {
    const [dispenseHistory, setDispenseHistory] = useState<GroupedDispenseHistory[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [refreshing, setRefreshing] = useState(false);

    const getDispenseHistory = async (isRefreshing = false) => {
        if(!isRefreshing) setLoading(true); 
        setError(null);

        try {
            const response = await dispenseService.getDispenseHistory();

            if (response.success) {
                setDispenseHistory(groupDispenseHistoryByDate(response.data.dispenseHistory || []));
            } else {
                setError(response.error?.message || 'Failed to fetch data');
            }
        } catch (err: any) {
            setError(err.message || 'Unexpected error');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        await getDispenseHistory(true);
    };

    useEffect(() => {
        getDispenseHistory();
    }, []);

    return {
        dispenseHistory,
        loading,
        error,
        refreshing,
        handleRefresh,
    };
};

export default useGetDispenseHistoryData;
