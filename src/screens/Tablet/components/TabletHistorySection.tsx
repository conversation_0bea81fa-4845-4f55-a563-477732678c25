import React, { useCallback } from 'react';
import {
  StyleSheet,
  FlatList,
} from 'react-native';
import TabletHistoryDayLogs from './TabletHistoryDayLogs';
import { GroupedDispenseHistory } from '../utils/groupTabletHistorybyDate';
interface TabletHistorySectionProps {
  dispenseHistory:GroupedDispenseHistory[];
  setDeleteLogId:(id:string|null)=>void;
}
const TabletHistorySection = ({dispenseHistory,setDeleteLogId}:TabletHistorySectionProps) => {
  

  return (
    <FlatList
        data={dispenseHistory}
        keyExtractor={(item) => item.title}
        renderItem={({ item }) => (
          <TabletHistoryDayLogs item={item} setDeleteLogId={setDeleteLogId}/>
        )}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  listContainer: {
    flexGrow:1
  },
});

export default TabletHistorySection;