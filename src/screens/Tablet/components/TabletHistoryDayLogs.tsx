import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useState } from 'react'
import TabletHistoryLogCard from './TabletHistoryLogCard'
import { AppFonts } from '../../../constants/theme/fonts/fonts'
import { Feather } from '@expo/vector-icons'
import { useTheme } from '../../../context/ThemeContext'
import { GroupedDispenseHistory } from '../utils/groupTabletHistorybyDate'

interface TabletHistoryDayLogsProps {
  item:GroupedDispenseHistory,
  setDeleteLogId:(id:string|null)=>void;
}

const TabletHistoryDayLogs = ({item,setDeleteLogId}:TabletHistoryDayLogsProps) => {
  const { theme } = useTheme();
  const [showLogs, setShowLogs] = useState(true);

  const toggleLogs = () => {
    setShowLogs(!showLogs);
  };

  return (
    <View>
        <TouchableOpacity onPress={toggleLogs}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle,{color:theme.text.quaternary}]}>{item.title}</Text>
            {
              showLogs ? (
                <Feather name="chevron-up" size={20} color={theme.icon.secondary} />
              ) : (
                <Feather name="chevron-down" size={20} color={theme.icon.secondary} />
              )
            }
          </View>
        </TouchableOpacity>
        {
          showLogs ? (
            <FlatList
                data={item.data}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TabletHistoryLogCard data={item} setDeleteLogId={setDeleteLogId}/>
                )}
                showsVerticalScrollIndicator={false}
                scrollEnabled={false}
            />
          ) : null
        }
    </View>
  )
}

export default TabletHistoryDayLogs

const styles = StyleSheet.create({
  sectionHeader: {
    marginBottom: 12,
    marginTop: 12,
    flexDirection:"row",
    alignItems:"flex-end",
  },
  sectionTitle: {
    fontSize: 16,
    marginLeft: 4,
    fontFamily:AppFonts.HelixaBoldItalic
  },
})