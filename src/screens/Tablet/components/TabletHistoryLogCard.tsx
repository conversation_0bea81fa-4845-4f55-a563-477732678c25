import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React from 'react'
import { Ionicons, MaterialIcons } from '@expo/vector-icons'
import { useTheme } from '../../../context/ThemeContext';
import { AppFonts } from '../../../constants/theme/fonts/fonts';
import { DispenseHistoryRecordType } from '../../../apis/dispense/schema/dispenseHIstoryResponseSchema';
import getRelativeDate from '../../../utils/getRelativeDate';
import { getUserHourMinutes } from '../../../utils/getUserHourMinutes';
import TabletUI from '../../../components/Icons/Tablet/TabletUI';

interface TabletHistoryLogCardProps {
    data:DispenseHistoryRecordType,
    setDeleteLogId:(id:string|null)=>void;
}

const TabletHistoryLogCard = ({ data,setDeleteLogId }:TabletHistoryLogCardProps) => {
    const { theme } = useTheme();
    return (
      <View style={styles.itemContainer}>
        <View style={[styles.medicationItem]}>
          <View style={{position:"absolute",left:0,right:0,top:0,bottom:0,
            backgroundColor:data.cartridge.colour.tablet,
            opacity:.6,
            borderRadius:19
          }}>

          </View>
          <TabletUI 
            cartridgeColor={data.cartridge.colour.cartridge}
            tabletColor={data.cartridge.colour.tablet}
          />
          
          <View style={styles.medicationInfo}>
              <View style={styles.medicationNameContainer}>
                  <Text style={[styles.medicationName,{color:theme.text.primary}]}>{data.cartridge.healthName}</Text>
                  <Text style={[styles.medicationType,{color:theme.text.quaternary}]}>({data.cartridge.healthArea})</Text>
              </View>
              <Text style={[styles.medicationTime,{color:theme.text.quaternary}]}>{
                getUserHourMinutes(data.dispenseTime)
                }</Text>
          </View>
          
          <TouchableOpacity style={[styles.deleteButton, { backgroundColor: theme.button.secondary }]} onPress={()=>setDeleteLogId(data.id)}>
              <Ionicons name="trash-outline" size={26} color="black" />
          </TouchableOpacity>
        </View>
      </View>
    )
}

export default TabletHistoryLogCard

const styles = StyleSheet.create({
  itemContainer:{
    borderRadius:19,
    overflow:"hidden",
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    marginBottom: 8,
  },
  medicationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 19,
    backgroundColor: '#fff',
  },
  medicationInfo: {
    flex: 1,
    gap:2,
    marginLeft:8
  },
  medicationNameContainer:{
    flexDirection:"row",
    gap:4,
    alignItems:"baseline",
  },
  medicationName: {
    fontSize: 18,
    fontFamily:AppFonts.HelixaBlack,
  },
  medicationType: {
    fontSize: 12,
    fontFamily:AppFonts.HelixaBoldItalic,
  },
  medicationTime: {
    fontSize: 14,
    fontFamily:AppFonts.HelixaBoldItalic
  },
  deleteButton: {
    padding: 4,
    borderRadius: 20,
    elevation:2
  },
})