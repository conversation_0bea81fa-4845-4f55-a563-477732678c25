import { StyleSheet, View } from 'react-native'
import React from 'react'
import { Bar<PERSON><PERSON> } from 'react-native-gifted-charts'
import { useTheme } from '../../../context/ThemeContext'

const WeeklyTabletGraph = () => {
  const { theme } = useTheme()

  // Sample data matching the UI design
  const barData = [
    {
      value: 6,
      label: 'Mon',
      frontColor: '#9CA3AF', // Gray color
    },
    {
      value: 7,
      label: 'Tue',
      frontColor: '#9CA3AF', // Gray color
    },
    {
      value: 5,
      label: 'Wed',
      frontColor: '#F97316', // Orange color
    },
    {
      value: 8,
      label: 'Thurs',
      frontColor: '#84CC16', // Green color
    },
    {
      value: 4,
      label: 'Fri',
      frontColor: '#9CA3AF', // Gray color
    },
    {
      value: 6,
      label: 'Sat',
      frontColor: '#9CA3AF', // Gray color
    },
    {
      value: 6,
      label: 'Sun',
      frontColor: '#9CA3AF', // Gray color
    },
  ]

  return (
    <View style={styles.container}>
      <BarChart
        data={barData}
        width={320}
        height={220}
        barWidth={32}
        spacing={24}
        roundedTop
        roundedBottom
        hideRules={false}
        xAxisThickness={1}
        yAxisThickness={1}
        xAxisColor={theme.text.secondary}
        yAxisColor={theme.text.secondary}
        yAxisTextStyle={{
          color: theme.text.secondary,
          fontSize: 12,
        }}
        xAxisLabelTextStyle={{
          color: theme.text.secondary,
          fontSize: 12,
          textAlign: 'center',
        }}
        noOfSections={4}
        maxValue={8}
        stepValue={2}
        initialSpacing={20}
        endSpacing={20}
        rulesColor={theme.text.secondary + '30'}
        rulesType="solid"
        showReferenceLine1={false}
        showReferenceLine2={false}
        showReferenceLine3={false}
      />
    </View>
  )
}

export default WeeklyTabletGraph

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
})