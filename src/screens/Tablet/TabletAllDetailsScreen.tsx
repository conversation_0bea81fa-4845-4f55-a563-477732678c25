import React, { useRef, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, FlatList, ViewToken } from 'react-native';
import CartridgeUI from '../../components/Icons/Cartridge/CartridgeUI';
import AppLayout from '../../navigation/components/Layouts/AppLayout';
import { useTheme } from '../../context/ThemeContext';
import { AppFonts } from '../../constants/theme/fonts/fonts';
import SimpleBtn from '../../components/Buttons/SimpleBtn';
import { Entypo, Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import AddCartridgeModal from '../../components/Modals/Tablet/AddCartridgeModal';
import TabletUI from '../../components/Icons/Tablet/TabletUI';
import NutrientDetailsModal from '../../components/Modals/Tablet/NutrientDetailsModal';
import { RouteProp, useRoute } from '@react-navigation/native';
import { MainStackParamList } from '../../navigation/MainStack';
import useGetCartridgeModalDetailsData from './hooks/getCartridgeModalDetailsData';
import PageLoader from '../../components/Loaders/PageLoader';

const TabletAllDetailsScreen = () => {
  const { params:{cartridgeModalId} }  = useRoute<RouteProp<MainStackParamList, 'Tablet All Details'>>();

  const { theme } = useTheme();
  const flatListRef = useRef<FlatList>(null);

  const { loading,refreshing,tabletDetails,error,getCartridgeModalDetails } = useGetCartridgeModalDetailsData(cartridgeModalId);

  const [ viewNutrientDetails, setViewNutrientDetails ] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAtEnd, setIsAtEnd] = useState(false);
  const [contentHeight, setContentHeight] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);

  // Update index whenever items change visibility
  const onViewableItemsChanged = useRef(
    ({ viewableItems }: { viewableItems: ViewToken[] }) => {
      if (viewableItems.length > 0 && viewableItems[0].index != null) {
        setCurrentIndex(viewableItems[0].index);
      }
    }
  );

  const viewConfigRef = useRef({ viewAreaCoveragePercentThreshold: 50 });

  const goToIndex = (newIndex: number) => {
    if(!tabletDetails?.cartridge?.nutrients?.length) return;
    if (newIndex < 0 || newIndex >= tabletDetails.cartridge.nutrients.length) return;
    try {
      flatListRef.current?.scrollToIndex({
        index: newIndex,
        animated: true,
        viewPosition: 0,
      });
      setCurrentIndex(newIndex);
    } catch (e) {
      console.warn("scrollToIndex failed, fallback:", e);
    }
  };

  // Check if scroll down button should be disabled
  const isScrollDownDisabled = () => {
    if (!tabletDetails?.cartridge?.nutrients?.length) return true;

    const totalItems = tabletDetails.cartridge.nutrients.length;

    // If we're near the end (can't scroll 10 more items)
    if (currentIndex + 10 >= totalItems) return true;

    // If we're at the end of scrollable content
    if (isAtEnd) return true;

    // If content fits entirely in container (no scrolling needed)
    if (contentHeight > 0 && containerHeight > 0 && contentHeight <= containerHeight) return true;

    return false;
  };

  // Handle FlatList layout to get container height
  const handleFlatListLayout = (event: any) => {
    const { height } = event.nativeEvent.layout;
    setContainerHeight(height);
  };

  // Handle content size change to get actual content height
  const handleContentSizeChange = (_width: number, height: number) => {
    setContentHeight(height);
  };

  // Handle scroll events to detect when we're at the end
  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isEndReached = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20; // 20px threshold
    setIsAtEnd(isEndReached);
  };

  if(loading) return (
    <View style={[styles.centerContainer,{paddingBottom:90}]}>
      <PageLoader overlayBg='trasparent'/>
    </View>
  );

  if(error || !tabletDetails) return (
    <AppLayout>
      <View style={[styles.centerContainer,{paddingHorizontal:24,paddingBottom:90}]}>
        <Text style={styles.errorMessageText}>{error || "Something went wrong. Please try again."}</Text>
        <SimpleBtn
          title='Retry' 
          onPress={() => {getCartridgeModalDetails()}} 
          containerBgColor='primary' 
          titleTextColor='secondary' 
          containerStyle={styles.viewMoreButton} 
        />
      </View>
    </AppLayout>
  );


  return (
    <AppLayout>
      <View style={styles.container}>
        <View style={[styles.card, { backgroundColor:theme.background.primary }]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerDetails}>
                <Text style={[styles.title,{color:tabletDetails.cartridge.colour.cartridge}]}>{`About ${tabletDetails.cartridge.healthName}`}</Text>
                <Text style={[styles.subtitle,{color:theme.text.quaternary}]}>{`(${tabletDetails.cartridge.healthArea})`}</Text>
            </View>
            <View>
                <Text style={[styles.totalCaloriesText,{color:theme.text.quinary}]}>Calories: 10 kcal</Text>
            </View>
          </View>

          {/* Cartridge Display */}
          <View style={styles.tabletDetailsSection}>
            <TabletUI 
              cartridgeColor={tabletDetails.cartridge.colour.cartridge}
              tabletColor={tabletDetails.cartridge.colour.tablet}
              outer={{borderRadius:14,borderWidth:3,padding:3,size:80}}
              inner={{borderRadius:8}}
            />
            <Text style={styles.tabletNameText}>{tabletDetails.cartridge.healthName}</Text>
            <Text style={styles.tabletDetailsText}>
                Super greens are powdered dietary supplements made from concentrated, nutrient-dense plant ingredients like wheatgrass, spirulina, and leafy greens, along with fruits and herbs, designed to offer vitamins, minerals, antioxidants, and other beneficial compounds in a convenient form.
            </Text>
          </View>
          
            {/* Nutrients Section */}
            <View style={[styles.nutrientsSection, { backgroundColor:theme.background.primary }]}>
                <Text style={[styles.nutrientsTitle, { color: theme.text.primary }]}>Nutrients</Text>
                
                <View style={styles.nutrientsHeader}>
                <Text style={[styles.nutrientsHeaderText, { flex: 1,color: theme.text.primary }]}>Nutrients</Text>
                <Text style={[styles.nutrientsHeaderText, { flex: 1,color: theme.text.primary, textAlign: 'center' }]}>Quantity</Text>
                <Text style={[styles.nutrientsHeaderText, { flex: 1,color: theme.text.primary, textAlign: 'right' }]}>RDA's</Text>
                </View>

                <FlatList
                    ref={flatListRef}
                    data={tabletDetails?.cartridge?.nutrients}
                    keyExtractor={(item) => item.name}
                    renderItem={({ item }) => (
                    <View style={styles.nutrientRow}>
                      <TouchableOpacity style={{flex: 1}} onPress={() => setViewNutrientDetails(item?.name)}>
                        <Text style={[styles.nutrientText, { color: tabletDetails.cartridge.colour.cartridge }]} numberOfLines={1}>
                          {item.name}
                        </Text>
                      </TouchableOpacity>
                      <Text style={[styles.nutrientText, { flex: 1,color: theme.text.quaternary, textAlign: 'center' }]}>
                      {item.rda.quantity} {item.rda.unit}
                      </Text>
                      <Text style={[styles.nutrientText, { flex: 1,color: theme.text.quaternary, textAlign: 'right', textDecorationLine:item.rda.percentage ? "underline" : "none",paddingHorizontal:item.rda.percentage ? 0 : 0 }]}>
                      {item.rda.percentage ? item.rda.percentage + '%' : '  -     '}
                      </Text>
                    </View>
                    )}
                    showsVerticalScrollIndicator={false}
                    onViewableItemsChanged={onViewableItemsChanged.current}
                    onLayout={handleFlatListLayout}
                    onContentSizeChange={handleContentSizeChange}
                    onScroll={handleScroll}
                    scrollEventThrottle={16}
                    viewabilityConfig={viewConfigRef.current}
                />
            </View>
            <TouchableOpacity
              activeOpacity={.5}
              onPress={() => {
                console.log('called')
                goToIndex(currentIndex + 10);
              }}
              style={[
                styles.scrollDownBtn,
                isScrollDownDisabled() && styles.disabledScrollBtn
              ]}
              disabled={isScrollDownDisabled()}
            >
              <Entypo
                name="chevron-thin-down"
                size={24}
                color={isScrollDownDisabled() ? "#ccc" : "black"}
              />
            </TouchableOpacity>

            <NutrientDetailsModal name={viewNutrientDetails} onClose={() => setViewNutrientDetails(null)}/>
        </View>

      </View>
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  centerContainer : {
    flex:1,
    justifyContent:"center",
    alignItems:"center",
  },
  errorMessageText:{
    fontSize:20,
    fontFamily:AppFonts.HelixaBold,
    textAlign:"center"
  },
  container: {
    flex: 1,
    padding: 8,
    gap:12,
    paddingBottom: 90
  },
  card: {
    flex:1,
    borderRadius: 20,
    padding: 28,
    paddingHorizontal:20,
    paddingTop:20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  header: {
    marginBottom: 20,
  },
  headerDetails:{
    flexDirection:"row",
    alignItems:"center",
    gap:8
  },
  title: {
    fontSize: 24,
    fontFamily: AppFonts.HelixaBold,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: AppFonts.HelixaRegular,
    marginTop: 2,
  },
  totalCaloriesText:{
    fontSize: 12,
    fontFamily: AppFonts.HelixaBoldItalic,
    marginTop: 2,
  },
  summaryIconWrapper:{
    elevation:2,
    left:6
  },
  summaryIconButton: {
    padding: 8,
    borderRadius:100,
    overflow:"hidden",
    elevation:2
  },
  summaryIconContainer:{
    padding:2,
    borderWidth:1.5,
    borderRadius:8,
  },
  summaryIcon: {
    borderRadius:4
  },
  icon: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 3,
  },
  iconLine: {
    width: 16,
    height: 2,
    backgroundColor: '#999',
    borderRadius: 1,
  },
  tabletDetailsSection: {
    alignItems: 'center',
    marginVertical: 28,
    marginTop:8
  },
  tabletNameText:{
    fontSize: 16,
    fontFamily: AppFonts.HelixaBold,
    marginVertical: 4,
    marginTop:8
  },
  tabletDetailsText:{
    fontSize: 12,
    fontFamily: AppFonts.HelixaBold,
    marginTop: 2,
    textAlign:"center",
    lineHeight:14
  },
  cartridgeContainer: {
    width: 41,
    height: 139,
    borderRadius: 8,
    borderWidth: 4,
    paddingBottom: 12,
  },
  tabletContainer: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    padding: 2,
    gap: 2,
  },
  tablet: {
    flex: 1,
    width: '100%',
  },
  iconText: {
    fontSize: 20,
  },
  nutrientsSection: {
    flex:1,
    padding:16,
    paddingVertical:8,
    borderRadius:16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth:2,
    borderColor:"#6B9E78"
  },
  nutrientsTitle: {
    fontSize: 20,
    fontFamily: AppFonts.HelixaBook,
    marginBottom: 12,
    textTransform:"uppercase",
    textAlign:'center'
  },
  nutrientsHeader: {
    flexDirection: 'row',
    paddingHorizontal:4
  },
  nutrientsHeaderText: {
    fontSize: 14,
    fontFamily: AppFonts.HelixaBold,
    paddingBottom:4
  },
  nutrientRow: {
    flexDirection: 'row',
    paddingBottom: 4,
    paddingHorizontal:4,
  },
  nutrientText: {
    fontSize: 12,
    fontFamily: AppFonts.HelixaRegular,
  },
  viewMoreButton: {
    width:'auto',
    paddingHorizontal: 32,
    alignSelf: 'center',
    marginTop: 8,
  },
  scrollDownBtn:{
    position:'absolute',
    justifyContent:'center',
    alignItems:'center',
    bottom:0,
    left:'50%',
    transform:[{translateX:'50%'}],
  },
  disabledScrollBtn: {
    opacity: 0.8,
  }
});

export default TabletAllDetailsScreen;
