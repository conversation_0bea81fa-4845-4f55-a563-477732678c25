import { Module } from '@nestjs/common';
import {
  CustomConfigService,
  CustomLogger,
  EmailService,
  EncryptionService,
  UserJourneyService,
  UtilsService,
} from './services';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import {
  UserJourneyPerDay,
  UserJourneyPerDaySchema,
} from 'models/user-journey/user-journey.schema';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserJourneyPerDay.name, schema: UserJourneyPerDaySchema },
    ]),
    ThirdPartyModule,
    ConfigModule,
    JwtModule.register({}),
  ],
  providers: [
    EmailService,
    CustomLogger,
    CustomConfigService,
    EncryptionService,
    UtilsService,
    UserJourneyService,
  ],
  exports: [
    EmailService,
    CustomLogger,
    CustomConfigService,
    EncryptionService,
    UtilsService,
    UserJourneyService,
  ],
})
export class CommonModule {}
