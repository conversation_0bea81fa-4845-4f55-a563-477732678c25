import {
  AdminOtpEmailTemplate,
  contactUsQueryConfirmationEmailTemplate,
  EmailVerificationTemplate,
  ForgotPasswordTemplate,
} from './email-templates';

export default {
  development: () => ({
    mongoDBConfig: {
      username: process.env.<PERSON>ONG<PERSON>_USERNAME,
      password: process.env.MONGO_PASSWORD,
      host: process.env.MONGO_HOST,
      port: parseInt(process.env.MONGO_PORT, 10),
      database: process.env.MONGO_DATABASE,
    },

    accountVerificationEmail: {
      subject: 'Verify Your Email to Complete Registration',
      emailBody:
        'Thank you for signing up with nestjs-starter! To complete your registration, please verify your email address by clicking the link below:',
      fromEmail: process.env.AWS_SES_EMAIL,
      EmailVerificationTemplate,
    },

    forgotPasswordEmail: {
      subject: 'Reset Your Password',
      emailBody:
        'You requested a password reset. Please click the link below to reset your password:',
      fromEmail: process.env.AWS_SES_EMAIL,
      ForgotPasswordTemplate,
    },

    contactUsQueryConfirmationEmail: {
      subject: "We've Received Your Inquiry",
      emailBody:
        'Thank you for reaching out! Our team has received your inquiry and will respond soon.',
      fromEmail: process.env.AWS_SES_EMAIL,
      contactUsQueryConfirmationEmailTemplate,
    },
    adminOtpEmail: {
      subject: 'Admin OTP for Secure Access',
      emailBody: 'Your OTP is $$otp. It will expire in 10 minutes.',
      fromEmail: process.env.AWS_SES_EMAIL,
      AdminOtpEmailTemplate,
    },

  }),

  production: () => ({
    mongoDBConfig: {
      username: process.env.MONGO_USERNAME,
      password: process.env.MONGO_PASSWORD,
      host: process.env.MONGO_HOST,
      port: parseInt(process.env.MONGO_PORT, 10),
      database: process.env.MONGO_DATABASE,
    },
  }),
};
