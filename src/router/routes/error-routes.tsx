import { Suspense, lazy } from 'react';
import { Outlet } from 'react-router-dom';

import SimpleLayout from '@/layouts/simple';

import AuthGuard from '../components/auth-guard';

import { AppRouteObject } from '#/router';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

const Page403 = lazy(() => import('@/pages/sys/error/Page403'));
const Page404 = lazy(() => import('@/pages/sys/error/Page404'));
const Page500 = lazy(() => import('@/pages/sys/error/Page500'));

/**
 * error routes
 * 403, 404, 500
 */
export const ErrorRoutes: AppRouteObject = {
  element: (
    <AuthGuard>
      <SimpleLayout>
        <Suspense
          fallback={
            <div className="flex h-full items-center justify-center">
              <CustomLoader />
            </div>
          }
        >
          <Outlet />
        </Suspense>
      </SimpleLayout>
    </AuthGuard>
  ),
  children: [
    { path: '403', element: <Page403 /> },
    { path: '404', element: <Page404 /> },
    { path: '500', element: <Page500 /> },
  ],
};
