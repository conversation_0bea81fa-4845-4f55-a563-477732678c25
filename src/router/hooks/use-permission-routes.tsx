import { isEmpty } from 'ramda';
import React, { Suspense, lazy, useMemo } from 'react';
import { Navigate, Outlet } from 'react-router-dom';

import { Iconify } from '@/components/icon';
import { useUserPermission } from '@/store/userStore';
import ProTag from '@/theme/antd/components/tag';
import { flattenTrees } from '@/utils/tree';

import { Permission } from '#/entity';
import { BasicStatus, PermissionType } from '#/enum';
import { AppRouteObject } from '#/router';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

// const { VITE_BACKEND_BASE_URL: BASE_URL } = import.meta.env;

// Define the module type
type ModuleType = {
  default: React.ComponentType;
};

// Define the import.meta.glob return type
type GlobModule = () => Promise<ModuleType>;

// Use import.meta.glob with type assertion
const pages: Record<string, GlobModule> = import.meta.glob(
  ['/src/pages/**/*.tsx', '!**/components/**'],
  { eager: false },
) as Record<string, GlobModule>;

function resolveComponent(componentPath: string): GlobModule {
  const fullPath = `/src/pages${componentPath.startsWith('/') ? componentPath : `/${componentPath}`}`;

  const moduleLoader = pages[fullPath];
  if (!moduleLoader) {
    console.error(`No matching component found for path: ${fullPath}`);
    return () => Promise.reject(new Error(`Component not found: ${fullPath}`));
  }

  return moduleLoader;
}

function transformPermissionToMenuRoutes(
  permissions: Permission[],
  flattenedPermissions: Permission[],
): AppRouteObject[] {
  return permissions.map((permission) => {
    const {
      route,
      type,
      label,
      icon,
      order,
      hide,
      hideTab,
      status,
      frameSrc,
      newFeature,
      component,
      parentId,
      children = [],
    } = permission;

    const appRoute: AppRouteObject = {
      path: route,
      meta: {
        label,
        key: route,
        hideMenu: !!hide,
        hideTab,
        disabled: status === BasicStatus.DISABLE,
      },
    };

    if (order) appRoute.order = order;
    if (icon) appRoute.meta!.icon = icon;
    if (frameSrc) appRoute.meta!.frameSrc = frameSrc;

    if (newFeature) {
      appRoute.meta!.suffix = (
        <ProTag color="cyan" icon={<Iconify icon="solar:bell-bing-bold-duotone" size={14} />}>
          NEW
        </ProTag>
      );
    }

    if (type === PermissionType.CATALOGUE) {
      appRoute.meta!.hideTab = true;
      if (!parentId) {
        appRoute.element = (
          <Suspense
            fallback={
              <div className="flex h-full items-center justify-center">
                <CustomLoader />
              </div>
            }
          >
            <Outlet />
          </Suspense>
        );
      }
      appRoute.children = transformPermissionToMenuRoutes(children, flattenedPermissions);

      if (!isEmpty(children)) {
        appRoute.children.unshift({
          index: true,
          element: <Navigate to={children[0].route} replace />,
        });
      }
    } else if (type === PermissionType.MENU && component) {
      const Element = lazy(() =>
        resolveComponent(component)().then((module: ModuleType) => ({
          default: module.default,
        })),
      );

      appRoute.element = frameSrc ? (
        <Element />
      ) : (
        <Suspense
          fallback={
            <div className="flex h-full items-center justify-center">
              <CustomLoader />
            </div>
          }
        >
          <Element />
        </Suspense>
      );
    }

    return appRoute;
  });
}

export function usePermissionRoutes() {
  // return []; // for testing only

  const permissions = useUserPermission();

  return useMemo(() => {
    if (!permissions) return [];

    const flattenedPermissions = flattenTrees(permissions);
    const permissionRoutes = transformPermissionToMenuRoutes(permissions, flattenedPermissions);
    return permissionRoutes;
  }, [permissions]);
}

// function getCompleteRoute(permission: Permission, flattenedPermissions: Permission[], route = '') {
//   const currentRoute = route ? `/${permission.route}${route}` : `/${permission.route}`;

//   if (permission.parentId) {
//     const parentPermission = flattenedPermissions.find((p) => p.id === permission.parentId)!;
//     return getCompleteRoute(parentPermission, flattenedPermissions, currentRoute);
//   }

//   return currentRoute;
// }
