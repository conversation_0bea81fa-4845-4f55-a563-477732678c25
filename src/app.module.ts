import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { MulterModule } from '@nestjs/platform-express';
import { ServeStaticModule } from '@nestjs/serve-static';
import { User, UserSchema } from 'models/user';
import { CartridgeModal, CartridgeModalSchema } from 'models/cartridge/cartridgeModal.schema';
import { Nutrient, NutrientSchema } from 'models/cartridge/nutrient.schema';
import { join } from 'path';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user/user.module';
import { ThirdPartyModule } from './third-party/third-party.module';
import { CommonModule } from './common/common.module';
import { RedisModule } from '@nestjs-modules/ioredis';
import { RepoModule } from './repo/repo.module';
import { BootstrapService } from './bootstrap.service';
import { NetworkService } from './utils/services/network.service';
import { AppController } from './app.controller';
import { APIUrlLoggerMiddleware } from './middlewares';
import { DatabaseConfigService } from 'config';
import { ThrottlerModule } from '@nestjs/throttler';
import { BootstrapCartidgeModalService } from './bootstrap-cartridgeModals.service';
import { CartridgeModule } from './user/cartridge/cartridge.module';
import { AdminModule } from './admin/admin.module';
import { DispenseModule } from './user/dispense/dispense.module';
import { ContactUsModule } from './user/contact_us/contact_us.module';
import { HelpModule } from './user/help/help.module';
import { FaqModule } from './user/faq/faq.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useClass: DatabaseConfigService,
      inject: [ConfigService],
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '../../public'),
    }),
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60,
          limit: 10,
        },
      ],
    }),
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Nutrient.name, schema: NutrientSchema },
      { name: CartridgeModal.name, schema: CartridgeModalSchema }
    ]),
    MulterModule.register(),

    RedisModule, // redis connection will be called from here
    ThirdPartyModule,
    RepoModule,
    CommonModule,

    AuthModule,
    UserModule,
    CartridgeModule,
    DispenseModule,
    ContactUsModule,
    HelpModule,
    FaqModule,
    AdminModule,
  ],
  providers: [BootstrapService, NetworkService,BootstrapCartidgeModalService],
  controllers: [AppController],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(APIUrlLoggerMiddleware).forRoutes('*');
  }
}
