import { Tabs, TabsProps } from 'antd';
import React, { useState } from 'react';

import { Iconify } from '@/components/icon';
import { AppTitle } from '@/pages/components/constants';

import DeviceDetail from './device-detail';
import EditDevice from './device-edit';
import DeviceList from './device-list';

import { DeviceData } from '#/device';

const Device: React.FC = () => {
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string[]>(['1']); // Active tab state
  const [isSingleDeviceLoading, setIsSingleDeviceLoading] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<DeviceData | null>(null);

  const handleDeviceClick = (Device: DeviceData) => {
    if (Device === null) {
      setIsSingleDeviceLoading(false);
      setActiveTab(['1']);
    } else {
      setIsEditing(false);
      setSelectedDeviceId(Device.id);
      setActiveTab(['2']);
    }
  };

  const handleBackToList = () => {
    setActiveTab(['1']);
    setSelectedDeviceId(null);
  };

  const handleDeviceEdit = (Device: DeviceData | null) => {
    if (Device === null) {
      setSelectedDeviceId(null);
      setActiveTab(['1']);
      return;
    }

    setIsEditing(true);
    setSelectedDevice(Device);
    setActiveTab(['3']);
  };

  const handleDeviceDelete = () => {
    setActiveTab(['1']);
    setSelectedDeviceId(null);
  };

  const handleBackToDetail = () => {
    setIsEditing(false);
    setActiveTab(['2']);
    setSelectedDevice(null);
  };

  const handleSaveDevice = () => {
    setIsEditing(false);
    setSelectedDeviceId(selectedDevice?.id || '');
    setSelectedDevice(null);
    setActiveTab(['2']);
  };

  const renderDeviceDetailTab =
    selectedDeviceId && !isEditing
      ? [
          {
            key: '2',
            label: (
              <div className="flex items-center">
                <Iconify icon="solar:document-bold" size={24} className="mr-2" />
                <span>Device Detail</span>
              </div>
            ),
            children: (
              <DeviceDetail
                id={selectedDeviceId}
                onBack={handleBackToList}
                activeTab={activeTab}
                onDelete={handleDeviceDelete}
                onEdit={handleDeviceEdit}
              />
            ),
          },
        ]
      : [];

  const renderDeviceEditTab =
    isEditing && selectedDevice
      ? [
          {
            key: '3',
            label: (
              <div className="flex items-center">
                <Iconify icon="solar:user-circle-bold" size={24} className="mr-2" />
                <span>Device Edit</span>
              </div>
            ),
            children: (
              <EditDevice
                Device={selectedDevice}
                onBack={handleBackToDetail}
                onSave={handleSaveDevice}
              />
            ),
          },
        ]
      : [];

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <div className="flex items-center">
          <Iconify icon="solar:list-bold" size={24} className="mr-2" />
          <span>Device List</span>
        </div>
      ),
      children: <DeviceList onDeviceClick={handleDeviceClick} activeTab={activeTab} />,
    },
    ...(isSingleDeviceLoading ? [] : []),
    ...renderDeviceDetailTab,
    ...renderDeviceEditTab,
  ];

  return (
    <div className="p-2">
      <AppTitle subTitle="Device" />

      <Tabs activeKey={activeTab[0]} onChange={(key) => setActiveTab([key])} items={items} />
    </div>
  );
};

export default Device;
