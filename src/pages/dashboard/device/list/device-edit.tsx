import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Form, Input, Card, Select, message, Spin } from 'antd';
import React, { useRef, useState } from 'react';
import { BsUpload } from 'react-icons/bs';
import { RxCross1 } from 'react-icons/rx';

import { useUpdateDevice } from '@/api/ApiHooks/device-hooks';

import { DeviceData } from '#/device';

interface EditDeviceProps {
  Device: DeviceData;
  onBack: () => void;
  onSave: (updatedDevice: DeviceData) => void;
}

const EditDevice: React.FC<EditDeviceProps> = ({ Device, onBack, onSave }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [form] = Form.useForm();
  const { UpdateDevice } = useUpdateDevice();
  const [isLoading, setIsLoading] = useState(false);
  const [thumbnailFile, setThumbnailFile] = useState<null | File>(null);
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(Device.thumbnailUrl);

  const RemoveThumbnail = () => {
    form.setFieldsValue({ thumbnailUrl: null });
    setThumbnailFile(null);
    setThumbnailUrl(null);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setThumbnailFile(file);
      form.setFieldsValue({ thumbnailUrl: URL.createObjectURL(file) });
    }
  };

  const handleFinish = async (values: any) => {
    if (!thumbnailFile && !values.thumbnailUrl) return message.error('Please provide a thumbnail.');

    setIsLoading(true);
    const formData = new FormData();

    formData.append('description', values.description);
    formData.append('deviceGuide', values.deviceGuide);
    formData.append('serialIds', values.serialIds.join(','));

    if (thumbnailFile) {
      formData.append('thumbnailFile', thumbnailFile);
    }

    const resp = await UpdateDevice(Device?.id || '', formData);

    if (resp === true) {
      message.success(`Device updated successfully.`);
      onSave({
        ...Device,
        ...values,
        thumbnailUrl: values.thumbnailUrl || Device.thumbnailUrl,
      });
    } else {
      message.error(`Failed to update the Device. Please try again.`);
    }

    setIsLoading(false);
  };

  return (
    <Card
      className="mt-4 p-6"
      style={{ boxShadow: '0 4px 8px rgba(0,0,0,0.1)', borderRadius: '8px' }}
    >
      <div className="mb-8 flex w-full items-center justify-between">
        <Button type="default" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back
        </Button>
      </div>

      <Form
        form={form}
        onFinish={handleFinish}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        labelAlign="left"
        initialValues={Device}
      >
        <Form.Item
          name="description"
          label="Description"
          rules={[{ required: true, message: 'Please input the Device description!' }]}
        >
          <Input.TextArea disabled={isLoading} placeholder="Enter Device description" rows={4} />
        </Form.Item>

        <Form.Item
          name="deviceGuide"
          label="Device Guide"
          rules={[{ required: true, message: 'Please input the Device guide!' }]}
        >
          <Input.TextArea disabled={isLoading} placeholder="Enter Device guide" rows={4} />
        </Form.Item>

        <Form.Item
          name="serialIds"
          label="Serial IDs"
          rules={[{ required: true, message: 'Please enter at least one serial ID!' }]}
        >
          <Select mode="tags" placeholder="Enter serial IDs" disabled={isLoading} />
        </Form.Item>

        <Form.Item name="thumbnailUrl" label="Thumbnail" className="w-full">
          <div className="relative h-[30vh] w-full overflow-hidden rounded-md border border-dashed border-gray-600 bg-hover">
            {thumbnailFile || thumbnailUrl ? (
              <>
                <button
                  type="button"
                  className="absolute right-2 top-2 rounded-full bg-gray-800 p-1 text-white"
                  onClick={RemoveThumbnail}
                  disabled={isLoading}
                >
                  <RxCross1 />
                </button>
                <div className="h-full w-full">
                  <img
                    src={thumbnailFile ? URL.createObjectURL(thumbnailFile) : thumbnailUrl || ''}
                    alt="thumbnail"
                    className="h-full w-full object-contain"
                  />
                </div>
              </>
            ) : (
              <div
                className="flex h-full cursor-pointer flex-col items-center justify-center"
                onClick={() => fileInputRef.current?.click()}
              >
                <BsUpload className="mb-2 text-[2rem]" />
                <p>Upload Device Thumbnail</p>
              </div>
            )}
            <input
              type="file"
              ref={fileInputRef}
              hidden
              accept="image/*"
              onChange={handleFileChange}
              disabled={isLoading}
            />
          </div>
        </Form.Item>

        <div style={{ marginTop: '20px', textAlign: 'right' }}>
          <Button type="primary" htmlType="submit" disabled={isLoading}>
            {isLoading && <Spin />} {isLoading ? `Saving` : 'Save'} Changes
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditDevice;
