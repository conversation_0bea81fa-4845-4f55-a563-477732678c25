import { useQuery } from '@tanstack/react-query';
import { Table, Input, Pagination, Space } from 'antd';
import { AxiosError } from 'axios';
import React, { useEffect, useState } from 'react';

import { GetAllDeviceApi } from '@/api/services/device/deviceService';
import CustomCard from '@/components/custom/CustomCard';
import { ErrorView } from '@/components/error';
import ProTag from '@/theme/antd/components/tag';

import { DeviceData } from '#/device';
import { DEVICE_TYPES } from '#/enum';
import type { ColumnsType } from 'antd/es/table';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

const { Search } = Input;

interface DeviceListProps {
  onDeviceClick: (Device: DeviceData) => void;
  activeTab: string[];
}

const DeviceList: React.FC<DeviceListProps> = ({ onDeviceClick, activeTab }) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [page, setPage] = useState<number>(1);

  // Fetch data with pagination and search query
  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ['list-Device-s', page, searchTerm],
    queryFn: () => GetAllDeviceApi(page, searchTerm),
  });

  useEffect(() => {
    if (activeTab.some((item) => item === '1')) {
      refetch();
    }
  }, [activeTab]);

  const DEVICE_TYPES_COLORS: Record<DEVICE_TYPES, string> = {
    [DEVICE_TYPES.MASSAGE]: 'orange',
    // [DEVICE_TYPES.SCAN]: 'blue',
  };

  const columns: ColumnsType<DeviceData> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      filterSearch: true,
      sorter: (a, b) => a.name.localeCompare(b.name),
      width: 300,
      render: (text) => (
        <Space className="truncate font-medium capitalize">
          <span>{text?.length > 40 ? `${text.substring(0, 40)}...` : text || 'N/A'}</span>
        </Space>
      ),
    },

    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: 300,
      render: (text) => (
        <Space className="truncate font-medium capitalize">
          <span>{text?.length > 40 ? `${text.substring(0, 40)}...` : text || 'N/A'}</span>
        </Space>
      ),
    },

    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version',
      width: 300,
      render: (text) => <ProTag>{text}</ProTag>,
    },

    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 300,
      render: (text) => (
        <ProTag
          color={DEVICE_TYPES_COLORS[text as DEVICE_TYPES] || 'default'}
          className="capitalize"
        >
          {text}
        </ProTag>
      ),
    },

    {
      title: 'Created At',
      dataIndex: 'createdAt', // Assuming 'createdAt' is part of the data
      key: 'createdAt',
      width: 200,
      render: (createdAt) => (
        <span>{new Date(createdAt).toLocaleDateString()}</span> // Format the date as per your requirement
      ),
    },
  ];

  if (isError) {
    if (error instanceof AxiosError && error.response) {
      return (
        <ErrorView
          message={error.response.data.message || 'Something went wrong,Please try again later.'}
        />
      );
    } else {
      return <ErrorView message="Something went wrong,Please try again later." />;
    }
  }

  return (
    <CustomCard>
      {/* Search Input */}
      <div className="mb-4 flex flex-col space-y-2">
        <Search
          placeholder="Search By Name"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      {isLoading ? (
        <div className="flex h-full min-h-[60vh] items-center justify-center">
          <CustomLoader className="h-20 w-20" />
        </div>
      ) : (
        <div>
          <Table
            columns={columns}
            dataSource={data?.devices}
            pagination={false}
            size="middle"
            onRow={(record) => ({
              onClick: () => onDeviceClick(record),
            })}
            style={{ cursor: 'pointer' }}
            scroll={{ x: true }}
          />

          {/* Pagination Component */}
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>
            <Pagination
              current={page}
              total={data?.total || 0}
              pageSize={10}
              onChange={(newPage) => setPage(newPage)}
              showSizeChanger={false}
              className="pagination"
            />
          </div>
        </div>
      )}
    </CustomCard>
  );
};

export default DeviceList;
