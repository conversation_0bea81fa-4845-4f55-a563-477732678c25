import { ArrowLeftOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Modal, Button, Table } from 'antd';
import React, { useEffect } from 'react';

import { useDeleteDevice } from '@/api/ApiHooks/device-hooks';
import { GetSingleDeviceApi } from '@/api/services/device/deviceService';
import CustomCard from '@/components/custom/CustomCard';

import { DeviceData } from '#/device';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

export interface DeviceDetailProps {
  id: string | null;
  onBack: () => void;
  activeTab: string[];
  onDelete: () => void;
  onEdit: (Device: DeviceData | null) => void;
}

const DeviceDetail: React.FC<DeviceDetailProps> = ({
  id: DeviceId,
  onBack,
  activeTab,
  onDelete,
  onEdit,
}) => {
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['Device--detail', DeviceId],
    queryFn: () => GetSingleDeviceApi(DeviceId || ''),
  });

  const { deleteDevice } = useDeleteDevice();

  const confirmDelete = () => {
    Modal.confirm({
      title: 'Are you sure you want to delete this?',
      content: 'This action cannot be undone.',
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        await deleteDevice(data?.device.id || '', onDelete);
      },
    });
  };

  useEffect(() => {
    if (activeTab.includes('2')) {
      refetch();
    }
  }, [activeTab]);

  if (isLoading) {
    return (
      <div className="flex h-full min-h-[60vh] items-center justify-center">
        <CustomLoader className="h-20 w-20" />
      </div>
    );
  }

  if (isError) {
    return <p>Error loading Device: {error?.message}</p>;
  }

  const device = data?.device;

  const referenceColumns = [
    {
      title: 'Reference ID',
      dataIndex: 'id',
      key: 'id',
    },
  ];

  const referenceData = device?.serialIds?.map((id, index) => ({
    key: index,
    id,
  }));

  return (
    <CustomCard className="flex w-full flex-col items-start justify-between space-y-5">
      <div className="mb-4 flex w-full justify-between">
        <Button type="default" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back
        </Button>

        <div className="flex space-x-3">
          <Button type="primary" onClick={() => onEdit(device || null)} icon={<EditOutlined />}>
            Update
          </Button>

          <Button danger onClick={confirmDelete} icon={<DeleteOutlined />}>
            Delete
          </Button>
        </div>
      </div>

      <div className="w-full space-y-4">
        <div className="flex w-full items-center justify-center">
          <img
            src={device?.thumbnailUrl}
            alt="Device Thumbnail"
            className="max-h-[30vh] w-full object-contain"
          />
        </div>

        <p className="capitalize">
          <strong>Name:</strong> {device?.name}
        </p>
        <p className="capitalize">
          <strong>Description:</strong> {device?.description}
        </p>
        <p className="capitalize">
          <strong>Device Guide:</strong> {device?.deviceGuide}
        </p>
        <p>
          <strong>Version:</strong> {device?.version}
        </p>
        <p className="capitalize">
          <strong>Type:</strong> {device?.type}
        </p>
        <p>
          <strong>Created At:</strong>{' '}
          {device?.createdAt ? new Date(device.createdAt).toLocaleString() : 'N/A'}
        </p>

        <h3 className="text-lg font-bold">Reference IDs</h3>
        <Table columns={referenceColumns} dataSource={referenceData} pagination={false} />
      </div>
    </CustomCard>
  );
};

export default DeviceDetail;
