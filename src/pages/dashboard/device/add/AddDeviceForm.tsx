import { message, Input, Select, Spin, Tooltip } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React, { useRef, useState } from 'react';
import { BsUpload } from 'react-icons/bs';
import { RxCross1 } from 'react-icons/rx';

import { useCreateDevice } from '@/api/ApiHooks/device-hooks';
import { CreatDeviceReqObj } from '@/api/services/device/types';
import CustomCard from '@/components/custom/CustomCard';

import { DEVICE_NAMES, DEVICE_TYPES } from '#/enum';

const { Option } = Select;

const AddDeviceForm: React.FC = () => {
  const AddDeviceFunc = useCreateDevice();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [deviceData, setDeviceData] = useState<CreatDeviceReqObj>({
    name: '',
    description: '',
    deviceGuide: '',
    version: 0,
    thumbnailFile: null,
    serialIds: [],
    type: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setDeviceData((prevState) => ({ ...prevState, [name]: value }));
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setDeviceData((prevState) => ({ ...prevState, thumbnailFile: file }));
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleRemoveThumbnail = () => {
    setDeviceData((prevState) => ({ ...prevState, thumbnailFile: null }));
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSerialIdChange = (value: string[]) => {
    const uniqueValues = new Set(value);
    if (uniqueValues.size !== value.length) {
      message.error('Duplicate Serial ID is not allowed.');
      return;
    }
    setDeviceData((prev) => ({ ...prev, serialIds: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!deviceData.name.trim()) return message.error('Please provide device name.');
    if (!deviceData.description.trim()) return message.error('Please provide a description.');
    if (!deviceData.deviceGuide.trim()) return message.error('Please provide a device guide.');
    if (!deviceData.thumbnailFile) return message.error('Please provide a thumbnail.');

    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append('name', deviceData.name);
      formData.append('description', deviceData.description);
      formData.append('deviceGuide', deviceData.deviceGuide);
      formData.append('version', deviceData.version.toString());
      formData.append('serialIds', deviceData.serialIds.join(','));
      formData.append('thumbnailFile', deviceData.thumbnailFile);
      formData.append('type', deviceData.type);

      await AddDeviceFunc(formData);
      message.success('Device added successfully!');
    } catch (error) {
      message.error('Failed to add device. Please try again.');
      console.error('Device creation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex w-full items-start justify-center">
      <CustomCard className="w-[80%] rounded-xl border-2 border-gray-300 p-8 shadow-sm">
        <h1 className="mb-7 text-2xl font-semibold">Add Device</h1>
        <form onSubmit={handleSubmit} className="space-y-7">
          <div>
            <label className="font-semibold">Name: </label>
            <Select
              className="w-full capitalize"
              placeholder="Select Device Name"
              onChange={(value) => setDeviceData((prev) => ({ ...prev, name: value }))}
              disabled={isLoading}
            >
              {Object.values(DEVICE_NAMES).map((item) => (
                <Option className="capitalize" value={item}>
                  {item}
                </Option>
              ))}
            </Select>
          </div>

          <div>
            <label className="font-semibold">Type: </label>
            <Select
              className="w-full capitalize"
              placeholder="Select Device Name"
              onChange={(value) => setDeviceData((prev) => ({ ...prev, type: value }))}
              disabled={isLoading}
            >
              {Object.values(DEVICE_TYPES).map((item) => (
                <Option className="capitalize" value={item}>
                  {item}
                </Option>
              ))}
            </Select>
          </div>

          <div>
            <label className="font-semibold">Description: </label>
            <TextArea
              rows={4}
              name="description"
              value={deviceData.description}
              onChange={handleInputChange}
              placeholder="Enter description"
              disabled={isLoading}
            />
          </div>

          <div>
            <label className="font-semibold">Device Guide: </label>
            <TextArea
              rows={4}
              name="deviceGuide"
              value={deviceData.deviceGuide}
              onChange={handleInputChange}
              placeholder="Enter device guide"
              disabled={isLoading}
            />
          </div>

          <div>
            <label className="font-semibold">Version: </label>
            <Input
              name="version"
              type="number"
              value={deviceData.version}
              onChange={handleInputChange}
              placeholder="Enter version"
              disabled={isLoading}
            />
          </div>

          <div>
            <label className="font-semibold">Serial IDs: </label>
            <Select
              mode="tags"
              className="w-full"
              placeholder="Enter Serial IDs"
              onChange={handleSerialIdChange}
              disabled={isLoading}
            />
          </div>

          <div>
            <label className="font-semibold">Thumbnail: </label>
            <div
              className="mb-2 flex h-[20vh] w-full cursor-pointer items-center justify-center rounded-md border border-dashed border-gray-600 bg-hover"
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                type="file"
                ref={fileInputRef}
                hidden
                accept="image/*"
                disabled={isLoading}
                onChange={handleFileChange}
              />

              {previewUrl ? (
                <div className="relative flex h-full w-full items-center justify-center bg-hover">
                  <img src={previewUrl} alt="Thumbnail" className="h-full object-contain" />
                  <button
                    disabled={isLoading}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveThumbnail();
                    }}
                    className="absolute right-2 top-2 rounded-full bg-gray-800 p-1 text-white"
                  >
                    <RxCross1 />
                  </button>
                </div>
              ) : (
                <Tooltip title="Click to upload a thumbnail">
                  <div className="flex flex-col items-center justify-center">
                    <BsUpload className="mb-2 text-[2rem]" />
                    <p>Upload Thumbnail</p>
                  </div>
                </Tooltip>
              )}
            </div>
          </div>

          <button
            type="submit"
            className="bg-orange-500 w-full rounded bg-primary py-2 font-semibold text-white shadow-md duration-100 hover:shadow-md"
            disabled={isLoading}
          >
            {isLoading && <Spin />} {isLoading ? 'Adding' : 'Add'} Device
          </button>
        </form>
      </CustomCard>
    </div>
  );
};

export default AddDeviceForm;
