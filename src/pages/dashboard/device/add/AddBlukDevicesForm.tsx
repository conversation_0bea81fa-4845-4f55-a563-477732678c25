import { message, Spin, Tooltip } from 'antd';
import React, { useRef, useState } from 'react';
import { BsUpload } from 'react-icons/bs';
import { RxCross1 } from 'react-icons/rx';

import { useAddBulkDevices } from '@/api/ApiHooks/device-hooks';
import CsvFileIcon from '@/assets/images/csv_file_icon.png';
import CustomCard from '@/components/custom/CustomCard';

import { DEVICE_TYPES } from '#/enum';

const AddBulkDevicesForm: React.FC = () => {
  const AddBulkDevices = useAddBulkDevices();

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.name.endsWith('.csv')) {
        message.error('Please upload a valid CSV file.');
        return;
      }
      setCsvFile(file);
    }
  };

  const handleRemoveFile = () => {
    setCsvFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = ''; // Reset input field
    }
  };

  const HandleSubmit = async () => {
    if (csvFile === null) return message.error('Please provide Devices csv file.');

    const csvFormData = new FormData();
    csvFormData.append('csvFile', csvFile);
    try {
      setIsLoading(true);
      await AddBulkDevices(csvFormData);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex w-full items-start justify-center">
      <CustomCard className="w-[80%] space-y-7 rounded-xl border-2 border-gray-300 p-8 shadow-sm">
        <h1 className="mb-7 text-2xl font-semibold">Add Bulk Devices via CSV</h1>

        {/* CSV Format Instructions */}
        <div className="mb-4 w-full rounded-md border border-gray-300 bg-primary_2 p-4">
          <h1 className="text-[1.2rem] font-semibold text-primary">CSV Upload Instructions:</h1>
          <ul className="list-disc pl-6 text-sm text-gray-700">
            <li>
              The first row must contain column headers exactly as shown in the sample{' '}
              <strong>format table</strong>.
            </li>
            <li>
              All data must follow the expected data types (e.g.,{' '}
              <strong>string for description and deviceGuide, number for version</strong>).
            </li>
            <li>
              The <strong>serialIds</strong> column must contain a <strong>comma-separated</strong>{' '}
              list of serial IDs.
            </li>
            <li>
              The <strong>type</strong> must contain one of the following values:{' '}
              <strong>{Object.values(DEVICE_TYPES).join(', ')}</strong>.
            </li>
            <li>
              The <strong>thumbnailUrl</strong> must contain a valid image URL.
            </li>
            <li>
              <strong>Only rows with valid data will be added</strong> to the database.
            </li>
            <li>
              <strong>Rows with incorrect data will be excluded</strong>, and a report will be sent
              to the admin's email.
            </li>
            <li>
              If a device with the <strong>same name and version</strong> already exists, it will be
              updated instead of being duplicated.
            </li>
          </ul>
        </div>

        {/* CSV Format Table */}
        <div className="w-full">
          <h2 className="text-md mt-6 font-semibold">CSV Format:</h2>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-400">
              <thead>
                <tr className="bg-primary">
                  <th className="border border-gray-400 p-2">name</th>
                  <th className="border border-gray-400 p-2">description</th>
                  <th className="border border-gray-400 p-2">deviceGuide</th>
                  <th className="border border-gray-400 p-2">version</th>
                  <th className="border border-gray-400 p-2">type</th>
                  <th className="border border-gray-400 p-2">thumbnailUrl</th>
                  <th className="border border-gray-400 p-2">serialIds</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border border-gray-400 p-2">mark</td>
                  <td className="border border-gray-400 p-2">A high-quality massage device.</td>
                  <td className="border border-gray-400 p-2">User manual v1.0</td>
                  <td className="border border-gray-400 p-2">1.2</td>
                  <td className="border border-gray-400 p-2">massage</td>
                  <td className="border border-gray-400 p-2">https://example.com/image1.jpg</td>
                  <td className="border border-gray-400 p-2">a1b2c3d4,e5f6g7h8</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* CSV Upload Section */}
        <div>
          <label className="font-semibold">Add CSV: </label>
          <div
            className="mb-2 flex h-[20vh] w-full cursor-pointer items-center justify-center rounded-md border border-dashed border-gray-600 bg-hover"
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              type="file"
              ref={fileInputRef}
              hidden
              accept=".csv"
              disabled={csvFile !== null}
              onChange={handleFileChange}
            />
            {csvFile ? (
              <div className="relative flex h-full w-full flex-col items-center justify-center">
                <img src={CsvFileIcon} alt="CSV File" className="w-2h-24 h-24" />
                <p className="mt-2 text-sm">{csvFile.name}</p>
                <button
                  disabled={isLoading}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveFile();
                  }}
                  className="absolute right-2 top-2 rounded-full bg-gray-800 p-1 text-white"
                >
                  <RxCross1 />
                </button>
              </div>
            ) : (
              <Tooltip title="Click to upload a CSV file">
                <div className="flex h-full w-full flex-col items-center justify-center">
                  <BsUpload className="mb-2 text-[2rem]" />
                  <p>Upload CSV File</p>
                </div>
              </Tooltip>
            )}
          </div>
        </div>

        <button
          type="submit"
          className="bg-orange-500 w-full rounded bg-primary py-2 font-semibold text-white shadow-md duration-100 hover:shadow-md"
          onClick={HandleSubmit}
          disabled={isLoading}
        >
          {isLoading && <Spin />} Add Bulk Devices
        </button>
      </CustomCard>
    </div>
  );
};

export default AddBulkDevicesForm;
