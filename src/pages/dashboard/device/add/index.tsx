import { Radio } from 'antd';
import React, { useState } from 'react';

import { AppTitle } from '@/pages/components/constants';

import AddBlukDevicesForm from './AddBlukDevicesForm';
import AddDeviceForm from './AddDeviceForm';

const AddQuest: React.FC = () => {
  const [isBulkAdd, setIsBulkAdd] = useState(false);

  return (
    <div className="w-full space-y-5">
      {/* Radio Button for Switching Forms */}
      <div className="">
        <Radio.Group
          onChange={(e) => setIsBulkAdd(e.target.value)}
          value={isBulkAdd}
          className="mb-4"
        >
          <Radio value={false}>Add Single Device</Radio>
          <Radio value={true}>Add Bulk Devices</Radio>
        </Radio.Group>
      </div>

      {/* Conditional Form Rendering */}
      {isBulkAdd ? (
        <>
          <AppTitle subTitle="Add Bulk Devices" />
          <AddBlukDevicesForm />
        </>
      ) : (
        <>
          <AppTitle subTitle="Add Single Device" />
          <AddDeviceForm />
        </>
      )}
    </div>
  );
};

export default AddQuest;
