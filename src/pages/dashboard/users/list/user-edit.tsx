// import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
// import { Button, Select, Switch, Card, Form } from 'antd';
// import React, { useState } from 'react';

// import { useUpdateUser } from '@/api/ApiHooks/user-hooks';
// import { updateUserReqObj } from '@/api/services/user/types';

// import { UserEditProps } from '#/user';

// export const roles = [
//   {
//     id: '1',
//     name: 'User',
//     value: 'user',
//     description: 'Regular user with standard access rights',
//   },
//   {
//     id: '2',
//     name: 'HR',
//     value: 'hr',
//     description: 'HR with access to content HR tools',
//   },
// ];

// const UserEdit: React.FC<UserEditProps> = ({ user, onBack, onSave }) => {
//   const [isActive, setIsActive] = useState(user.isActive);
//   const [rolesSelected, setRolesSelected] = useState<string[]>(
//     user.roles?.map((role) => role.name) || [],
//   );
//   const [loading, setLoading] = useState(false);

//   const updateUser = useUpdateUser();

//   const handleStatusChange = (checked: boolean) => {
//     setIsActive(checked);
//   };

//   const handleRoleChange = (value: string[]) => {
//     // Ensure "user" role is not removed and prevent duplicates
//     const uniqueRoles = Array.from(new Set([...value, 'User']));
//     setRolesSelected(uniqueRoles);
//   };

//   const handleSave = async () => {
//     const updateData: updateUserReqObj = {
//       isActive,
//       assignHrRole: rolesSelected.some((item) => item === 'HR'),
//     };

//     try {
//       setLoading(true);

//       await updateUser(updateData, user.id, onSave);

//       // message.success('User updated successfully!');
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <Card className="bg-white mx-auto max-w-4xl rounded-lg p-4 shadow">
//       <div className="mb-4 flex items-center justify-between">
//         <Button onClick={onBack} style={{ marginRight: '8px' }} icon={<ArrowLeftOutlined />}>
//           Back
//         </Button>
//       </div>

//       <Card>
//         <h2 className="mb-4 text-2xl font-semibold">
//           User Name - {`${user.firstName} ${user.lastName}`}
//         </h2>
//         <Form layout="vertical" onFinish={handleSave}>
//           <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
//             {/* Status Toggle */}
//             <div className="flex items-center gap-4">
//               <h3 className="mr-1 text-lg font-medium">Account Status: </h3>
//               <Switch
//                 checked={isActive}
//                 onChange={handleStatusChange}
//                 checkedChildren="Active"
//                 unCheckedChildren="Inactive"
//                 style={{
//                   backgroundColor: isActive ? 'blue' : 'red', // Changes to blue when active
//                 }}
//               />
//             </div>

//             {/* Roles Selector */}
//             <div className="flex flex-col gap-2">
//               <h3 className="text-lg font-medium">User Roles</h3>
//               <Select
//                 mode="multiple"
//                 value={rolesSelected}
//                 onChange={handleRoleChange}
//                 options={roles.map((role) => ({
//                   value: role.name,
//                   label: role.name,
//                   disabled: role.name === 'User', // Disable "User" role for removal
//                 }))}
//                 className="w-full"
//                 placeholder="Select roles"
//               />
//             </div>
//           </div>

//           {/* Save Button */}
//           <div className="mt-6 flex justify-end">
//             <Button
//               htmlType="submit"
//               type="primary"
//               icon={<SaveOutlined />}
//               loading={loading}
//               className="flex items-center"
//             >
//               Save Changes
//             </Button>
//           </div>
//         </Form>
//       </Card>
//     </Card>
//   );
// };

// export default UserEdit;
