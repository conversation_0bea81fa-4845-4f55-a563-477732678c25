import { FilterOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Pagination, Input, Select, Button } from 'antd';
import Table, { ColumnsType } from 'antd/es/table';
import { AxiosError } from 'axios';
import React, { useEffect, useState } from 'react';

import { GetAllUsersApi } from '@/api/services/user/userService';
import CustomCard from '@/components/custom/CustomCard';
import { ErrorView } from '@/components/error';
import ProTag from '@/theme/antd/components/tag';
import { useThemeToken } from '@/theme/hooks';

import { UserData } from '#/user';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

const { Option } = Select;
const { Search } = Input;

interface UserListProps {
  onUserSelect: (user: UserData) => void;
  activeTab: string[];
  setVerified: (value: boolean) => void;
  setStep: (value: 'send' | 'verify' | 'done') => void;
}

const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const UserList: React.FC<UserListProps> = ({ onUserSelect, activeTab, setVerified, setStep }) => {
  const { colorTextSecondary } = useThemeToken();
  const [IsDeleted, setIsDeleted] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>(''); // User input
  const [validSearchTerm, setValidSearchTerm] = useState<string>(''); // Only valid search term triggers API
  const [isSearching, setIsSearching] = useState<boolean>(false); // Track if search is active
  const [page, setPage] = useState<number>(1); // Page number

  // Fetch data only when validSearchTerm updates (after button click)
  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ['list-users', page, validSearchTerm, IsDeleted],
    queryFn: () => GetAllUsersApi(page, validSearchTerm, IsDeleted),
  });

  useEffect(() => {
    if (activeTab.some((item) => item === '1')) {
      refetch();
    }
  }, [activeTab]);

  useEffect(() => {
    if (error && error instanceof AxiosError && error.response?.data?.isInvalidOTP) {
      localStorage.removeItem('otpAccessToken');
      setVerified(false);
      setStep('send');
    }
  }, [error]);

  // Search button click handler
  const handleSearch = () => {
    if (isValidEmail(searchTerm)) {
      setValidSearchTerm(searchTerm);
      setIsSearching(true); // Disable input field
    }
  };

  // Remove button click handler
  const handleRemove = () => {
    setSearchTerm('');
    setValidSearchTerm('');
    setIsSearching(false); // Enable input field
    setPage(1); // Reset pagination
  };

  const columns: ColumnsType<UserData> = [
    {
      title: 'Name',
      dataIndex: 'name',
      width: 100,
      render: (_, record) => (
        <p style={{ color: colorTextSecondary }} className="text-[0.9rem] font-semibold">
          {record.firstName.charAt(0).toUpperCase() + record.firstName.slice(1) + ' ' + record.lastName}
        </p>
      ),
    },
    {
      title: 'Email',
      dataIndex: 'email',
      width: 200,
      render: (_, record) => (
        <p style={{ color: colorTextSecondary }} className="text-[0.8rem]">
          {record.email.toLowerCase()}
        </p>
      ),
    },
    {
      title: 'Profile Completed',
      dataIndex: 'isAccountCompleted',
      align: 'center',
      width: 120,
      render: (isAccountCompleted: boolean) => (
        <ProTag color={isAccountCompleted ? 'success' : 'error'}>
          {isAccountCompleted ? 'True' : 'False'}
        </ProTag>
      ),
    },
    {
      title: 'Account Deleted',
      dataIndex: 'isDeleted',
      align: 'center',
      width: 150,
      render: (isDeleted: boolean) => (
        <ProTag color={!isDeleted ? 'success' : 'error'}>{isDeleted ? 'True' : 'False'}</ProTag>
      ),
    },
  ];

  if (isError) {
    if (error instanceof AxiosError && error.response) {
      return (
        <ErrorView
          message={error.response.data.message || 'Something went wrong, Please try again later.'}
        />
      );
    } else {
      return <ErrorView message="Something went wrong, Please try again later." />;
    }
  }

  return (
    <CustomCard>
      <div className="mb-2 flex w-full items-center justify-end space-x-3">
        <FilterOutlined className="h-10 text-lg text-gray-600" />
        <Select
          style={{ width: 140 }}
          id="filterDropdown"
          value={IsDeleted}
          onChange={(e) => setIsDeleted(e)}
          className="w-full"
        >
          <Option value="">View All Users</Option>
          <Option value="true">Deleted Users</Option>
          <Option value="false">Active Users</Option>
        </Select>
      </div>

      <div className="mb-2 flex space-x-2">
        <Search
          placeholder="Search User By Email"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          disabled={isSearching} // Disable input during search
        />
        <Button type="primary" onClick={handleSearch} disabled={!isValidEmail(searchTerm)}>
          Search
        </Button>
        <Button danger onClick={handleRemove} disabled={!isSearching}>
          Remove
        </Button>
      </div>

      {isLoading ? (
        <div className="flex h-full min-h-[60vh] items-center justify-center">
          <CustomLoader className="h-20 w-20" />
        </div>
      ) : (
        <>
          <Table
            onRow={(record) => ({
              onClick: () => onUserSelect(record),
            })}
            style={{ cursor: 'pointer' }}
            rowKey="id"
            size="middle"
            scroll={{ x: 'max-content' }}
            pagination={false}
            columns={columns}
            dataSource={data?.users}
          />

          {/* Pagination Component */}
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>
            <Pagination
              current={page}
              total={data?.total || 0}
              pageSize={10}
              onChange={(newPage) => setPage(newPage)}
              showSizeChanger={false}
              className="pagination"
            />
          </div>
        </>
      )}
    </CustomCard>
  );
};

export default UserList;
