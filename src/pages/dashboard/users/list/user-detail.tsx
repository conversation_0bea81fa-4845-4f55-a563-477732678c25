import { ArrowLeftOutlined, DeleteOutlined, EditOutlined, UserOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Card, Button, Modal, Avatar, Tag, Divider } from 'antd';
import { AxiosError } from 'axios';
import React, { useEffect } from 'react';

import { useDeleteUser } from '@/api/ApiHooks/user-hooks';
import { GetSingleUserApi } from '@/api/services/user/userService';
import { ErrorView } from '@/components/error';

import { UserData } from '#/user';
import CustomLoader from '@/pages/components/loaders/CustomLoader';
import { convertDOBtoAge } from '@/utils/convertDOBtoAge';

interface UserDetailProps {
  userId: number;
  onBack: () => void;
  onDelete: () => void;
  onEdit: (User: UserData) => void;
  activeTab: string[];
  setVerified: (value: boolean) => void;
  setStep: (value: 'send' | 'verify' | 'done') => void;
}

function capitalizeFirstLetter(str?: string) {
  if (!str) return 'N/A';
  return str.charAt(0).toUpperCase() + str.slice(1);
}

const InfoItem: React.FC<{ label: string; value: React.ReactNode }> = ({ label, value }) => (
  <div className="mb-3">
    <strong>{label}: </strong>
    <span>{value}</span>
  </div>
);

const UserDetail: React.FC<UserDetailProps> = ({
  userId,
  onBack,
  onEdit,
  onDelete,
  activeTab,
  setVerified,
  setStep,
}) => {
  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ['user-profile-view', userId],
    queryFn: () => GetSingleUserApi(userId.toString()),
  });

  const deleteUser = useDeleteUser();

  useEffect(() => {
    if (activeTab.some((item) => item === '2')) {
      refetch();
    }
  }, [activeTab]);

  useEffect(() => {
    if (error && error instanceof AxiosError && error.response?.data?.isInvalidOTP) {
      localStorage.removeItem('otpAccessToken');
      setVerified(false);
      onBack();
      setStep('send');
    }
  }, [error]);

  const handleUserDelete = (userId: string) => {
    Modal.confirm({
      title: 'Are you sure you want to delete this user?',
      content: 'This action cannot be undone.',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      centered: true,
      onOk: async () => {
        await deleteUser(userId, onDelete, () => {
          localStorage.removeItem('otpAccessToken');
          onBack();
          setStep('send');
          setVerified(false);
        });
      },
    });
  };

  if (isLoading) {
    return (
      <div className="flex h-full min-h-[60vh] items-center justify-center">
        <CustomLoader className="h-20 w-20" />
      </div>
    );
  }

  if (isError) {
    if (error instanceof AxiosError && error.response) {
      return (
        <ErrorView
          message={error.response.data.message || 'Something went wrong, Please try again later.'}
        />
      );
    } else {
      return <ErrorView message="Something went wrong, Please try again later." />;
    }
  }

  const fullName = `${capitalizeFirstLetter(data.user.firstName)} ${capitalizeFirstLetter(data.user.lastName)}`;
  const profilePicUrl = data.user.profilePic || data.user.avatar;

  return (
    <Card>
      <div className="mb-4 flex items-center justify-between">
        <Button type="text" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back
        </Button>

        <div>
          <Button
            disabled={data?.user?.isDeleted}
            danger
            icon={<DeleteOutlined />}
            className="mr-3"
            onClick={() => handleUserDelete(data?.user?.id)}
          >
            Delete User
          </Button>
          <Button
            type="primary"
            disabled={true}
            onClick={() => onEdit(data.user)}
            icon={<EditOutlined />}
          >
            Edit User
          </Button>
        </div>
      </div>

      {/* Profile Header Section */}
      <div className="mb-6 flex flex-col sm:flex-row items-center sm:items-start gap-4 p-4 rounded-lg border border-gray-300 bg-gray-50">
        <Avatar
          size={100}
          src={profilePicUrl}
          icon={!profilePicUrl && <UserOutlined />}
          className="flex-shrink-0"
        />
        
        <div className="flex-1 text-center sm:text-left">
          <h2 className="text-2xl font-semibold mb-1">{fullName}</h2>
          <p className="text-gray-600 mb-2">{data.user.email.toLowerCase()}</p>
          
          <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
            <Tag color={data.user.isAccountCompleted ? 'green' : 'orange'}>
              {data.user.isAccountCompleted ? 'Completed' : 'Incomplete'}
            </Tag>
            {data.user.isDeleted && <Tag color="red">Deleted</Tag>}
          </div>
        </div>
      </div>

      {/* Information Grid */}
      <div className="grid w-full sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 rounded-lg border border-gray-300 p-5 shadow-md">
        {/* Basic Details */}
        <div className="h-full p-2">
          <h3 className="font-semibold mb-3 text-base">Basic Details</h3>
          
          <InfoItem label="Name" value={fullName} />
          <InfoItem label="Email" value={data.user.email.toLowerCase() || 'N/A'} />
          <InfoItem label="Account Completed" value={data.user.isAccountCompleted ? 'True' : 'False'} />
          <InfoItem label="Account Deleted" value={data.user.isDeleted ? 'True' : 'False'} />
        </div>

        {/* Personal Details */}
        <div className="h-full p-2 sm:border-0 lg:border-x">
          <h3 className="font-semibold mb-3 text-base">Personal Details</h3>
          
          <InfoItem label="Gender" value={capitalizeFirstLetter(data.user.gender)} />
          <InfoItem 
            label="Age" 
            value={data.user.dob ? `${convertDOBtoAge(data.user.dob)} years old` : 'N/A'} 
          />
          <InfoItem 
            label="Height" 
            value={data.user.height ? `${data.user.height} cm` : 'N/A'} 
          />
          <InfoItem 
            label="Weight" 
            value={data.user.weight ? `${data.user.weight} kgs` : 'N/A'} 
          />
          <InfoItem 
            label="Activity Level" 
            value={data.user.activityLevel || 'N/A'} 
          />
        </div>

        {/* Location Details */}
        <div className="h-full p-2">
          <h3 className="font-semibold mb-3 text-base">Location</h3>
          
          <InfoItem label="City" value={data.user.city || 'N/A'} />
          <InfoItem label="State" value={data.user.state || 'N/A'} />
          <InfoItem label="Country" value={data.user.country || 'N/A'} />
        </div>
      </div>
    </Card>
  );
};

export default UserDetail;