/* global localStorage, setTimeout */
import React, { useState, useEffect } from 'react';
import { Card, Button, Input, message, Typography } from 'antd';
import { useVerifyOtp } from '@/api/ApiHooks/otp-hooks';
import { useUserToken } from '@/store/userStore';
import { sendOtpApi } from '@/api/services/otp/otpService';

const { Title, Text } = Typography;

// Helper to decode JWT and extract email
function getEmailFromToken(): string | null {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    return user?.email || null;
  } catch {
    return null;
  }
}

interface OtpGateProps {
  verified: boolean;
  setVerified: (value: boolean) => void;
  step: 'send' | 'verify' | 'done';
  setStep: (value: 'send' | 'verify' | 'done') => void;
  children: React.ReactNode;
}

const OtpGate: React.FC<OtpGateProps> = ({ verified, setVerified, step, setStep, children }) => {
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState<string | null>(null);
  const { accessToken } = useUserToken();
  const { verify, isPending } = useVerifyOtp();

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setEmail(getEmailFromToken());
      // Check for OTP access token
      const otpAccessToken = localStorage.getItem('otpAccessToken');
      if (otpAccessToken) {
        setVerified(true);
      }
    }
  }, []);

  // Mock API call to send OTP
  const sendOtp = async () => {
    if (!accessToken) {
      message.error('No access token found.');
      return;
    }
    setLoading(true);
    try {
      await sendOtpApi();
      message.success(`OTP sent to ${email}`);
      setStep('verify');
    } catch (err) {
      message.error(err?.response?.data?.message || 'Failed to send OTP');
    } finally {
      setLoading(false);
    }
  };

  const handleVerify = () => {
    if (!accessToken) {
      message.error('No access token found.');
      return;
    }
    verify(otp, (res) => {
      // Save the OTP access token to localStorage
      if (res && res.accessToken) {
        localStorage.setItem('otpAccessToken', res.accessToken);
      }
      setVerified(true);
    });
  };

  if (verified) {
    return <>{children}</>;
  }

  return (
    <div
      style={{
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
        padding: '20px',
      }}
    >
      <Card style={{ width: 350, textAlign: 'center' }}>
        <Title level={4}>Verify OTP</Title>
        {email ? (
          <div className="flex flex-col">
            <Text type="secondary">Your email: {email}</Text>
            <Text type="secondary" className="mb-3">
              Please verify your email to access this page.
            </Text>
            {step === 'send' && (
              <Button type="primary" loading={loading} onClick={sendOtp} block>
                Send OTP
              </Button>
            )}
            {step === 'verify' && (
              <>
                <Input
                  placeholder="Enter OTP"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  maxLength={6}
                  style={{ marginBottom: 12 }}
                />
                <Button type="primary" loading={isPending} onClick={handleVerify} block>
                  Verify OTP
                </Button>
              </>
            )}
          </div>
        ) : (
          <Text type="danger">No email found in access token.</Text>
        )}
      </Card>
    </div>
  );
};

export default OtpGate;
