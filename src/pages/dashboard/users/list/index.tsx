import { Tabs, TabsProps } from 'antd';
import React, { useState } from 'react';

import { Iconify } from '@/components/icon';
import { AppTitle } from '@/pages/components/constants';

import OtpGate from './OtpGate';
import UserDetail from './user-detail';
import UserList from './user-list';

import { UserData } from '#/user';

const User: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string[]>(['1']);
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [verified, setVerified] = useState(false);
  const [step, setStep] = useState<'send' | 'verify' | 'done'>('send');

  const handleUserSelect = (user: UserData) => {
    if (user === null) {
      setActiveTab(['1']);
    } else {
      setSelectedUser(user);
      setActiveTab(['2']);
      setIsEditing(false);
    }
  };

  const handleBackToList = () => {
    setActiveTab(['1']);
    setSelectedUser(null);
  };

  const handleUserEdit = (user: UserData) => {
    setIsEditing(true);
    setSelectedUser(user);
    setActiveTab(['3']);
  };

  const handleUserDelete = () => {
    setActiveTab(['1']);
    setSelectedUser(null);
  };

  const renderUserDetailTab =
    selectedUser && !isEditing && verified
      ? [
          {
            key: '2',
            label: (
              <div className="flex items-center">
                <Iconify icon="solar:user-circle-bold" size={24} className="mr-2" />
                <span>User Detail</span>
              </div>
            ),
            children: (
              <UserDetail
                userId={selectedUser.id}
                onBack={handleBackToList}
                onEdit={handleUserEdit}
                onDelete={handleUserDelete}
                activeTab={activeTab}
                setVerified={setVerified}
                setStep={setStep}
              />
            ),
          },
        ]
      : [];

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <div className="flex items-center">
          <Iconify icon="solar:list-bold" size={24} className="mr-2" />
          <span>User List</span>
        </div>
      ),
      children: (
        <>
          <UserList
            onUserSelect={handleUserSelect}
            activeTab={activeTab}
            setVerified={setVerified}
            setStep={setStep}
          />
          {/* </OtpGate> */}
        </>
      ),
    },
    ...renderUserDetailTab,
    // ...renderUserEditTab,
  ];

  return (
    <div className="p-2">
      <AppTitle subTitle="Users" />
      <OtpGate
        setVerified={() => setVerified(true)}
        verified={verified}
        step={step}
        setStep={setStep}
      >
        <Tabs activeKey={activeTab[0]} onChange={(key) => setActiveTab([key])} items={items} />
      </OtpGate>
    </div>
  );
};

export default User;
