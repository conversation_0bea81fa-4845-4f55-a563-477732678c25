import { Tabs, TabsProps } from 'antd';
import React, { useState } from 'react';

import { Iconify } from '@/components/icon';
import { AppTitle } from '@/pages/components/constants';

import AddHelpForm from './AddHelpForm';
import HelpDetail from './help-detail';
import EditHelp from './help-edit';
import HelpList from './help-list';

import { HelpData } from '#/help';

const Help: React.FC = () => {
  const [selectedHelpId, setSelectedHelpId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string[]>(['1']); // Active tab state
  const [isSingleHelpLoading, setIsSingleHelpLoading] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [selectedHelp, setSelectedHelp] = useState<HelpData | null>(null);

  const handleHelpClick = (Help: HelpData) => {
    if (Help === null) {
      setIsSingleHelpLoading(false);
      setActiveTab(['1']);
    } else {
      setIsEditing(false);
      setIsAdding(false);
      setSelectedHelpId(Help.id);
      setActiveTab(['2']);
    }
  };

  const handleBackToList = () => {
    setActiveTab(['1']);
    setSelectedHelpId(null);
    setIsAdding(false);
  };

  const handleHelpEdit = (Help: HelpData | null) => {
    if (Help === null) {
      setSelectedHelpId(null);
      setActiveTab(['1']);
      return;
    }

    setIsEditing(true);
    setIsAdding(false);
    setSelectedHelp(Help);
    setActiveTab(['3']);
  };

  const handleAddHelp = () => {
    setIsAdding(true);
    setIsEditing(false);
    setSelectedHelpId(null);
    setSelectedHelp(null);
    setActiveTab(['4']);
  };

  const handleHelpDelete = () => {
    setActiveTab(['1']);
    setSelectedHelpId(null);
  };

  const handleBackToDetail = () => {
    setIsEditing(false);
    setActiveTab(['2']);
    setSelectedHelp(null);
  };

  const handleSaveHelp = () => {
    setIsEditing(false);
    setIsAdding(false);
    setSelectedHelpId(selectedHelp?.id || '');
    setSelectedHelp(null);
    setActiveTab(['2']);
  };

  const handleSaveNewHelp = () => {
    setIsAdding(false);
    // You might want to navigate to the detail view of the newly created Help

    setActiveTab(['1']);
  };

  const renderHelpDetailTab =
    selectedHelpId && !isEditing
      ? [
          {
            key: '2',
            label: (
              <div className="flex items-center">
                <Iconify icon="solar:document-bold" size={24} className="mr-2" />
                <span>Help Detail</span>
              </div>
            ),
            children: (
              <HelpDetail
                id={selectedHelpId}
                onBack={handleBackToList}
                activeTab={activeTab}
                onDelete={handleHelpDelete}
                onEdit={handleHelpEdit}
              />
            ),
          },
        ]
      : [];

  const renderHelpEditTab =
    isEditing && selectedHelp
      ? [
          {
            key: '3',
            label: (
              <div className="flex items-center">
                <Iconify icon="solar:user-circle-bold" size={24} className="mr-2" />
                <span>Help Edit</span>
              </div>
            ),
            children: (
              <EditHelp Help={selectedHelp} onBack={handleBackToDetail} onSave={handleSaveHelp} />
            ),
          },
        ]
      : [];

  const renderHelpAddTab = isAdding
    ? [
        {
          key: '4',
          label: (
            <div className="flex items-center">
              <Iconify icon="solar:add-circle-bold" size={24} className="mr-2" />
              <span>Add Help</span>
            </div>
          ),
          children: <AddHelpForm onBack={handleBackToList} onSave={handleSaveNewHelp} />,
        },
      ]
    : [];

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <div className="flex items-center">
          <Iconify icon="solar:list-bold" size={24} className="mr-2" />
          <span>Help List</span>
        </div>
      ),
      children: (
        <HelpList onHelpClick={handleHelpClick} activeTab={activeTab} onAddHelp={handleAddHelp} />
      ),
    },
    ...(isSingleHelpLoading ? [] : []),
    ...renderHelpDetailTab,
    ...renderHelpEditTab,
    ...renderHelpAddTab,
  ];

  return (
    <div className="p-2">
      <AppTitle subTitle="Help" />

      <Tabs activeKey={activeTab[0]} onChange={(key) => setActiveTab([key])} items={items} />
    </div>
  );
};

export default Help;
