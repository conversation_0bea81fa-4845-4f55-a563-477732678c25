import { ArrowLeftOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Modal, Button } from 'antd';
import React, { useEffect } from 'react';
import { useDeleteHelp } from '@/api/ApiHooks/help-hooks';
import { GetSingleHelpApi } from '@/api/services/help/helpService';
import CustomCard from '@/components/custom/CustomCard';
import { HelpData } from '#/help';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

export interface HelpDetailProps {
  id: string | null;
  onBack: () => void;
  activeTab: string[];
  onDelete: () => void;
  onEdit: (Help: HelpData | null) => void;
}

const HelpDetail: React.FC<HelpDetailProps> = ({
  id: HelpId,
  onBack,
  activeTab,
  onDelete,
  onEdit,
}) => {
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['Help--detail', HelpId],
    queryFn: () => GetSingleHelpApi(HelpId || ''),
  });

  const { deleteHelp } = useDeleteHelp();

  const confirmDelete = () => {
    Modal.confirm({
      title: 'Are you sure you want to delete this?',
      content: 'This action cannot be undone.',
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        await deleteHelp(data?.data?.id || '', onDelete);
      },
    });
  };

  useEffect(() => {
    if (activeTab.some((item) => item === '2')) {
      refetch();
    }
  }, [activeTab]);

  if (isLoading) {
    return (
      <div className="flex h-full min-h-[60vh] items-center justify-center">
        <CustomLoader className="h-20 w-20" />
      </div>
    );
  }

  if (isError) {
    return <p>Error loading Help: {error?.message}</p>;
  }

  return (
    <CustomCard className="flex w-full flex-col items-start justify-between space-y-5">
      <div className="mb-4 flex w-full items-center justify-between">
        <Button type="default" onClick={onBack} icon={<ArrowLeftOutlined />} />
        <div className="flex items-center justify-between space-x-3">
          <Button type="primary" onClick={() => onEdit(data?.data || null)} icon={<EditOutlined />}>
            Update
          </Button>
          <Button danger onClick={confirmDelete} icon={<DeleteOutlined />}>
            Delete
          </Button>
        </div>
      </div>
      
      <div className="w-full space-y-6">
        <div>
          <h2 className="mb-2 text-xl font-semibold">Title</h2>
          <p className="text-gray-700">{data?.data?.title}</p>
        </div>
        
        <div>
          <h2 className="mb-2 text-xl font-semibold">Description</h2>
          <p className="whitespace-pre-wrap text-gray-700">{data?.data?.description}</p>
        </div>
      </div>
    </CustomCard>
  );
};

export default HelpDetail;