import { useQuery } from '@tanstack/react-query';
import { Table, Input, Pagination, Button } from 'antd';
import { AxiosError } from 'axios';
import React, { useEffect, useState } from 'react';
import { IoAdd } from 'react-icons/io5';

import { GetAllHelpApi } from '@/api/services/help/helpService';
import CustomCard from '@/components/custom/CustomCard';
import { ErrorView } from '@/components/error';

import { HelpData } from '#/help';
import type { ColumnsType } from 'antd/es/table';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

const { Search } = Input;

interface HelpListProps {
  onHelpClick: (Help: HelpData) => void;
  activeTab: string[];
  onAddHelp: () => void;
}

const HelpList: React.FC<HelpListProps> = ({ onHelpClick, activeTab, onAddHelp }) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [page, setPage] = useState<number>(1);

  // Fetch data with pagination and search query
  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ['list-Help-s', page, searchTerm],
    queryFn: () => GetAllHelpApi(page, searchTerm),
  });

  useEffect(() => {
    if (activeTab.some((item) => item === '1')) {
      refetch();
    }
  }, [activeTab]);

  // Table columns
  const columns: ColumnsType<HelpData> = [
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      render: (text: string) => <strong>{text}</strong>,
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text,
    },
  ];

  if (isError) {
    if (error instanceof AxiosError && error.response) {
      return (
        <ErrorView
          message={error.response.data.message || 'Something went wrong,Please try again later.'}
        />
      );
    } else {
      return <ErrorView message="Something went wrong,Please try again later." />;
    }
  }

  return (
    <CustomCard>
      {/* Add Button */}
      <div className="mb-4 flex w-full justify-end">
        <Button type="primary" onClick={onAddHelp}>
          <IoAdd className="text-[1.2rem]" />
          Add Help
        </Button>
      </div>
      
      {/* Search Input */}
      <div className="mb-4 flex flex-col space-y-2">
        <Search
          placeholder="Search for help"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {isLoading ? (
        <div className="flex h-full min-h-[60vh] items-center justify-center">
          <CustomLoader className="h-20 w-20" />
        </div>
      ) : (
        <div>
          <Table
            columns={columns}
            dataSource={data?.data}
            pagination={false}
            size="middle"
            rowKey="id"
            onRow={(record) => ({
              onClick: () => onHelpClick(record),
            })}
            style={{ cursor: 'pointer' }}
            scroll={{ x: true }}
          />
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>
            <Pagination
              current={page}
              total={data?.total || 0}
              pageSize={10}
              onChange={(newPage) => setPage(newPage)}
              showSizeChanger={false}
              className="pagination"
            />
          </div>
        </div>
      )}
    </CustomCard>
  );
};

export default HelpList;