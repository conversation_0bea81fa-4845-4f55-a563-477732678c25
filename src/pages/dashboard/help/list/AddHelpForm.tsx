import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Form, Input, message, Spin } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React, { useEffect } from 'react';
import { useCreateHelp, useUpdateHelp } from '@/api/ApiHooks/help-hooks';
import { CreateHelpReqObj } from '@/api/services/help/types';
import CustomCard from '@/components/custom/CustomCard';

interface AddHelpFormPropsInterface {
  onBack: () => void;
  onSave: () => void;
  initialData?: CreateHelpReqObj & { id?: string };
}

const AddHelpForm: React.FC<AddHelpFormPropsInterface> = ({ onBack, onSave, initialData }) => {
  const [form] = Form.useForm();
  const AddHelpFunc = useCreateHelp();
  const { UpdateHelp } = useUpdateHelp();
  const [isLoading, setIsLoading] = React.useState(false);

  // Populate form in edit mode
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
    }
  }, [initialData, form]);

  const handleFinish = async (values: CreateHelpReqObj) => {
    try {
      setIsLoading(true);
      if (initialData?.id) {
        await UpdateHelp(initialData.id, values);
        onSave();
      } else {
        await AddHelpFunc(values, onSave);
      }
    } catch (err) {
      console.error(err);
      message.error('Failed to save Help. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex w-full items-start justify-center">
      <CustomCard className="w-[70%] rounded-xl border-2 border-gray-300 p-8 shadow-sm">
        <Button type="default" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back
        </Button>
        <h1 className="mb-7 py-2 text-2xl font-semibold">
          {initialData ? 'Edit Help' : 'Add Help'}
        </h1>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFinish}
          disabled={isLoading}
          autoComplete="off"
        >
          <Form.Item
            label="Title"
            name="title"
            rules={[{ required: true, message: 'Please enter a title!' }]}
          >
            <Input placeholder="Enter title" />
          </Form.Item>

          <Form.Item
            label="Description"
            name="description"
            rules={[{ required: true, message: 'Please enter a description!' }]}
          >
            <TextArea 
              placeholder="Enter description" 
              rows={6}
              showCount
              maxLength={1000}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="w-full bg-orange-500 font-semibold"
              disabled={isLoading}
            >
              {isLoading ? <Spin /> : initialData ? 'Update Help' : 'Add Help'}
            </Button>
          </Form.Item>
        </Form>
      </CustomCard>
    </div>
  );
};

export default AddHelpForm;