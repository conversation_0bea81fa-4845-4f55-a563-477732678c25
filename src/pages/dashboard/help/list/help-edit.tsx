import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Form, Input, message, Spin } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React, { useState, useEffect } from 'react';
import { useUpdateHelp } from '@/api/ApiHooks/help-hooks';
import { UpdateHelpApiReqObj } from '@/api/services/help/types';
import CustomCard from '@/components/custom/CustomCard';
import { HelpData } from '#/help';

interface EditHelpProps {
  Help: HelpData;
  onBack: () => void;
  onSave: (updatedHelp: HelpData) => void;
}

const EditHelp: React.FC<EditHelpProps> = ({ Help, onBack, onSave }) => {
  const [form] = Form.useForm();
  const { UpdateHelp } = useUpdateHelp();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (Help) {
      form.setFieldsValue({
        title: Help.title,
        description: Help.description,
      });
    }
  }, [Help, form]);

  const handleFinish = async (values: UpdateHelpApiReqObj) => {
    try {
      setIsLoading(true);
      const resp = await UpdateHelp(Help?.id || '', values);
      if (resp === true) {
        onSave({ ...Help, ...values });
      }
    } catch (err) {
      console.error(err);
      message.error('Failed to update Help. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex w-full items-start justify-center">
      <CustomCard className="w-[70%] rounded-xl border-2 border-gray-300 p-8 shadow-sm">
        <Button type="default" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back
        </Button>
        <h1 className="mb-7 py-2 text-2xl font-semibold">Edit Help</h1>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFinish}
          autoComplete="off"
          disabled={isLoading}
        >
          <Form.Item
            label="Title"
            name="title"
            rules={[{ required: true, message: 'Please enter a title!' }]}
          >
            <Input placeholder="Enter title" />
          </Form.Item>

          <Form.Item
            label="Description"
            name="description"
            rules={[{ required: true, message: 'Please enter a description!' }]}
          >
            <TextArea 
              placeholder="Enter description" 
              rows={6}
              showCount
              maxLength={1000}
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className="w-full bg-orange-500 font-semibold"
              disabled={isLoading}
            >
              {isLoading ? <Spin /> : 'Update Help'}
            </Button>
          </Form.Item>
        </Form>
      </CustomCard>
    </div>
  );
};

export default EditHelp;