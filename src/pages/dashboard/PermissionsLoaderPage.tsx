import Color from 'color';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import Overlay2 from 'public/images/background/overlay_2.jpg';
import { useUserPermission, useUserToken } from '@/store/userStore';
import { useThemeToken } from '@/theme/hooks';
import CustomLoader from '../components/loaders/CustomLoader';

const PermissionsLoaderPage = () => {
  const navigate = useNavigate();
  const { accessToken } = useUserToken();
  const permissions = useUserPermission();

  const { colorBgElevated } = useThemeToken();
  const gradientBg = Color(colorBgElevated).alpha(0.9).toString();
  const bg = `linear-gradient(${gradientBg}, ${gradientBg}) center center / cover no-repeat,url(${Overlay2})`;

  useEffect(() => {
    if (accessToken) {
      if (permissions.length > 0) {
        navigate(permissions[0].route);
      } else {
        navigate('/loader');
      }
    } else {
      navigate('/login');
    }
  }, [permissions]);

  return (
    <div
      className="flex h-[100vh] w-full items-center justify-center"
      style={{
        background: bg,
      }}
    >
      <CustomLoader />
    </div>
  );
};

export default PermissionsLoaderPage;
