import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Form, Input, Card } from 'antd';
import React from 'react';

import { useUpdateFAQ } from '@/api/ApiHooks/faq-hooks';
import { UpdateFAQApiReqObj } from '@/api/services/faq/types';

import { FaqData } from '#/faq';

interface EditFaqProps {
  Faq: FaqData;
  onBack: () => void;
  onSave: (updatedFaq: FaqData) => void;
}

const EditFaq: React.FC<EditFaqProps> = ({ Faq, onBack, onSave }) => {
  const [form] = Form.useForm();
  const { UpdateFAQ } = useUpdateFAQ();

  const handleFinish = async (values: any) => {
    const updatedata: UpdateFAQApiReqObj = {
      answer: values.description,
      question: values.title,
    };

    const resp = await UpdateFAQ(Faq?.id || '', updatedata);
    if (resp === true) {
      onSave(Faq);
    }
  };

  return (
    <Card
      className="mt-4 p-6"
      style={{ boxShadow: '0 4px 8px rgba(0,0,0,0.1)', borderRadius: '8px' }}
    >
      <div className="mb-8 flex w-full items-center justify-between">
        <Button type="default" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back
        </Button>
      </div>

      <Form
        form={form}
        onFinish={handleFinish}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        labelAlign="left"
        initialValues={{
          title: Faq.question,
          description: Faq.answer,
        }}
      >
        <Form.Item
          name="title"
          label="Question"
          rules={[{ required: true, message: 'Please input the FAQ question!' }]}
        >
          <Input placeholder="Enter FAQ question" />
        </Form.Item>

        <Form.Item
          name="description"
          label="Answer"
          rules={[{ required: true, message: 'Please input the FAQ answer!' }]}
        >
          <Input.TextArea placeholder="Enter FAQ answer" rows={4} />
        </Form.Item>

        <div style={{ marginTop: '20px', textAlign: 'right' }}>
          <Button type="primary" htmlType="submit">
            Save
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditFaq;
