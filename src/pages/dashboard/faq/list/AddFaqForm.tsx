import { ArrowLeftOutlined } from '@ant-design/icons';
import { message, Input, Spin, Button } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React, { useState } from 'react';

import { useCreateFAQ } from '@/api/ApiHooks/faq-hooks';
import { CreatFAQReqObj } from '@/api/services/faq/types';
import CustomCard from '@/components/custom/CustomCard';

interface AddFaqFormPropsInterface {
  onBack: () => void;
  onSave: () => void;
}

const AddFaqForm: React.FC<AddFaqFormPropsInterface> = ({ onBack, onSave }) => {
  const AddFaqFunc = useCreateFAQ();
  const [isLoading, setIsLoading] = useState(false);
  const [FaqData, setFaqData] = useState<CreatFAQReqObj>({
    question: '',
    answer: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFaqData((prevState) => ({ ...prevState, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const { question, answer } = FaqData;

    if (!question.trim()) return message.error(' question is required!');
    if (!answer.trim()) return message.error(' answer is required!');

    try {
      setIsLoading(true);

      await AddFaqFunc({ ...FaqData }, onSave);
    } catch (error) {
      message.error('Failed to add Faq . Please try again.');
      console.error('Faq  creation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex w-full items-start justify-center">
      <CustomCard className="w-[70%] rounded-xl border-2 border-gray-300 p-8 shadow-sm">
        <Button type="default" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back
        </Button>
        <h1 className="mb-7 pt-4 text-2xl font-semibold">Add Faq Recommendation </h1>
        <form onSubmit={handleSubmit} className="space-y-7">
          <div>
            <label className="mr-2 space-y-2 font-semibold">Question:</label>

            <Input
              name="question"
              value={FaqData.question}
              onChange={handleInputChange}
              placeholder="Enter question"
              disabled={isLoading} // Disable input during loading
            />
          </div>

          <div>
            <label className="mr-2 space-y-2 font-semibold">Answer:</label>

            <TextArea
              rows={5}
              name="answer"
              value={FaqData.answer}
              onChange={handleInputChange}
              placeholder="Enter answer"
              disabled={isLoading} // Disable input during loading
            />
          </div>

          <button
            type="submit"
            className="bg-orange-500 w-full rounded bg-primary py-2 font-semibold text-white shadow-md duration-100 hover:shadow-md"
            disabled={isLoading}
          >
            {isLoading ? <Spin /> : 'Add Faq '}
          </button>
        </form>
      </CustomCard>
    </div>
  );
};

export default AddFaqForm;
