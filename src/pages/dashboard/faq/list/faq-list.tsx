import { useQuery } from '@tanstack/react-query';
import { Table, Input, Pagination, Space, Button } from 'antd';
import { AxiosError } from 'axios';
import React, { useEffect, useState } from 'react';
import { IoAdd } from 'react-icons/io5';

import { GetAllFAQApi } from '@/api/services/faq/faqService';
import CustomCard from '@/components/custom/CustomCard';
import { ErrorView } from '@/components/error';

import { FaqData } from '#/faq';
import type { ColumnsType } from 'antd/es/table';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

const { Search } = Input;

interface FaqListProps {
  onFaqClick: (Faq: FaqData) => void;
  activeTab: string[];
  onAddFaq: () => void;
}

const FaqList: React.FC<FaqListProps> = ({ onFaqClick, activeTab, onAddFaq }) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [page, setPage] = useState<number>(1);

  // Fetch data with pagination and search query
  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ['list-Faq-s', page, searchTerm],
    queryFn: () => GetAllFAQApi(page, searchTerm),
  });

  useEffect(() => {
    if (activeTab.some((item) => item === '1')) {
      refetch();
    }
  }, [activeTab]);

  // Table columns
  const columns: ColumnsType<FaqData> = [
    {
      title: 'Question',
      dataIndex: 'question',
      key: 'question',
      filterSearch: true,
      sorter: (a, b) => a.question.localeCompare(b.question),
      width: 300,
      render: (text) => (
        <Space className="truncate font-medium capitalize">
          <span>{text?.length > 40 ? `${text.substring(0, 40)}...` : text || 'N/A'}</span>
        </Space>
      ),
    },

    {
      title: 'Answer',
      dataIndex: 'answer',
      key: 'answer',
      filterSearch: true,
      sorter: (a, b) => a.answer.localeCompare(b.answer),
      width: 300,
      render: (text) => (
        <Space className="truncate font-medium capitalize">
          <span>{text?.length > 40 ? `${text.substring(0, 40)}...` : text || 'N/A'}</span>
        </Space>
      ),
    },

    {
      title: 'Created At',
      dataIndex: 'createdAt', // Assuming 'createdAt' is part of the data
      key: 'createdAt',
      width: 200,
      render: (createdAt) => (
        <span>{new Date(createdAt).toLocaleDateString()}</span> // Format the date as per your requirement
      ),
    },
  ];

  if (isError) {
    if (error instanceof AxiosError && error.response) {
      return (
        <ErrorView
          message={error.response.data.message || 'Something went wrong,Please try again later.'}
        />
      );
    } else {
      return <ErrorView message="Something went wrong,Please try again later." />;
    }
  }

  return (
    <CustomCard>
      <div className="mb-4 flex w-full justify-end">
        <Button type="primary" onClick={onAddFaq}>
          <IoAdd className="text-[1.2rem]" />
          Add FAQ
        </Button>
      </div>
      {/* Search Input */}
      <div className="mb-4 flex flex-col space-y-2">
        <Search
          placeholder="Search By FAQ Question"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {isLoading ? (
        <div className="flex h-full min-h-[60vh] items-center justify-center">
          <CustomLoader className="h-20 w-20" />
        </div>
      ) : (
        <div>
          <Table
            columns={columns}
            dataSource={data?.data?.map((faq) => ({ ...faq, key: faq.id })) || []}
            pagination={false}
            size="middle"
            onRow={(record) => ({
              onClick: () => {
                onFaqClick(record);
              },
            })}
            style={{ cursor: 'pointer' }}
            scroll={{ x: true }}
          />
          {/* Pagination Component */}
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>
            <Pagination
              current={page}
              total={data?.total || 0}
              pageSize={10}
              onChange={(newPage) => setPage(newPage)}
              showSizeChanger={false}
              className="pagination"
            />
          </div>
        </div>
      )}
    </CustomCard>
  );
};

export default FaqList;
