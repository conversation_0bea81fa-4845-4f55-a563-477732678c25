import { ArrowLeftOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Modal, Button } from 'antd';
import React, { useState, useEffect } from 'react';

import { useDeleteFAQ } from '@/api/ApiHooks/faq-hooks';
import { GetSingleFAQApi } from '@/api/services/faq/faqService';
import CustomCard from '@/components/custom/CustomCard';

import { FaqData } from '#/faq';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

export interface FaqDetailProps {
  id: string | null;
  onBack: () => void;
  activeTab: string[];
  onDelete: () => void;
  onEdit: (Faq: FaqData | null) => void;
}

const FaqDetail: React.FC<FaqDetailProps> = ({
  id: FaqId,
  onBack,
  activeTab,
  onDelete,
  onEdit,
}) => {
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['Faq--detail', FaqId],
    queryFn: () => GetSingleFAQApi(FaqId || ''),
  });

  const [expanded, setExpanded] = useState(false);

  const { deleteFAQ } = useDeleteFAQ();

  const confirmDelete = () => {
    Modal.confirm({
      title: 'Are you sure you want to delete this ?',
      content: 'This action cannot be undone.',
      okText: 'Yes, Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        await deleteFAQ(data?.data?.id || '', onDelete);
      },
    });
  };

  useEffect(() => {
    if (activeTab.some((item) => item === '2')) {
      refetch();
    }
  }, [activeTab]);

  if (isLoading) {
    return (
      <div className="flex h-full min-h-[60vh] items-center justify-center">
        <CustomLoader className="h-20 w-20" />
      </div>
    );
  }

  if (isError) {
    return <p>Error loading Faq : {error?.message}</p>;
  }

  return (
    <CustomCard className="flex w-full flex-col items-start justify-between space-y-5">
      <div className="mb-4 flex w-full items-center justify-between">
        <Button type="default" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back
        </Button>

        <div className="flex items-center justify-between space-x-3">
          <Button type="primary" onClick={() => onEdit(data?.data || null)} icon={<EditOutlined />}>
            Update
          </Button>

          <Button danger onClick={confirmDelete} icon={<DeleteOutlined />}>
            Delete
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <p className="capitalize">
          <strong className="mr-2">Question:</strong> {data?.data?.question}
        </p>

        <p className="capitalize">
          <strong className="mr-2">Answer:</strong>
          {expanded ? data?.data?.answer : `${data?.data?.answer?.slice(0, 100) ?? ''}... `}
          {(data?.data?.answer?.length ?? 0) > 100 && (
            <button
              type="button"
              className="ml-2 text-primary"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? 'Read less' : 'Read more'}
            </button>
          )}
        </p>
      </div>
    </CustomCard>
  );
};

export default FaqDetail;
