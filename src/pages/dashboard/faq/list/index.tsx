import { Tabs, TabsProps } from 'antd';
import React, { useState } from 'react';

import { Iconify } from '@/components/icon';
import { AppTitle } from '@/pages/components/constants';

import AddFaqForm from './AddFaqForm';
import FaqDetail from './faq-detail';
import EditFaq from './faq-edit';
import FaqList from './faq-list';

import { FaqData } from '#/faq';

const Faq: React.FC = () => {
  const [selectedFaqId, setSelectedFaqId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string[]>(['1']); // Active tab state
  const [isSingleFaqLoading, setIsSingleFaqLoading] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [selectedFaq, setSelectedFaq] = useState<FaqData | null>(null);

  const handleFaqClick = (Faq: FaqData) => {
    if (Faq === null) {
      setIsSingleFaqLoading(false);
      setActiveTab(['1']);
    } else {
      setIsEditing(false);
      setIsAdding(false);
      setSelectedFaqId(Faq.id);
      setActiveTab(['2']);
    }
  };

  const handleBackToList = () => {
    setActiveTab(['1']);
    setSelectedFaqId(null);
    setIsAdding(false);
  };

  const handleFaqEdit = (Faq: FaqData | null) => {
    if (Faq === null) {
      setSelectedFaqId(null);
      setActiveTab(['1']);
      return;
    }

    setIsEditing(true);
    setIsAdding(false);
    setSelectedFaq(Faq);
    setActiveTab(['3']);
  };

  const handleAddFaq = () => {
    setIsAdding(true);
    setIsEditing(false);
    setSelectedFaqId(null);
    setSelectedFaq(null);
    setActiveTab(['4']);
  };

  const handleFaqDelete = () => {
    setActiveTab(['1']);
    setSelectedFaqId(null);
  };

  const handleBackToDetail = () => {
    setIsEditing(false);
    setActiveTab(['2']);
    setSelectedFaq(null);
  };

  const handleSaveFaq = () => {
    setIsEditing(false);
    setIsAdding(false);
    setSelectedFaqId(selectedFaq?.id || '');
    setSelectedFaq(null);
    setActiveTab(['2']);
  };

  const handleSaveNewFaq = () => {
    setIsAdding(false);
    setActiveTab(['1']);
  };

  const renderFaqDetailTab =
    selectedFaqId && !isEditing
      ? [
          {
            key: '2',
            label: (
              <div className="flex items-center">
                <Iconify icon="solar:document-bold" size={24} className="mr-2" />
                <span>Faq Detail</span>
              </div>
            ),
            children: (
              <FaqDetail
                id={selectedFaqId}
                onBack={handleBackToList}
                activeTab={activeTab}
                onDelete={handleFaqDelete}
                onEdit={handleFaqEdit}
              />
            ),
          },
        ]
      : [];

  const renderFaqEditTab =
    isEditing && selectedFaq
      ? [
          {
            key: '3',
            label: (
              <div className="flex items-center">
                <Iconify icon="solar:user-circle-bold" size={24} className="mr-2" />
                <span>FAQs Edit</span>
              </div>
            ),
            children: (
              <EditFaq Faq={selectedFaq} onBack={handleBackToDetail} onSave={handleSaveFaq} />
            ),
          },
        ]
      : [];

  const renderFaqAddTab = isAdding
    ? [
        {
          key: '4',
          label: (
            <div className="flex items-center">
              <Iconify icon="solar:add-circle-bold" size={24} className="mr-2" />
              <span>Add FAQs</span>
            </div>
          ),
          children: <AddFaqForm onBack={handleBackToList} onSave={handleSaveNewFaq} />,
        },
      ]
    : [];

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <div className="flex items-center">
          <Iconify icon="solar:list-bold" size={24} className="mr-2" />
          <span>FAQs List</span>
        </div>
      ),
      children: (
        <FaqList onFaqClick={handleFaqClick} activeTab={activeTab} onAddFaq={handleAddFaq} />
      ),
    },
    ...(isSingleFaqLoading ? [] : []),
    ...renderFaqDetailTab,
    ...renderFaqEditTab,
    ...renderFaqAddTab,
  ];

  return (
    <div className="p-2">
      <AppTitle subTitle="FAQs" />

      <Tabs activeKey={activeTab[0]} onChange={(key) => setActiveTab([key])} items={items} />
    </div>
  );
};

export default Faq;
