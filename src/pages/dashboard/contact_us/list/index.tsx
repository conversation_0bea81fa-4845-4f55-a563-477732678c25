import { Tabs, TabsProps } from 'antd';
import React, { useState } from 'react';

import { Iconify } from '@/components/icon';
import { AppTitle } from '@/pages/components/constants';

import ContactQueryDetail from './contact-us-query-detail';
import EditContactQuery from './contact-us-query-edit';
import ContactQueryList from './contact-us-query-list';

import { ContactQueryData } from '#/contactQuery';

const ContactQuery: React.FC = () => {
  const [selectedContactQueryId, setSelectedContactQueryId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string[]>(['1']); // Active tab state
  const [isSingleContactQueryLoading, setIsSingleContactQueryLoading] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedContactQuery, setSelectedContactQuery] = useState<ContactQueryData | null>(null);

  const handleContactQueryClick = (ContactQuery: ContactQueryData) => {
    if (ContactQuery === null) {
      setIsSingleContactQueryLoading(false);
      setActiveTab(['1']);
    } else {
      setIsEditing(false);
      setSelectedContactQueryId(ContactQuery.id);
      setActiveTab(['2']);
    }
  };

  const handleBackToList = () => {
    setActiveTab(['1']);
    setSelectedContactQueryId(null);
  };

  const handleContactQueryEdit = (ContactQuery: ContactQueryData | null) => {
    if (ContactQuery === null) {
      setSelectedContactQueryId(null);
      setActiveTab(['1']);
      return;
    }

    setIsEditing(true);
    setSelectedContactQuery(ContactQuery);
    setActiveTab(['3']);
  };

  const handleContactQueryDelete = () => {
    setActiveTab(['1']);
    setSelectedContactQueryId(null);
  };

  const handleBackToDetail = () => {
    setIsEditing(false);
    setActiveTab(['2']);
    setSelectedContactQuery(null);
  };

  const handleSaveContactQuery = () => {
    setIsEditing(false);
    setSelectedContactQueryId(selectedContactQuery?.id || '');
    setSelectedContactQuery(null);
    setActiveTab(['2']);
  };

  const renderContactQueryDetailTab =
    selectedContactQueryId && !isEditing
      ? [
          {
            key: '2',
            label: (
              <div className="flex items-center">
                <Iconify icon="solar:document-bold" size={24} className="mr-2" />
                <span>Contact Query Detail</span>
              </div>
            ),
            children: (
              <ContactQueryDetail
                id={selectedContactQueryId}
                onBack={handleBackToList}
                activeTab={activeTab}
                onDelete={handleContactQueryDelete}
                onEdit={handleContactQueryEdit}
              />
            ),
          },
        ]
      : [];

  const renderContactQueryEditTab =
    isEditing && selectedContactQuery
      ? [
          {
            key: '3',
            label: (
              <div className="flex items-center">
                <Iconify icon="solar:user-circle-bold" size={24} className="mr-2" />
                <span>Contact Query Edit</span>
              </div>
            ),
            children: (
              <EditContactQuery
                ContactQuery={selectedContactQuery}
                onBack={handleBackToDetail}
                onSave={handleSaveContactQuery}
              />
            ),
          },
        ]
      : [];

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <div className="flex items-center">
          <Iconify icon="solar:list-bold" size={24} className="mr-2" />
          <span>Contact Query List</span>
        </div>
      ),
      children: (
        <ContactQueryList onContactQueryClick={handleContactQueryClick} activeTab={activeTab} />
      ),
    },
    ...(isSingleContactQueryLoading ? [] : []),
    ...renderContactQueryDetailTab,
    ...renderContactQueryEditTab,
  ];

  return (
    <div className="p-2">
      <AppTitle subTitle="Contact Us Queries" />

      <Tabs activeKey={activeTab[0]} onChange={(key) => setActiveTab([key])} items={items} />
    </div>
  );
};

export default ContactQuery;
