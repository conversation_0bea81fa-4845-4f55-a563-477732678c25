import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button, Form, Select, Card } from 'antd';
import React from 'react';

import { useUpdateContactQuery } from '@/api/ApiHooks/contact-us-query-hooks';
import { UpdateContactQueryApiReqObj } from '@/api/services/contact-us-query/types';

import { ContactQueryData } from '#/contactQuery';

export enum QUERY_STATUS {
  PENDING = 'pending',
  RESOLVED = 'resolved',
}

interface EditContactQueryProps {
  ContactQuery: ContactQueryData;
  onBack: () => void;
  onSave: (updatedContactQuery: ContactQueryData) => void;
}

const EditContactQuery: React.FC<EditContactQueryProps> = ({ ContactQuery, onBack, onSave }) => {
  const [form] = Form.useForm();
  const { UpdateContactQuery } = useUpdateContactQuery();

  const handleFinish = async (values: any) => {
    const updatedata: UpdateContactQueryApiReqObj = {
      status: values.status,
    };

    const resp = await UpdateContactQuery(ContactQuery?.id || '', updatedata);
    if (resp === true) {
      onSave(ContactQuery);
    }
  };

  return (
    <Card
      className="mt-4 p-6"
      style={{ boxShadow: '0 4px 8px rgba(0,0,0,0.1)', borderRadius: '8px' }}
    >
      <div className="mb-8 flex w-full items-center justify-between">
        <Button type="default" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back
        </Button>
      </div>

      <Form
        form={form}
        onFinish={handleFinish}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        labelAlign="left"
        initialValues={{
          status: ContactQuery.status,
        }}
      >
        <Form.Item
          name="status"
          label="Query Status"
          rules={[{ required: true, message: 'Please select the ContactQuery status!' }]}
        >
          <Select placeholder="Select ContactQuery status">
            <Select.Option value={QUERY_STATUS.PENDING}>Pending</Select.Option>
            <Select.Option value={QUERY_STATUS.RESOLVED}>Resolved</Select.Option>
          </Select>
        </Form.Item>

        <div style={{ marginTop: '20px', textAlign: 'right' }}>
          <Button type="primary" htmlType="submit">
            Save
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditContactQuery;
