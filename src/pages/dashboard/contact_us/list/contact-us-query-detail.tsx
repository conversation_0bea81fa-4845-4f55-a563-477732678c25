import { ArrowLeftOutlined, EditOutlined, CloseOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Button, Tooltip } from 'antd';
import React, { useState, useEffect } from 'react';
import { GoMail } from 'react-icons/go';

import { GetSingleContactApi } from '@/api/services/contact-us-query/contactService';
import CustomCard from '@/components/custom/CustomCard';
import ProTag from '@/theme/antd/components/tag';

import { ContactQueryData } from '#/contactQuery';
import { QUERY_STATUS } from '#/enum';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

export interface ContactQueryDetailProps {
  id: string | null;
  onBack: () => void;
  activeTab: string[];
  onDelete: () => void;
  onEdit: (ContactQuery: ContactQueryData | null) => void;
}

const ContactQueryDetail: React.FC<ContactQueryDetailProps> = ({
  id: ContactQueryId,
  onBack,
  activeTab,
  onEdit,
}) => {
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['ContactQuery--detail', ContactQueryId],
    queryFn: () => GetSingleContactApi(ContactQueryId || ''),
  });

  const [expanded, setExpanded] = useState(false);
  const [previewFile, setPreviewFile] = useState<any | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (activeTab.some((item) => item === '2')) {
      refetch();
    }
  }, [activeTab]);

  const handleReply = () => {
    const email = data?.query?.user?.email;
    const subject = 'Response to Your Contact Us Inquiry'; // Customize this
    const body = `Hello ${data?.query.user?.name},\nWe have resolved your request.`;

    if (email) {
      const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodeURIComponent(email)}&su=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.open(gmailUrl, '_blank'); // Opens Gmail in a new tab
    }
  };

  const handlePreview = (file: any) => {
    setPreviewFile(file);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setPreviewFile(null);
  };

  if (isLoading) {
    return (
      <div className="flex h-full min-h-[60vh] items-center justify-center">
        <CustomLoader className="h-20 w-20" />
      </div>
    );
  }

  if (isError) {
    return <p>Error loading Query : {error?.message}</p>;
  }

  return (
    <CustomCard className="flex w-full flex-col items-start justify-between space-y-5">
      <div className="mb-4 flex w-full items-center justify-between">
        <Button type="default" onClick={onBack} icon={<ArrowLeftOutlined />}>
          Back
        </Button>

        <div className="flex items-center justify-between space-x-3">
          <Tooltip title="Click to reply via mail to">
            <Button type="primary" onClick={handleReply} icon={<GoMail />} className="ml-3">
              Respond to User
            </Button>
          </Tooltip>

          <Button
            type="primary"
            onClick={() => onEdit(data?.query || null)}
            icon={<EditOutlined />}
          >
            Update
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <p className="font-medium">
          <strong className="mr-2">ID:</strong>{' '}
          <span className="text-gray-600">{data?.query?.id}</span>
        </p>

        <p className="capitalize">
          <strong className="mr-2">Category:</strong> {data?.query?.category}
        </p>

        <p className="capitalize">
          <strong className="mr-2">Query Description:</strong>
          {expanded ? data?.query?.description : `${data?.query?.description?.slice(0, 100) ?? ''}`}
          {(data?.query?.description?.length ?? 0) > 100 && (
            <button
              type="button"
              className="ml-2 text-primary"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? 'Read less' : 'Read more'}
            </button>
          )}
        </p>

        <p>
          <strong className="mr-2">Status:</strong>{' '}
          <ProTag
            color={data?.query?.status === QUERY_STATUS.RESOLVED ? 'blue' : 'red'}
            className="capitalize"
          >
            {data?.query?.status}
          </ProTag>
        </p>

        <p className="capitalize">
          <strong className="mr-2">Name:</strong> {data?.query?.user?.name}
        </p>

        <p className="flex items-center">
          <strong className="mr-2">Email:</strong>
          {data?.query?.user?.email}
        </p>

        <div>
          <strong className="mr-2">Attachments:</strong>
          {Array.isArray(data?.query?.attachments) && data.query.attachments.length > 0 ? (
            <div className="mt-2 flex flex-wrap gap-4">
              {data.query.attachments.map((file: any, idx: number) => (
                <div
                  key={idx}
                  className="cursor-pointer"
                  onClick={() => handlePreview(file)}
                  title="Click to preview"
                >
                  {file.type === 'image' ? (
                    <img
                      src={file.url}
                      alt={`attachment-${idx}`}
                      className="h-32 w-32 rounded border object-cover"
                    />
                  ) : file.type === 'video' ? (
                    <video src={file.url} className="h-32 w-48 rounded border object-cover" muted />
                  ) : (
                    <div className="flex items-center space-x-2">
                      <span className="text-blue-600 underline">
                        {file.name || `Download file ${idx + 1}`}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <span className="text-gray-500">No Attachment</span>
          )}
        </div>
      </div>

      {isModalOpen && previewFile && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
          <div
            className={`relative w-full ${previewFile.type === 'video' ? 'max-w-4xl' : 'max-w-lg'} rounded bg-white p-8 shadow-lg`}
          >
            <button
              className="absolute right-2 top-2 text-lg font-bold text-gray-600 hover:text-black"
              onClick={handleModalClose}
            >
              <CloseOutlined style={{ fontSize: 20 }} />
            </button>
            {previewFile.type === 'image' ? (
              <img src={previewFile.url} alt="Preview" className="mx-auto max-h-[70vh]" />
            ) : previewFile.type === 'video' ? (
              <video
                src={previewFile.url}
                controls
                className="mx-auto h-auto max-h-[80vh] w-full rounded"
              />
            ) : (
              <a
                href={previewFile.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline"
              >
                {previewFile.name || 'Download file'}
              </a>
            )}
          </div>
        </div>
      )}
    </CustomCard>
  );
};

export default ContactQueryDetail;
