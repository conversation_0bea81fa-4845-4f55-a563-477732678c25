import { FilterOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import { Table, Input, Pagination, Select, Button } from 'antd';
import { AxiosError } from 'axios';
import React, { useEffect, useState } from 'react';

import { GetAllContactApi } from '@/api/services/contact-us-query/contactService';
import CustomCard from '@/components/custom/CustomCard';
import { ErrorView } from '@/components/error';
import ProTag from '@/theme/antd/components/tag';

import { categoryColorMap, ContactQueryData } from '#/contactQuery';
import { QUERY_CATEGORY, QUERY_STATUS } from '#/enum';
import type { ColumnsType } from 'antd/es/table';
import CustomLoader from '@/pages/components/loaders/CustomLoader';

const { Search } = Input;
const { Option } = Select;

interface ContactQueryListProps {
  onContactQueryClick: (ContactQuery: ContactQueryData) => void;
  activeTab: string[];
}

const ContactQueryList: React.FC<ContactQueryListProps> = ({ onContactQueryClick, activeTab }) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [validSearchTerm, setValidSearchTerm] = useState<string>('');
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const [statusFilter, setStatusFilter] = useState<string>('');

  // Fetch data with pagination, search query, and filters
  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ['list-ContactQuery-s', page, validSearchTerm, statusFilter],
    queryFn: () => GetAllContactApi(page, validSearchTerm, statusFilter),
  });

  useEffect(() => {
    if (activeTab.some((item) => item === '1')) {
      refetch();
    }
  }, [activeTab]);

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSearch = () => {
    if (isValidEmail(searchTerm)) {
      setValidSearchTerm(searchTerm);
      setIsSearching(true);
      setPage(1);
    }
  };

  const handleRemove = () => {
    setSearchTerm('');
    setValidSearchTerm('');
    setIsSearching(false);
    setPage(1);
  };

  const columns: ColumnsType<ContactQueryData> = [
    {
      title: 'User Name',
      dataIndex: 'user',
      key: 'name',
      sorter: (a, b) => a.user?.name?.localeCompare(b.user?.name ?? '') ?? 0,
      width: 200,
      render: (user) => <span className="capitalize">{user?.name || 'N/A'}</span>,
    },
    {
      title: 'User Email',
      dataIndex: 'user',
      key: 'email',
      sorter: (a, b) => a.user?.email?.localeCompare(b.user?.email ?? '') ?? 0,
      width: 250,
      render: (user) => <span>{user?.email || 'N/A'}</span>,
    },
    {
      title: 'Query Category',
      dataIndex: 'category',
      key: 'category',
      sorter: (a, b) => a.category.localeCompare(b.category),
      width: 200,
      render: (category) => (
        <ProTag
          className={`capitalize`}
          color={categoryColorMap[category as QUERY_CATEGORY] || 'gray'}
        >
          {category}
        </ProTag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 150,
      render: (status) => (
        <ProTag color={status === QUERY_STATUS.RESOLVED ? 'blue' : 'red'} className="capitalize">
          {status === QUERY_STATUS.RESOLVED ? QUERY_STATUS.RESOLVED : QUERY_STATUS.PENDING}
        </ProTag>
      ),
    },
  ];

  if (isError) {
    if (error instanceof AxiosError && error.response) {
      return (
        <ErrorView
          message={error.response.data.message || 'Something went wrong,Please try again later.'}
        />
      );
    } else {
      return <ErrorView message="Something went wrong,Please try again later." />;
    }
  }

  return (
    <CustomCard>
      {/* Filters Section */}
      <div className="mb-2 flex w-full items-center justify-end space-x-2">
        <FilterOutlined className="h-10 text-lg text-gray-600" />
        <Select
          style={{ width: 140 }}
          value={statusFilter}
          onChange={(value) => setStatusFilter(value)}
          placeholder="Filter by Status"
        >
          <Option value="">All Status</Option>
          <Option value={QUERY_STATUS.RESOLVED}>Resolved</Option>
          <Option value={QUERY_STATUS.PENDING}>Pending</Option>
        </Select>
      </div>

      {/* Search Section */}
      <div className="mb-4 flex space-x-2">
        <Search
          placeholder="Search By User Email"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          disabled={isSearching}
        />
        <Button type="primary" onClick={handleSearch} disabled={!isValidEmail(searchTerm)}>
          Search
        </Button>
        <Button danger onClick={handleRemove} disabled={!isSearching}>
          Remove
        </Button>
      </div>

      {isLoading ? (
        <div className="flex h-full min-h-[60vh] items-center justify-center">
          <CustomLoader className="h-20 w-20" />
        </div>
      ) : (
        <div>
          <Table
            columns={columns}
            dataSource={data?.queries}
            pagination={false}
            size="middle"
            onRow={(record) => ({
              onClick: () => onContactQueryClick(record),
            })}
            style={{ cursor: 'pointer' }}
            scroll={{ x: true }}
          />
          {/* Pagination Component */}
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>
            <Pagination
              current={page}
              total={data?.total || 0}
              pageSize={10}
              onChange={(newPage) => setPage(newPage)}
              showSizeChanger={false}
              className="pagination"
            />
          </div>
        </div>
      )}
    </CustomCard>
  );
};

export default ContactQueryList;
