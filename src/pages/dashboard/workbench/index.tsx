import { AppTitle } from '@/pages/components/constants';
import ActiveUsersChart from './components/graphs/users/ActiveUsersChart';
import { useSettings } from '@/store/settingStore';
import { ThemeMode } from '#/enum';
import { ActiveRegisteredUserByAge } from './components/graphs/users/ActiveRegisteredUserByAge';

const Dashboard = () => {
  const { themeMode } = useSettings();
  return (
    <div className="p-6">
      <AppTitle subTitle="Admin Dashboard" />

      <div className="">
        <h1 className="font mb-5 mt-5 text-4xl font-bold capitalize">User Analytics</h1>
        <div
          className={`mb-8 h-[1px] ${themeMode === ThemeMode.Dark ? 'bg-white' : 'bg-gray-800'}`}
        />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <ActiveUsersChart />
          <div className="col-span-1 md:col-span-2 lg:col-span-1">
            <ActiveRegisteredUserByAge />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
