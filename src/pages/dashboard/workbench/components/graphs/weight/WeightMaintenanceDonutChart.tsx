import { useQuery } from '@tanstack/react-query';
import { Card, Select } from 'antd';
import { useState } from 'react';
import { <PERSON>, Legend, Pie, Pie<PERSON><PERSON>, ResponsiveContainer, Tooltip } from 'recharts';

import { GetWeightRecordSummary } from '@/api/services/dashboard/weightGraphService';
import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';
import { ThemeMode } from '#/enum';
import { useSettings } from '@/store/settingStore';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

function WeightMaintenanceDonutChart() {
  const [period, setPeriod] = useState<'7d' | '30d'>('7d');
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['weight-status-summary', period],
    queryFn: () => GetWeightRecordSummary(period),
  });

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Weight Maintenance</p>
          <Select
            value={period}
            onChange={(val: '7d' | '30d') => setPeriod(val)}
            style={{ width: 200 }}
          >
            <Select.Option value="7d">Last 7 Days</Select.Option>
            <Select.Option value="30d">Last 30 Days</Select.Option>
          </Select>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                {getAxiosErrorMessage(error, 'Error loading weight trends summary')}
              </div>
            ) : (
              data && (
                <ResponsiveContainer width="100%" height={'98%'}>
                  <PieChart margin={{ top: 20, right: 0, left: 0, bottom: 0 }}>
                    <Pie
                      data={[
                        { name: 'Maintaining', value: data?.maintaining },
                        { name: 'Gaining', value: data?.gaining },
                        { name: 'Losing', value: data?.losing },
                      ]}
                      dataKey="value"
                      nameKey="name"
                      fill="#8884d8"
                      innerRadius={'40%'}
                    >
                      <Cell key={`cell-gaining`} fill="#2196F3" />
                      <Cell key={`cell-losing`} fill="#FFA500" />
                      <Cell key={`cell-maintaining`} fill="#8884d8" />
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                        borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                        color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                      }}
                      itemStyle={{
                        color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                      }}
                      labelFormatter={(label: string) => `Users: ${label}`}
                    />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              )
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
}

export default WeightMaintenanceDonutChart;
