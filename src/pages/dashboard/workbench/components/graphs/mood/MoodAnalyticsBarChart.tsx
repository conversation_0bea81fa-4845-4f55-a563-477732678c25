import { useQuery } from '@tanstack/react-query';
import { Select, Card } from 'antd';
import React, { useState } from 'react';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from 'recharts';

import { GetMoodAnalyticsTrend } from '@/api/services/dashboard/mood/moodGraphService';
import { useSettings } from '@/store/settingStore';
import { ThemeMode } from '#/enum';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';
import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';

const { Option } = Select;

const MoodStackedBarChart: React.FC = () => {
  const [period, setPeriod] = useState<'weekly' | 'monthly'>('weekly');
  const { themeMode } = useSettings();

  // Fetch mood analytics data based on period
  const { data, error, isLoading } = useQuery({
    queryKey: ['mood-distribution', period],
    queryFn: () => GetMoodAnalyticsTrend(period),
  });

  // Prepare chart data
  const chartData =
    data?.data?.map((item: any) => ({
      name: period === 'weekly' ? item.period : item.period,
      happy: item.moodCounts.happy,
      moderatelyHappy: item.moodCounts['moderately happy'],
      irritated: item.moodCounts.irritated,
      sad: item.moodCounts.sad,
      anxious: item.moodCounts.anxious,
    })) || [];

  const handlePeriodChange = (value: 'weekly' | 'monthly') => {
    setPeriod(value);
  };

  const formatLabel = (label: string) => {
    if (label.includes('to')) {
      const [start, end] = label.split(' to ');
      const startDate = new Date(start);
      const endDate = new Date(end);
      const format = (d: Date) =>
        `${d.getDate()} ${d.toLocaleString('default', { month: 'short' })}`;
      return `${format(startDate)} - ${format(endDate)}`;
    } else {
      const date = new Date(label);
      return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
    }
  };

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Mood Trends</p>
          <Select value={period} onChange={handlePeriodChange} style={{ width: 200 }}>
            <Option value="weekly">Weekly</Option>
            <Option value="monthly">Monthly</Option>
          </Select>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                {getAxiosErrorMessage(error, 'Error loading mood trends')}
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={chartData}>
                  <XAxis dataKey="name" tickFormatter={formatLabel} />
                  <YAxis />
                  <Tooltip
                    // content={<CustomTooltip />}
                    cursor={{ fill: 'transparent' }}
                    wrapperStyle={
                      {
                        // backgroundColor: 'rgba(0,0,0,1)',
                      }
                    }
                    contentStyle={{
                      backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                      borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                      color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                    }}
                  />
                  <Legend />
                  <Bar dataKey="happy" stackId="a" fill="#82ca9d" name="Happy" />
                  <Bar
                    dataKey="moderatelyHappy"
                    stackId="a"
                    fill="#8884d8"
                    name="Moderately Happy"
                  />
                  <Bar dataKey="irritated" stackId="a" fill="#ff7300" name="Irritated" />
                  <Bar dataKey="sad" stackId="a" fill="#888888" name="Sad" />
                  <Bar dataKey="anxious" stackId="a" fill="#ffc658" name="Anxious" />
                </BarChart>
              </ResponsiveContainer>
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
};

export default MoodStackedBarChart;
