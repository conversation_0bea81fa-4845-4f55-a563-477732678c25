import { useQuery } from '@tanstack/react-query';
import { Card } from 'antd';
import { <PERSON>, Legend, <PERSON>, <PERSON><PERSON>, ResponsiveContainer, Tooltip } from 'recharts';

import { GetMoodAnalyticsTrend } from '@/api/services/dashboard/mood/moodGraphService';
import { useSettings } from '@/store/settingStore';
import { ThemeMode } from '#/enum';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

function MoodCurrentAnalyticsPieChart() {
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['mood-distribution', 'daily'],
    queryFn: () => GetMoodAnalyticsTrend('daily'),
  });

  // Transform and filter out 0-value moods
  const chartData = data?.data[0].moodPercentages;
  const hasData = chartData && Object.values(chartData).some((value) => value > 0);

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Today's Mood Analytics</p>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                Error: {error?.message || 'Error loading mood distribution data'}
              </div>
            ) : !hasData ? (
              <ResponsiveContainer width="100%" height="95%">
                <PieChart>
                  <Pie
                    data={[
                      {
                        name: 'Not logged',
                        value: 100,
                      },
                    ]}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius="80%"
                  >
                    <Cell
                      key="Not logged"
                      fill={themeMode == ThemeMode.Dark ? '#4b5563' : '#DFE3E8'}
                    />
                  </Pie>
                  <Legend
                    payload={[
                      {
                        value: 'Happy',
                        type: 'square',
                        color: '#FFD700',
                      },
                      {
                        value: 'Irritated',
                        type: 'square',
                        color: '#FF6347',
                      },
                      {
                        value: 'Sad',
                        type: 'square',
                        color: '#1E90FF',
                      },
                      {
                        value: 'Moderately happy',
                        type: 'square',
                        color: '#90EE90',
                      },
                      {
                        value: 'Anxious',
                        type: 'square',
                        color: '#FF69B4',
                      },
                    ]}
                  />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <ResponsiveContainer width="100%" height="95%">
                <PieChart>
                  <Pie
                    data={[
                      {
                        name: 'Happy',
                        value: chartData?.happy,
                      },
                      {
                        name: 'Moderately happy',
                        value: chartData?.['moderately happy'],
                      },
                      {
                        name: 'Sad',
                        value: chartData?.sad,
                      },
                      {
                        name: 'Irritated',
                        value: chartData?.irritated,
                      },
                      {
                        name: 'Anxious',
                        value: chartData?.anxious,
                      },
                    ]}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius="80%"
                    label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                  >
                    {<Cell key="happy" fill="#FFD700" />}
                    {<Cell key="irritated" fill="#FF6347" />}
                    {<Cell key="sad" fill="#1E90FF" />}
                    {<Cell key="moderately happy" fill="#90EE90" />}
                    {<Cell key="anxious" fill="#FF69B4" />}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1f2937',
                      borderColor: '#4b5563',
                      color: '#fff',
                    }}
                    itemStyle={{
                      color: '#fff',
                    }}
                    formatter={(value: number, name: string) => [
                      `${value}%`,
                      name.charAt(0).toUpperCase() + name.slice(1),
                    ]}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
}

export default MoodCurrentAnalyticsPieChart;
