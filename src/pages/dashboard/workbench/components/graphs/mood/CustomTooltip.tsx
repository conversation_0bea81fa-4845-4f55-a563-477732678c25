const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div
        style={{
          border: '1px solid #ccc',
          padding: 10,
          borderRadius: 4,
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
        }}
      >
        <p style={{ margin: 0, fontWeight: 'bold' }}>{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={`item-${index}`} style={{ color: entry.color, marginTop: 4 }}>
            {entry.name}: {entry.value}
          </div>
        ))}
      </div>
    );
  }

  return null;
};
export default CustomTooltip;
