import { useQuery } from '@tanstack/react-query';
import { Card, Select } from 'antd';
import { useState } from 'react';
import { <PERSON>, Legend, Pie, PieC<PERSON>, ResponsiveContainer, Tooltip } from 'recharts';

import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';
import { GetPopularActivities } from '@/api/services/dashboard/activityGraphService';
import { ThemeMode } from '#/enum';
import { useSettings } from '@/store/settingStore';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

const COLORS = ['#2196F3', '#FF6B6B', '#4ECDC4', '#FFD93D', '#6C5CE7', '#ff8bf1'];

function MostPlayedActivityDonutChart() {
  const [period, setPeriod] = useState<'weekly' | 'monthly'>('weekly');
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['popular-activity-graph', period],
    queryFn: () => GetPopularActivities(period),
  });

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Most Popular Activities</p>
          <Select
            value={period}
            onChange={(val: 'weekly' | 'monthly') => setPeriod(val)}
            style={{ width: 200 }}
          >
            <Select.Option value="weekly">Weekly</Select.Option>
            <Select.Option value="monthly">Monthly</Select.Option>
          </Select>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                {getAxiosErrorMessage(error, 'Error loading popular activities data')}
              </div>
            ) : !data || data.length == 0 ? (
              <ResponsiveContainer width="100%" height="95%">
                <PieChart>
                  <Pie
                    data={[
                      {
                        name: 'No Data',
                        value: 100,
                      },
                    ]}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius="80%"
                    innerRadius={'40%'}
                  >
                    <Cell
                      key="No Data"
                      fill={themeMode == ThemeMode.Dark ? '#4b5563' : '#DFE3E8'}
                    />
                  </Pie>
                  <Legend
                    payload={[
                      {
                        value: 'No Data',
                        type: 'square',
                        color: themeMode == ThemeMode.Dark ? '#4b5563' : '#DFE3E8',
                      },
                    ]}
                  />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <ResponsiveContainer width="100%" height={'98%'}>
                <PieChart margin={{ top: 20, right: 0, left: 0, bottom: 0 }}>
                  <Pie
                    data={data.map((item) => ({
                      name: item.activityName,
                      value: Number(
                        Number(
                          (item.count / data?.reduce((acc, curr) => acc + curr?.count, 0)) * 100,
                        ).toFixed(2),
                      ),
                    }))}
                    dataKey="value"
                    nameKey="name"
                    fill="#8884d8"
                    innerRadius={'40%'}
                  >
                    {data.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                      borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                      color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                    }}
                    itemStyle={{
                      color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                    }}
                    formatter={(value: number, name: string) => [
                      `${value}%`,
                      name.charAt(0).toUpperCase() + name.slice(1),
                    ]}
                    // labelFormatter={(label: string) => `Users: ${'100%'}`}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
}

export default MostPlayedActivityDonutChart;
