import { useQuery } from '@tanstack/react-query';
import { Card, Select } from 'antd';
import { useState } from 'react';
import { Legend, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';
import { GetAverageDeviceUsage } from '@/api/services/dashboard/deviceGraphService';
import { ThemeMode } from '#/enum';
import { useSettings } from '@/store/settingStore';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

const formatMarker = (value: number) => {
  if (value === 0) return '0sec';

  const hours = Math.floor(value / 3600);
  const minutes = Math.floor((value % 3600) / 60);
  const seconds = Number((value % 60).toFixed(0));

  if (hours > 0) {
    return `${hours}hr${minutes > 0 ? ` ${minutes}min` : ''}`;
  } else if (minutes > 0) {
    return `${minutes}min${seconds > 0 ? ` ${seconds}sec` : ''}`;
  } else {
    return `${seconds}sec`;
  }
};

const formatLabel = (label: string) => {
  if (label.includes('to')) {
    const [start, end] = label.split(' to ');
    const startDate = new Date(start);
    const endDate = new Date(end);
    const format = (d: Date) => `${d.getDate()} ${d.toLocaleString('default', { month: 'short' })}`;
    return `${format(startDate)} - ${format(endDate)}`;
  } else {
    const date = new Date(label);
    return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
  }
};

const formatYAxisTick = (value: number) => {
  const hours = Math.floor(value / 3600);
  const minutes = Math.floor((value % 3600) / 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m`;
  } else {
    return `${value}s`;
  }
};

function AverageDeviceUsageLineGraph() {
  const [period, setPeriod] = useState<'weekly' | 'monthly'>('weekly');
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['device-average-usage-trends', period],
    queryFn: () => GetAverageDeviceUsage(period),
  });

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Average Device Usage</p>
          <Select
            value={period}
            onChange={(val: 'weekly' | 'monthly') => setPeriod(val)}
            style={{ width: 200 }}
          >
            <Select.Option value="weekly">Weekly</Select.Option>
            <Select.Option value="monthly">Monthly</Select.Option>
          </Select>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                {getAxiosErrorMessage(error, 'Error loading sleep trends data')}
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={'98%'}>
                <LineChart
                  data={data?.map((item) => ({
                    ...item,
                    averageDeviceUsage: item.averageDeviceUsage,
                  }))}
                  margin={{ top: 20, right: 0, left: -20, bottom: 0 }}
                >
                  <XAxis
                    dataKey="label"
                    tickFormatter={formatLabel}
                    tick={{ fontSize: 12, fill: '#9ca3af' }}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: '#9ca3af' }}
                    tickFormatter={formatYAxisTick}
                    domain={[0, (dataMax: number) => dataMax + 60]}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                      borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                      color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                    }}
                    labelFormatter={(label: string) => `Period: ${formatLabel(label)}`}
                    formatter={(value: any) => [formatMarker(value * 1), 'Average Device Usage']}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="averageDeviceUsage"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    name="Average Device Usage"
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
}

export default AverageDeviceUsageLineGraph;
