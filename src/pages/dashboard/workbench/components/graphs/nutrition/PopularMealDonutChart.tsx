import { useQuery } from '@tanstack/react-query';
import { Card, Select } from 'antd';
import { useState } from 'react';
import { <PERSON>, Legend, Pie, PieChart, ResponsiveContainer, Tooltip } from 'recharts';

import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';
import { GetPopularMeals } from '@/api/services/dashboard/nutritionGraphService';
import { useSettings } from '@/store/settingStore';
import { ThemeMode } from '#/enum';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#AF19FF'];

function PopularMealDonutChart() {
  const [period, setPeriod] = useState<'weekly' | 'monthly'>('weekly');
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['popular-meals-graph', period],
    queryFn: () => GetPopularMeals(period),
  });

  const chartData = data?.map((meal) => ({
    name: meal.recipeName,
    value: (meal.count / data.reduce((acc, curr) => acc + curr.count, 0)) * 100,
  }));

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Most Popular Meal</p>
          <Select
            value={period}
            onChange={(val: 'weekly' | 'monthly') => setPeriod(val)}
            style={{ width: 200 }}
          >
            <Select.Option value="weekly">Weekly</Select.Option>
            <Select.Option value="monthly">Monthly</Select.Option>
          </Select>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                {getAxiosErrorMessage(error, 'Error loading popular meals data')}
              </div>
            ) : !chartData || chartData.length === 0 ? (
              <ResponsiveContainer width="100%" height="95%">
                <PieChart>
                  <Pie
                    data={[
                      {
                        name: 'No Data',
                        value: 100,
                      },
                    ]}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius="80%"
                    innerRadius={'40%'}
                  >
                    <Cell
                      key="No Data"
                      fill={themeMode == ThemeMode.Dark ? '#4b5563' : '#DFE3E8'}
                    />
                  </Pie>
                  <Legend
                    payload={[
                      {
                        value: 'No Data',
                        type: 'square',
                        color: themeMode == ThemeMode.Dark ? '#4b5563' : '#DFE3E8',
                      },
                    ]}
                  />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <ResponsiveContainer width="100%" height={'98%'}>
                <PieChart margin={{ top: 20, right: 0, left: 0, bottom: 0 }}>
                  <Pie
                    data={chartData}
                    dataKey="value"
                    nameKey="name"
                    fill="#8884d8"
                    innerRadius={'40%'}
                  >
                    {chartData.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                      borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                      color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                    }}
                    itemStyle={{
                      color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                    }}
                    formatter={(value: number, name: string) => [
                      `${Number(value.toFixed(2))}%`,
                      name.charAt(0).toUpperCase() + name.slice(1),
                    ]}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
}

export default PopularMealDonutChart;
