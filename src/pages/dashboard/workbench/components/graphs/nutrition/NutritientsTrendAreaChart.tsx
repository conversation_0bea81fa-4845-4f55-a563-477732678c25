import { useQuery } from '@tanstack/react-query';
import { Card, Select } from 'antd';
import { useState } from 'react';
import { Area, AreaChart, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';
import { NutrientsTrends } from '@/api/services/dashboard/nutritionGraphService';
import { ThemeMode } from '#/enum';
import { useSettings } from '@/store/settingStore';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

const CustomTooltip = ({ active, payload, label, theme, data }: any) => {
  if (active && payload && payload.length) {
    return (
      <div
        style={{
          border: '1px solid #ccc',
          padding: 10,
          borderRadius: 4,
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          backgroundColor: ThemeMode.Dark == theme ? '#1f2937' : '#fff',
        }}
      >
        <p
          style={{
            margin: 0,
            fontWeight: 'bold',
            color: theme == ThemeMode.Dark ? '#fff' : '#000',
          }}
        >
          {formatLabel(label)}
        </p>
        <div style={{ color: '#FFA500' }}>
          Calories: {`${data.find((entry: any) => entry.period === label)?.calories} cal`}
        </div>
        {payload
          .filter((entry: any) => entry.name != 'Calories')
          .map((entry: any, index: number) => (
            <div key={`item-${index}`} style={{ color: entry.color, marginTop: 4 }}>
              {entry.name}: {entry.value} %
            </div>
          ))}
      </div>
    );
  }

  return null;
};

const formatLabel = (label: string) => {
  if (label.includes('to')) {
    const [start, end] = label.split(' to ');
    const startDate = new Date(start);
    const endDate = new Date(end);
    const format = (d: Date) => `${d.getDate()} ${d.toLocaleString('default', { month: 'short' })}`;
    return `${format(startDate)} - ${format(endDate)}`;
  } else {
    const date = new Date(label);
    return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
  }
};

function NutrientTrendAreaChart() {
  const [period, setPeriod] = useState<'weekly' | 'monthly'>('weekly');
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['nutrients-trends', period],
    queryFn: () => NutrientsTrends(period),
  });

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Nutrient trend</p>
          <Select
            value={period}
            onChange={(val: 'weekly' | 'monthly') => setPeriod(val)}
            style={{ width: 200 }}
          >
            <Select.Option value="weekly">Weekly</Select.Option>
            <Select.Option value="monthly">Monthly</Select.Option>
          </Select>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                {getAxiosErrorMessage(error, 'Error loading popular meals data')}
              </div>
            ) : (
              data && (
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={data.data.map((item) => ({
                      name: item.period,
                      carbs: item.carbs,
                      protein: item.protein,
                      fiber: item.fiber,
                      fats: item.fats,
                    }))}
                    margin={{
                      top: 10,
                      right: 30,
                      left: 0,
                      bottom: 0,
                    }}
                  >
                    <XAxis dataKey="name" tickFormatter={formatLabel} />
                    <YAxis tickFormatter={(value) => `${value}%`} />
                    <Tooltip
                      content={<CustomTooltip theme={themeMode} data={data.data} />}
                      contentStyle={{
                        backgroundColor: '#1f2937',
                        borderColor: '#4b5563',
                        color: '#fff',
                      }}
                      labelFormatter={(label: string) => `Period: ${formatLabel(label)}`}
                      formatter={(value, name) => [`${value}cal`, name]}
                    />
                    <Area
                      type="monotone"
                      dataKey="carbs"
                      stackId="1"
                      stroke="#8884d8"
                      fill="#8884d8"
                      name="Carbs"
                    />
                    <Area
                      type="monotone"
                      dataKey="protein"
                      stackId="1"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      name="Protein"
                    />
                    <Area
                      type="monotone"
                      dataKey="fats"
                      stackId="1"
                      stroke="#4b5563"
                      fill="#4b5563"
                      name="Fats"
                    />
                    <Area
                      type="monotone"
                      dataKey="fiber"
                      stackId="1"
                      stroke="#ffc658"
                      fill="#ffc658"
                      name="Fiber"
                    />
                    <Legend />
                  </AreaChart>
                </ResponsiveContainer>
              )
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
}

export default NutrientTrendAreaChart;
