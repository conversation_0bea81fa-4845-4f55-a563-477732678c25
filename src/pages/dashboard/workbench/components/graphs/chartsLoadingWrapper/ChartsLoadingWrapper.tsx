import CustomLoader from '@/pages/components/loaders/CustomLoader';
import React from 'react';

interface ChartsLoadingWrapperProps {
  loading: boolean;
  children: React.ReactNode;
}

function ChartsLoadingWrapper({ loading, children }: ChartsLoadingWrapperProps) {
  if (loading)
    return (
      <div className="flex h-full items-center justify-center">
        <CustomLoader className="h-20 w-20" />
      </div>
    );

  return children;
}

export default ChartsLoadingWrapper;
