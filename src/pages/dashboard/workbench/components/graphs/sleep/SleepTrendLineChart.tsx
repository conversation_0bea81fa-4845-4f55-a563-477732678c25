import { useQuery } from '@tanstack/react-query';
import { Card, Select } from 'antd';
import { useState } from 'react';
import { Legend, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

import { GetUserSleepTrends } from '@/api/services/dashboard/sleepGraphService';
import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';
import { ThemeMode } from '#/enum';
import { useSettings } from '@/store/settingStore';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

const formatLabel = (label: string) => {
  if (label.includes('to')) {
    const [start, end] = label.split(' to ');
    const startDate = new Date(start);
    const endDate = new Date(end);
    const format = (d: Date) => `${d.getDate()} ${d.toLocaleString('default', { month: 'short' })}`;
    return `${format(startDate)} - ${format(endDate)}`;
  } else {
    const date = new Date(label);
    return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
  }
};

function SleepTrendLineChart() {
  const [period, setPeriod] = useState<'weekly' | 'monthly'>('weekly');
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['sleep-trends', period],
    queryFn: () => GetUserSleepTrends(period),
  });

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">Average User Sleep</p>
          <Select
            value={period}
            onChange={(val: 'weekly' | 'monthly') => setPeriod(val)}
            style={{ width: 200 }}
          >
            <Select.Option value="weekly">Weekly</Select.Option>
            <Select.Option value="monthly">Monthly</Select.Option>
          </Select>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                {getAxiosErrorMessage(error, 'Error loading sleep trends data')}
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={'98%'}>
                <LineChart
                  data={data?.map((item) => ({
                    ...item,
                    averageSleep: item.averageSleep.toFixed(1),
                  }))}
                  margin={{ top: 20, right: 10, left: -20, bottom: 0 }}
                >
                  <XAxis
                    dataKey="label"
                    tickFormatter={formatLabel}
                    tick={{ fontSize: 12, fill: '#9ca3af' }}
                  />
                  <YAxis tick={{ fontSize: 12, fill: '#9ca3af' }} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                      borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                      color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                    }}
                    itemStyle={{
                      color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                    }}
                    labelFormatter={(label: string) => `Period: ${formatLabel(label)}`}
                    formatter={(value: any) => [`${value} hr`, 'Average Sleep']}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="averageSleep"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    name="Average Sleep"
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
}

export default SleepTrendLineChart;
