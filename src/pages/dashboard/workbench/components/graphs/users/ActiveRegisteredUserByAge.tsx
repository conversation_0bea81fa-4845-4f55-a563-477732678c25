import { useQuery } from '@tanstack/react-query';
import { Select, Card } from 'antd';
import { useState } from 'react';

import { <PERSON>, Legend, Pie, <PERSON><PERSON><PERSON>, ResponsiveContainer, Tooltip } from 'recharts';
import { ThemeMode } from '#/enum';
import getAxiosErrorMessage from '@/utils/getAxiosErrorMessage';
import { useSettings } from '@/store/settingStore';
import { GetUserAgeAnalytics } from '@/api/services/dashboard/activeNRegisteredUsersByAge';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';

const { Option } = Select;
const COLORS = ['#2196F3', '#FF6B6B', '#4ECDC4', '#FFD93D', '#6C5CE7'];

export const ActiveRegisteredUserByAge = () => {
  const [period, setPeriod] = useState<'24h' | '7d' | '30d'>('24h');
  const [dataType, setDataType] = useState<'activeUsers' | 'registeredUsers'>('activeUsers');
  const { themeMode } = useSettings();

  const { data, isLoading, error } = useQuery({
    queryKey: ['user-age-analytics', period, dataType],
    queryFn: () => GetUserAgeAnalytics(period, dataType),
  });

  return (
    <Card>
      <div className="flex aspect-square h-full flex-col">
        <div className="mb-4 flex items-center justify-between">
          <div className="text-lg font-semibold">
            {dataType === 'activeUsers' ? 'Active' : 'Registered'} Users by Age Group
          </div>
          <div>
            <div className="flex gap-4">
              <Select value={period} onChange={(val) => setPeriod(val)} style={{ width: 150 }}>
                <Option value="24h">Last 24 Hours</Option>
                <Option value="7d">Last 7 Days</Option>
                <Option value="30d">Last 30 Days</Option>
              </Select>
              <Select value={dataType} onChange={(val) => setDataType(val)} style={{ width: 180 }}>
                <Option value="activeUsers">Active Users</Option>
                <Option value="registeredUsers">Registered Users</Option>
              </Select>
            </div>
          </div>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={isLoading}>
            {error ? (
              <div className="flex h-full items-center justify-center">
                {getAxiosErrorMessage(error, 'Error loading popular activities data')}
              </div>
            ) : !data || data.reduce((acc, curr) => acc + curr?.value, 0) === 0 ? (
              <ResponsiveContainer width="100%" height="95%">
                <PieChart>
                  <Pie
                    data={[
                      {
                        name: 'No Data',
                        value: 100,
                      },
                    ]}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    innerRadius={'40%'}
                  >
                    <Cell
                      key="Not Data"
                      fill={themeMode == ThemeMode.Dark ? '#4b5563' : '#DFE3E8'}
                    />
                  </Pie>
                  <Legend
                    payload={[
                      {
                        value: 'Child',
                        type: 'square',
                        color: '#2196F3',
                      },
                      {
                        value: 'Teen',
                        type: 'square',
                        color: '#FF6B6B',
                      },
                      {
                        value: 'Young Adult',
                        type: 'square',
                        color: '#4ECDC4',
                      },
                      {
                        value: 'Adult',
                        type: 'square',
                        color: '#FFD93D',
                      },
                      {
                        value: 'Senior',
                        type: 'square',
                        color: '#6C5CE7',
                      },
                    ]}
                  />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              data && (
                <ResponsiveContainer width="100%" height={'98%'}>
                  <PieChart margin={{ top: 20, right: 0, left: 0, bottom: 0 }}>
                    <Pie
                      data={data.map((item) => ({
                        name: item.name,
                        value: Number(
                          Number(
                            (item.value / data?.reduce((acc, curr) => acc + curr?.value, 0)) * 100,
                          ).toFixed(2),
                        ),
                      }))}
                      dataKey="value"
                      nameKey="name"
                      fill="#8884d8"
                      innerRadius={'40%'}
                    >
                      {data.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                        borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                        color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                      }}
                      itemStyle={{
                        color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                      }}
                      formatter={(value: number, name: string) => [
                        `${value}%`,
                        name.charAt(0).toUpperCase() + name.slice(1),
                      ]}
                      // labelFormatter={(label: string) => `Users: ${'100%'}`}
                    />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              )
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
};
