import { Card, Select } from 'antd';
import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YA<PERSON><PERSON>, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';

import { useSettings } from '@/store/settingStore';
import { ThemeMode } from '#/enum';
import ChartsLoadingWrapper from '../chartsLoadingWrapper/ChartsLoadingWrapper';
import apiClient from '@/api/services/axios/axiosInstance';

const formatLabel = (label: string) => {
  if (label.includes('to')) {
    const [start, end] = label.split(' to ');
    const startDate = new Date(start);
    const endDate = new Date(end);
    const format = (d: Date) => `${d.getDate()} ${d.toLocaleString('default', { month: 'short' })}`;
    return `${format(startDate)} - ${format(endDate)}`;
  } else {
    const date = new Date(label);
    return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
  }
};

const LineGraph = () => {
  const [period, setPeriod] = useState<'weekly' | 'monthly'>('weekly');
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const { themeMode } = useSettings();

  const fetchData = async () => {
    setLoading(true);
    setErrorMsg('');

    try {
      const response = await apiClient.get(`/admin/analytics/user_engagement_trends/${period}`);

      if (response.data?.error === false) {
        setData(response.data.data);
      } else {
        setErrorMsg('Failed to fetch analytics data.');
      }
    } catch {
      setErrorMsg('An error occurred while fetching data.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [period]);

  return (
    <Card>
      <div className="flex aspect-square flex-col">
        <div className="mb-4 flex items-center justify-between">
          <p className="text-lg font-semibold">User Engagement Trends</p>
          <Select
            value={period}
            onChange={(val: 'weekly' | 'monthly') => setPeriod(val)}
            style={{ width: 200 }}
            // dropdownStyle={{ backgroundColor: '#1f2937', color: '#fff' }} // Tailwind dark gray-800
            className="dark:[&_.ant-select-selector]:!bg-neutral-800 dark:[&_.ant-select-selector]:!border-neutral-700 dark:[&_.ant-select-selector]:!text-white"
          >
            <Select.Option value="weekly">Weekly</Select.Option>
            <Select.Option value="monthly">Monthly</Select.Option>
          </Select>
        </div>
        <div className="grow">
          <ChartsLoadingWrapper loading={loading}>
            {errorMsg ? (
              <p className="text-red-500 dark:text-red-400 text-sm">{errorMsg}</p>
            ) : (
              <ResponsiveContainer width="100%" height={'98%'}>
                <LineChart data={data} margin={{ top: 20, right: 10, left: -20, bottom: 0 }}>
                  {/* <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" /> */}
                  <XAxis
                    dataKey="label"
                    tickFormatter={formatLabel}
                    tick={{ fontSize: 12, fill: '#9ca3af' }} // gray-400
                  />
                  <YAxis tick={{ fontSize: 12, fill: '#9ca3af' }} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: themeMode == ThemeMode.Dark ? '#1f2937' : '#fff',
                      borderColor: themeMode == ThemeMode.Dark ? '#4b5563' : '#ccc',
                      color: themeMode == ThemeMode.Dark ? '#fff' : '#000',
                    }}
                    labelFormatter={(label: string) => `Date: ${formatLabel(label)}`}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="activeUsers"
                    stroke="#6366f1"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    name="Active Users"
                  />
                  <Line
                    type="monotone"
                    dataKey="registeredUsers"
                    stroke="#10b981"
                    strokeWidth={2}
                    dot={{ r: 3 }}
                    name="Registered Users"
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </ChartsLoadingWrapper>
        </div>
      </div>
    </Card>
  );
};

export default LineGraph;
