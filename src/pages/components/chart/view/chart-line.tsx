import Chart from '@/components/chart/chart';
import useChart from '@/components/chart/useChart';

const series = [
  {
    name: 'Credit Points',
    data: [10, 41, 35, 51, 49, 62, 69],
  },
];

export default function ChartLine() {
  const chartOptions = useChart({
    xaxis: {
      categories: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
    },
    tooltip: {
      x: {
        show: true,
      },
      marker: { show: true },
    },
  });

  return <Chart type="line" series={series} options={chartOptions} height={300} />;
}
