import { Layout } from 'antd';

import { Navigate } from 'react-router-dom';

import Nouriq_logo from 'public/images/app_logo.png';
import { useUserToken } from '@/store/userStore';

import LoginForm from './LoginForm';
import { LoginStateProvider } from './providers/LoginStateProvider';

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

function Login() {
  // const { t } = useTranslation();
  const token = useUserToken();

  // Check if the user has permission
  if (token.accessToken) {
    // If authorized, redirect to the homepage
    return <Navigate to={HOMEPAGE} replace />;
  }

  return (
    <Layout className="relative flex !min-h-screen !w-full !flex-row">
      <div className="3xl:max-w-[35%] 3xl:px-24 mx-auto flex h-screen w-full flex-col justify-center px-4 sm:px-6 md:max-w-[60%] md:px-8 lg:max-w-[45%] lg:px-12 xl:max-w-[40%] xl:px-16 2xl:max-w-[35%] 2xl:px-20">
        <LoginStateProvider>
          <LoginForm />
        </LoginStateProvider>
      </div>

      <div className="hidden grow flex-col items-center justify-center gap-[2px] bg-primary bg-center bg-no-repeat md:flex">
        <img
          className="w-[180px] object-contain px-4 sm:w-[220px] md:w-[300px] lg:w-[400px] xl:w-[500px] 2xl:w-[600px]"
          src={Nouriq_logo}
          alt="logo"
        />
      </div>
    </Layout>
  );
}
export default Login;
