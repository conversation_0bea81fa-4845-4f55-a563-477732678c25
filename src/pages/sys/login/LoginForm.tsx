import { Button, Form, Input } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useLogIn } from '@/api/ApiHooks/auth-hooks';

import { LoginStateEnum, useLoginStateContext } from './providers/LoginStateProvider';

function LoginForm() {
  const { t } = useTranslation();
  // const themeToken = useThemeToken();
  const [loading, setLoading] = useState(false);

  const { loginState } = useLoginStateContext();
  const logIn = useLogIn();

  if (loginState !== LoginStateEnum.LOGIN) return null;

  const handleFinish = async (values: any) => {
    setLoading(true);
    try {
      await logIn({
        email: values.user_email,
        password: values.user_password,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="mb-4 text-2xl font-bold xl:text-3xl">{t('sys.login.signInFormTitle')}</div>
      <Form
        name="login"
        size="large"
        initialValues={{
          remember: true,
        }}
        onFinish={handleFinish}
        autoComplete="off"
      >
        {/* Dummy fields to trick browser autofill */}
        <input type="text" name="email" style={{ display: 'none' }} autoComplete="off" />
        <input
          type="password"
          name="password"
          style={{ display: 'none' }}
          autoComplete="new-password"
        />

        <Form.Item name="user_email" rules={[{ required: true, message: 'Please Input Email' }]}>
          <Input placeholder="Email" autoComplete="off" />
        </Form.Item>
        <Form.Item
          name="user_password"
          rules={[{ required: true, message: t('sys.login.passwordPlaceholder') }]}
        >
          <Input.Password
            type="password"
            placeholder={t('sys.login.password')}
            autoComplete="new-password"
          />
        </Form.Item>

        {/* <Form.Item>
          <Row align="middle">
            <Col span={12}>
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>{t('sys.login.rememberMe')}</Checkbox>
              </Form.Item>
            </Col>
          </Row>
        </Form.Item> */}

        <Form.Item>
          <Button type="primary" htmlType="submit" className="w-full" loading={loading}>
            {t('sys.login.loginButton')}
          </Button>
        </Form.Item>

        {/* <Divider className="!text-xs">{t('sys.login.otherSignIn')}</Divider>

        <div className="flex cursor-pointer justify-around text-2xl">
          <AiFillGoogleCircle />
        </div> */}
      </Form>
    </>
  );
}

export default LoginForm;
