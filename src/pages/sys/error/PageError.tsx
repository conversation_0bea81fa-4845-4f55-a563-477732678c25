import { Typography } from 'antd';
import { m } from 'framer-motion';
import { Helmet } from 'react-helmet-async';

import MotionContainer from '@/components/animate/motion-container';
import { varBounce } from '@/components/animate/variants/bounce';
import { useRouter } from '@/router/hooks';
import { useThemeToken } from '@/theme/hooks';

import type { FallbackProps } from 'react-error-boundary';

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

export default function PageError({ error, resetErrorBoundary }: FallbackProps) {
  const { colorBgBase, colorTextBase } = useThemeToken();

  const { replace } = useRouter();

  const goHome = () => {
    resetErrorBoundary();
    replace(HOMEPAGE);
  };
  return (
    <div>
      <Helmet>
        <title>Sorry, Page error occurred!</title>
      </Helmet>

      <div className="m-auto flex h-screen max-w-[400px] items-center justify-center">
        <MotionContainer className="flex flex-col items-center justify-center px-2">
          <m.div variants={varBounce().in}>
            <Typography.Title level={3} className="text-center">
              Sorry, Page error occurred!
            </Typography.Title>
          </m.div>

          <m.div variants={varBounce().in}>
            <Typography.Paragraph type="secondary" className="text-center">
              {error.toString()}
            </Typography.Paragraph>
          </m.div>

          <button
            style={{ background: colorTextBase, color: colorBgBase }}
            className="rounded-md p-4"
            onClick={goHome}
          >
            Go to Home
          </button>
        </MotionContainer>
      </div>
    </div>
  );
}
