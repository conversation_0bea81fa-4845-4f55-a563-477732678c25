import { Typography } from 'antd';
import { m } from 'framer-motion';
import { Helmet } from 'react-helmet-async';
import { NavLink } from 'react-router-dom';

import MotionContainer from '@/components/animate/motion-container';
import { varBounce } from '@/components/animate/variants/bounce';
import { useThemeToken } from '@/theme/hooks';

const { VITE_APP_HOMEPAGE: HOMEPAGE } = import.meta.env;

export default function Page404() {
  const { colorBgBase, colorTextBase } = useThemeToken();
  return (
    <>
      <Helmet>
        <title> 404 Page Not Found!</title>
      </Helmet>

      <div className="m-auto max-w-[400px]">
        <MotionContainer className="flex flex-col items-center justify-center px-2">
          <m.div variants={varBounce().in}>
            <Typography.Title level={3} className="text-center">
              Sorry, Page Not Found!
            </Typography.Title>
          </m.div>

          <m.div variants={varBounce().in}>
            <Typography.Paragraph type="secondary" className="text-center">
              Sorry, we couldn’t find the page you’re looking for. Perhaps you’ve mistyped the URL?
              Be sure to check your spelling.
            </Typography.Paragraph>
          </m.div>

          <NavLink
            to={HOMEPAGE}
            style={{ background: colorTextBase, color: colorBgBase }}
            className="rounded-md p-4"
          >
            Go to Home
          </NavLink>
        </MotionContainer>
      </div>
    </>
  );
}
