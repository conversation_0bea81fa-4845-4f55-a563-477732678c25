
export const convertDOBtoAge = (dob:string) => {
    if(!dob) return "";
    
    const today = new Date();
    const birthDate = new Date(dob.split('-').reverse().join('-'));
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();
    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    return age;
}