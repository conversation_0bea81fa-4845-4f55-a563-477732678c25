import * as Location from 'expo-location';
import { ResponseSuccessType } from '../types/ResponseSuccessType';
import { ResponseErrorType } from '../types/ResponseErrorType';
import { createErrorResponse } from '../apis/axios/axiosInstance';

export async function getUserLocation():Promise<ResponseSuccessType<Location.LocationObject> | ResponseErrorType> {
  try {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      throw new Error('Location permission not granted');
    }
    const location = await Location.getCurrentPositionAsync({});

    return {
        success:true,
        data:location
    };
    
  } catch (error:any) {
    return {
        success:false,
        error:createErrorResponse(error)
    }
  }
}

export async function getAddressFromCoordinates(latitude:number,longitude:number):Promise<ResponseSuccessType<Location.LocationGeocodedAddress> | ResponseErrorType> {
  try {
    const address = await Location.reverseGeocodeAsync({
      latitude,
      longitude
    });

    return {
        success:true,
        data:address[0]
    };
    
  } catch (error:any) {
    return {
        success:false,
        error:createErrorResponse(error)
    }
  }
}