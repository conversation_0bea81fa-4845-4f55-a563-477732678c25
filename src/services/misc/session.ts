// import * as SecureStore from 'expo-secure-store';
// import { USER_STORAGE_KEY } from '../../store/constants/store_keys';

// export const restoreSession = async () => {
//   const sessionData = await SecureStore.getItemAsync(USER_STORAGE_KEY);

//   if (!sessionData) {
//     throw new Error('Session not found');
//   }
//   const {accessToken, ...rest} = JSON.parse(sessionData);

//   const jwt: any = await decode(
//     accessToken, // the token
//     '', // the secret part
//     {
//       skipValidation: true,
//     },
//   );

//   // check if previous JWT token expired
//   if (Date.now() >= jwt.payload.exp * 1000) {
//     const freshSessionData = await getRefreshToken({
//       refreshToken: rest.refreshToken,
//     });

//     const newSessionData = JSON.stringify(freshSessionData.data);

//     await SecureStore.setItemAsync(USER_STORAGE_KEY, newSessionData);
//   }
// };