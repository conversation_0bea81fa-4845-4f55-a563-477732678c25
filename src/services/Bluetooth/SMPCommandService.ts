import { <PERSON>uff<PERSON> } from 'buffer';
import { DISPENSE_COMMAND_ID, FREQUENCY_COMMAND_ID, GET_LIVE_DATA_COMMAND_ID, SYNC_RTC_COMMAND_ID } from '../../constants/device/DeviceCommandIDS';

export class SNBCommandService {

  // Convert timestamp to little-endian byte array (4 bytes for uint32)
  static timestampToLittleEndian(timestamp: number) {
    const buffer = new ArrayBuffer(4); // 4 bytes instead of 8
    const view = new DataView(buffer);

    view.setUint32(0, timestamp, true); // little-endian 32-bit

    return new Uint8Array(buffer);
  }

  // Convert Uint8Array to base64 string for BLE transmission
  static uint8ArrayToBase64(uint8Array: Uint8Array) {
    let binary = '';
    uint8Array.forEach(byte => {
      binary += String.fromCharCode(byte);
    });
    return btoa(binary);
  }

  // Parse response from base64 to bytes
  static parseResponseFromBase64(base64Response: string): Uint8Array {
    try {
      const buffer = Buffer.from(base64Response, "base64");
      const bytes = new Uint8Array(buffer);
      return bytes; // Return Uint8Array directly
    } catch (error) {
      console.error("Error parsing response:", error);
      return new Uint8Array(0);
    }
  }

  static parseResponseFromBase64ToJSON(base64Value: string) {
    try {
      // Convert base64 → Buffer
      const buffer = Buffer.from(base64Value, "base64");

      // Decode buffer → UTF-8 string
      const decodedText = buffer.toString("utf-8");

      // Parse JSON
      return JSON.parse(decodedText);
    } catch (error) {
      return null;
    }
  }

  // Parse dispense response from base64 encoded JSON
  static parseDispenseResponseFromBase64(base64Value: string) {
    try {
      // Convert base64 → Buffer
      const buffer = Buffer.from(base64Value, "base64");

      // Decode buffer → UTF-8 string
      const decodedText = buffer.toString("utf-8");

      // Parse JSON
      const parsedData = JSON.parse(decodedText);
      
      // Return the parsed data with consistent field names
      return {
        timestamp: parsedData.timestamp,
        sequence: parsedData.sequence,
        water_qty: parsedData.water_qt || parsedData.water_qty, // Handle both field names
        cartridge_id: parsedData.cartridge_id || '00000000',
        // catridge_id_2: parsedData.catridge_id_2 || '00000000',
        battery_percentage: parsedData.battery_percentage || '0'
      };
    } catch (error) {
      console.error("Error parsing dispense response:", error);
      return null;
    }
  }

  // Build time sync command (Command ID 0x03)
  static buildTimeSyncCommand() {
    const currentTimestamp = Math.floor(Date.now()/1000);
    console.log('Building time sync command with timestamp:', currentTimestamp);

    const timestampBytes = this.timestampToLittleEndian(currentTimestamp);

    // SNB Command Format: SOF_L | SOF_H | CMD | LEN | DATA | RW | EOF_L | EOF_H
    const command = new Uint8Array([
      0xDE, 0xAD,           // SOF_L, SOF_H (Start of Frame)
      SYNC_RTC_COMMAND_ID,                 // CMD (Set RTC timestamp)
      0x04,                 // LEN (4 bytes for uint32 timestamp)
      ...timestampBytes,    // DATA (timestamp in little-endian)
      0x01,                 // RW (Write operation)
      0xFE, 0xED           // EOF_L, EOF_H (End of Frame)
    ]);

    console.log('Command bytes:', Array.from(command).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
    return command;
  }

  // Build get data frequency command (Command ID 0x01) - Read
  static buildGetDataFrequencyCommand() {
    const command = new Uint8Array([
      0xDE, 0xAD,           // SOF
      FREQUENCY_COMMAND_ID,                 // CMD (Get data frequency)
      0x00,                 // LEN (no data)
      0x00,                 // RW (Read operation)
      0xFE, 0xED           // EOF
    ]);
    return command;
  }

  // Build set data frequency command (Command ID 0x01) - Write
  static buildSetDataFrequencyCommand(intervalSeconds: number) {
    const command = new Uint8Array([
      0xDE, 0xAD,           // SOF
      FREQUENCY_COMMAND_ID,                 // CMD (Set data frequency)
      0x01,                 // LEN (1 byte for interval)
      intervalSeconds,      // DATA (interval in seconds)
      0x01,                 // RW (Write operation)
      0xFE, 0xED           // EOF
    ]);
    return command;
  }

  // Build dispense nutrition command (Command ID 0x02)
  // static buildDispenseNutritionCommand(cartridgeId: string) {
  //   // Parse hex string into bytes (e.g., "A2C1B2D1" -> [0xA2, 0xC1, 0xB2, 0xD1])
  //   const cartridgeBytes: number[] = [];
  //   for (let i = 0; i < cartridgeId.length; i += 2) {
  //     cartridgeBytes.push(parseInt(cartridgeId.substring(i, i + 2), 16));
  //   }
  //   cartridgeBytes.reverse();
    
  //   const command = new Uint8Array([
  //     0xDE, 0xAD,           // SOF
  //     DISPENSE_COMMAND_ID,                 // CMD (Dispense nutrition)
  //     cartridgeBytes.length,               // LEN (number of bytes for cartridge ID)
  //     ...cartridgeBytes,                   // DATA (cartridge ID bytes)
  //     0x01,                 // RW (Write operation)
  //     0xFE, 0xED           // EOF
  //   ]);
  //   return command;
  // }

  static buildDispenseNutritionCommand(cartridgeId: number) {
    console.log('Building dispense nutrition command with cartridgeId:', cartridgeId);
    
    const command = new Uint8Array([
      0xDE, 0xAD,           // SOF
      DISPENSE_COMMAND_ID,  // CMD (Dispense nutrition)
      0x01,                 // LEN (1 byte for cartridge ID)
      cartridgeId,          // DATA (cartridge ID bytes)
      0x01,                 // RW (Write operation)
      0xFE, 0xED            // EOF
    ]);
    return command;
  }

  // Build get live sensor data command (Command ID 0x04)
  static buildGetLiveSensorDataCommand() {
    const command = new Uint8Array([
      0xDE, 0xAD,           // SOF
      GET_LIVE_DATA_COMMAND_ID,                 // CMD (Get live sensor data)
      0x00,                 // LEN (no data)
      0x00,                 // RW (Read operation)
      0xFE, 0xED           // EOF
    ]);
    return command;
  }

  // Validate response format
  static validateResponse(responseBytes: Uint8Array, expectedCommandId: number) {
    if (!responseBytes || responseBytes.length < 7) {
      return false;
    }

    // Check frame markers and command ID
    return (
      responseBytes[0] === 0xDE &&                    // SOF_L
      responseBytes[1] === 0xAD &&                    // SOF_H
      responseBytes[2] === expectedCommandId &&       // Same command ID
      responseBytes[responseBytes.length - 2] === 0xFE && // EOF_L
      responseBytes[responseBytes.length - 1] === 0xED    // EOF_H
    );
  }
}