// react-query
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
// react
// import { Analytics } from '@vercel/analytics/react';
import { Suspense } from 'react';
import ReactDOM from 'react-dom/client';
// react helmet
import { HelmetProvider } from 'react-helmet-async';
import 'virtual:svg-icons-register';

import App from '@/App';

// import worker from './_mock';
// i18n
import './locales/i18n';
// tailwind css
import './theme/index.css';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3, // Number of retry attempts on failure
      gcTime: 0, // Cache expiration time is 5 minutes
      staleTime: 10_1000, // The time for data to become 'stale' is 10 seconds.
      refetchOnWindowFocus: false, // Prevent re-fetching data when the window is focused.
      refetchOnReconnect: false, // Prevent re-fetching data when reconnecting
      refetchOnMount: false, // Prevent re-fetching data when the component mounts.
    },
  },
});

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);

root.render(
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      {/* <ReactQueryDevtools initialIsOpen={false} /> */}
      <Suspense>
        {/* <Analytics /> */}
        <App />
      </Suspense>
    </QueryClientProvider>
  </HelmetProvider>,
);

// 🥵 start service worker mock in development mode
// worker.start();     // { onUnhandledRequest: 'bypass' } use it to bypassoriginal server for real server
