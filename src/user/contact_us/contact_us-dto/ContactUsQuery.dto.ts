import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsMongoId,
  IsNotEmpty,
  IsString,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  CONTACTUS_ATTACHMENTS_TYPES,
  QUERY_CATEGORY,
  QUERY_STATUS,
} from 'models/help';
import { User } from 'models/user';
import { ObjectId, Types } from 'mongoose';

export class ContactUsQueryUserDTO {
  @ApiProperty({
    description: 'User ID of the person submitting the query',
    type: String,
    example: '60d21b4667d0d8992e610c85',
  })
  @IsMongoId()
  @IsNotEmpty()
  id: Types.ObjectId;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'User Name',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  static transform(object: User): ContactUsQueryUserDTO {
    const transformedObj = new ContactUsQueryUserDTO();

    transformedObj.id = object.id;
    transformedObj.email = object.email;
    transformedObj.name = `${object.firstName} ${object.lastName}`;

    return transformedObj;
  }
}

export class ContactUsQueryAttachmentsDTO {

  @ApiProperty({
    description: 'Attachment URL',
    example: 'https://example.com/attachment.jpg',
  })
  url: string;

  @ApiProperty({
    description: 'Attachment Type',
    enum: CONTACTUS_ATTACHMENTS_TYPES,
    example: CONTACTUS_ATTACHMENTS_TYPES.IMAGE,
  })
  @IsEnum(CONTACTUS_ATTACHMENTS_TYPES)
  @IsNotEmpty()
  type: CONTACTUS_ATTACHMENTS_TYPES;

  static transform(
    object: any,
  ): ContactUsQueryAttachmentsDTO {
    const transformedObj = new ContactUsQueryAttachmentsDTO();
    transformedObj.type = object.type;
    transformedObj.url = object.url;
    return transformedObj;
  }
}

export class ContactUsQueryDTO {
  @ApiProperty({
    description: 'Query ID',
    type: String,
    example: '60d21b4667d0d8992e610c85',
  })
  @IsMongoId()
  @IsNotEmpty()
  id: Types.ObjectId;

  @ApiProperty({
    description: 'Category of the query',
    enum: QUERY_CATEGORY,
    example: QUERY_CATEGORY.LAGGING_AND_GLITCHES,
  })
  @IsEnum(QUERY_CATEGORY)
  @IsNotEmpty()
  category: QUERY_CATEGORY;

  @ApiProperty({
    description: 'Description of the issue',
    example: 'I am facing an issue with my billing statement.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Status of the query',
    enum: QUERY_STATUS,
    example: QUERY_STATUS.PENDING,
  })
  @IsEnum(QUERY_STATUS)
  @IsNotEmpty()
  status: QUERY_STATUS;

  @ApiProperty({
    description: 'User details',
    type: ContactUsQueryUserDTO,
  })
  @ValidateNested()
  @Type(() => ContactUsQueryUserDTO)
  @IsNotEmpty()
  user: ContactUsQueryUserDTO;

  @ApiProperty({
    description: 'Attachments related to the query',
    type: [ContactUsQueryAttachmentsDTO],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ContactUsQueryAttachmentsDTO)
  @IsNotEmpty()
  attachments: ContactUsQueryAttachmentsDTO[];

  static transform(object: any): ContactUsQueryDTO {
    const transformedObj = new ContactUsQueryDTO();

    transformedObj.id = object._id.toString();
    transformedObj.category = object.category;
    transformedObj.description = object.description;
    transformedObj.status = object.status;
    transformedObj.user = ContactUsQueryUserDTO.transform(object.user);

    if (object.attachments && object.attachments.length > 0) {
      transformedObj.attachments = object.attachments.map(
        (item: any) =>
          ContactUsQueryAttachmentsDTO.transform(item),
      );
    }

    return transformedObj;
  }
}
