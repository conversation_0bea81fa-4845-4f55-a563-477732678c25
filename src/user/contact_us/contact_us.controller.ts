import {
  Body,
  Controller,
  Post,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/middlewares';
import { ContactUsService } from './contact_us.service';
import { PostContactUsQueryReqDTO } from './contact_us-dto';
import { FilesInterceptor } from '@nestjs/platform-express';
import { AllowedMixEntensions, getMulterMediaOptions } from 'src/utils/multer';

@ApiTags('User-Contact-Us')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('contact')
export class ContactUsController {
  constructor(private readonly contactUsService: ContactUsService) {}

  @ApiOperation({ summary: 'Submit a contact us query' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Contact Us payload including optional file attachments',
    type: PostContactUsQueryReqDTO,
  })
  @ApiResponse({
    status: 201,
    description: 'Query submitted successfully',
  })
  @Post()
  @UseInterceptors(
    FilesInterceptor(
      'attachments',
      3,
      {
        ...getMulterMediaOptions({
          fileSize: 20,
          fileExtensions: AllowedMixEntensions,
        }),
        fileFilter: (req, file, callback) => {
          // Store the limit in request for error handling
          req['fileLimit'] = 3;
          
          // Use the file filter logic directly instead of calling originalFilter
          if (!file || !file.mimetype) {
            return callback(
              new Error('Invalid file or missing mimetype. Please upload a valid file.'),
              false,
            );
          }

          const fileExt = file.mimetype.split('/')[1];
          if (!fileExt) {
            return callback(
              new Error('Unable to extract file extension from the mimetype.'),
              false,
            );
          }

          if (AllowedMixEntensions.includes(fileExt)) {
            callback(null, true);
          } else {
            callback(
              new Error(`Invalid file type. Allowed types are: ${AllowedMixEntensions.join(', ')}.`),
              false,
            );
          }
        },
      },
    ),
  )
  async PostContactQuery(
    @Req() req: Request,
    @Body() postData: PostContactUsQueryReqDTO,
    @UploadedFiles() attachments: Express.Multer.File[],
  ) {
    const user = req['user'];

    return this.contactUsService.postContactUsQuery(
      user,
      postData,
      attachments,
    );
  }
}
