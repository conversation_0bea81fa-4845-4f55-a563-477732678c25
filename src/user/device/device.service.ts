import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ClientSession, Model, Types } from 'mongoose';
import {
  ChangeDeviceDetailsReqDTO,
  CreateDeviceConnectionReqDTO,
  CreateDeviceConnectionResDTO,
  RemoveDeviceConnectionReqDTO,
  UserDeviceConnectionsDTO,
} from './device-dto';
import {
  DEVICE_CONNECTION_STATUS,
  UserDeviceConnections,
} from 'models/device';
import { User } from 'models/user';
import { UtilsService } from 'src/common/services';
import { GetConnectedDeviceResDTO } from './device-dto/getConnectedDeviceRes.dto';
import { ChangeDeviceDetailsResDTO } from './device-dto/changeDeviceDetails-res.dto';

@Injectable()
export class UserDeviceService {
  constructor(
    private readonly utilsService: UtilsService,
    @InjectModel(UserDeviceConnections.name)
    private readonly userDeviceConnectionModel: Model<UserDeviceConnections>,

  ) {}

  async createDeviceConnection(
    user: User,
    createDeviceData: CreateDeviceConnectionReqDTO,
    session?: ClientSession,
  ): Promise<CreateDeviceConnectionResDTO> {
    try {
      const { deviceId, deviceName, localName } = createDeviceData;

      // Check if the user already has this device connected
      const existingConnection = await this.userDeviceConnectionModel.findOne({
        userId: user._id,
        deviceId,
        deviceName,
        localName,
        connectionStatus: DEVICE_CONNECTION_STATUS.CONNECTED,
      });

      if (existingConnection) {
        throw new BadRequestException('Device is already connected.');
      }

      // Disconnect all existing connections for the user
      await this.userDeviceConnectionModel.updateMany(
        { userId: user._id },
        { $set: { connectionStatus: DEVICE_CONNECTION_STATUS.DISCONNECTED } },
        { session },
      );

      // Upsert a new connection
      const updatedConnection =
        await this.userDeviceConnectionModel.findOneAndUpdate(
          { userId: user._id, deviceId, deviceName, localName },
          { connectionStatus: DEVICE_CONNECTION_STATUS.CONNECTED },
          { new: true, upsert: true, session },
        );

      return {
        msg: 'Device Connected Successfully!',
        deviceConnectionData:
          UserDeviceConnectionsDTO.transform(updatedConnection),
      };
    } catch (error) {
      throw new BadRequestException(
        error.message || 'Failed to connect device',
      );
    }
  }

  async getConnectedDevice(userId: Types.ObjectId): Promise<GetConnectedDeviceResDTO> {
    
    const userDeviceConnection = await this.userDeviceConnectionModel.findOne({
      userId,
      connectionStatus: DEVICE_CONNECTION_STATUS.CONNECTED,
    });

    if (!userDeviceConnection) {
      return {
        error: false,
        statusCode: HttpStatus.OK,
        device: null,

        connectionStatus: DEVICE_CONNECTION_STATUS.DISCONNECTED,
      };
    }
    return {
      error: false,
      statusCode: HttpStatus.OK,
      device:  null,
      connectionStatus: DEVICE_CONNECTION_STATUS.CONNECTED,
    };
  }

  async createNewDeviceConnection(
    user: User,
    createDeviceData: CreateDeviceConnectionReqDTO,
  ) {
    try {
      const { deviceId, deviceName, localName } = createDeviceData;

      if (!deviceId || !deviceName || !localName) {
        throw new BadRequestException({
          statusCode: HttpStatus.BAD_REQUEST,
          success: false,
          message: 'Device ID, Device Name and Local Name are required!',
        });
      }

      const response = await this.createDeviceConnection(
        user,
        createDeviceData,
      );
      return {
        statusCode: HttpStatus.OK,
        success: true,
        message: response.msg,
        data: response.deviceConnectionData,
      };
    } catch (error) {
      throw new BadRequestException({
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message || 'Failed to connect device',
      });
    }
  }

  async getAllDevicesToUser({ page = '1' }: { page?: string }) {
    return {
      statusCode: HttpStatus.OK,
      success: true,
      message: 'Devices fetched successfully!',
      data: [],
    };
  }

  async removeDeviceConnection(
    userId: Types.ObjectId,
    removeDeviceData: RemoveDeviceConnectionReqDTO,
  ) {
    try {
      const { deviceId } = removeDeviceData;

      // Find the user's device connection
      const existingConnection = await this.userDeviceConnectionModel.findOne({
        userId,
        deviceId,
      });

      if (!existingConnection) {
        throw new BadRequestException({
          statusCode: HttpStatus.NOT_FOUND,
          success: false,
          message: 'Device connection not found!',
        });
      }

      // Remove the device connection
      await this.userDeviceConnectionModel.deleteOne({ userId, deviceId });

      return {
        statusCode: HttpStatus.OK,
        success: true,
        message: 'Device disconnected successfully!',
      };
    } catch (error) {
      throw new BadRequestException({
        statusCode: HttpStatus.BAD_REQUEST,
        success: false,
        message: error.message || 'Failed to remove device connection',
      });
    }
  }

  async changeDeviceDetails(
    userId: Types.ObjectId,
    changeDeviceData: ChangeDeviceDetailsReqDTO,
  ):Promise<ChangeDeviceDetailsResDTO> {
    const { localName, deviceId } = changeDeviceData;

      // Find the user's device connection
    const existingConnection = await this.userDeviceConnectionModel.findOne({
      userId,
      deviceId,
      connectionStatus: DEVICE_CONNECTION_STATUS.CONNECTED,
    });

    if (!existingConnection) {
      throw new BadRequestException({
        statusCode: HttpStatus.NOT_FOUND,
        success: false,
        message: 'Device connection not found!',
      });
    }

    // Update the device name
    await this.userDeviceConnectionModel.updateOne(
      { userId },
      { $set: { localName: localName } },
    );

    return ChangeDeviceDetailsResDTO.transform('Device details updated successfully!');
  }
}
