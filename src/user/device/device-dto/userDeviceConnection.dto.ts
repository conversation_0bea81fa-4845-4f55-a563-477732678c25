import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString, IsNotEmpty } from 'class-validator';
import { DEVICE_CONNECTION_STATUS, UserDeviceConnections } from 'models/device';
import { Types } from 'mongoose';

export class UserDeviceConnectionsDTO {
  @ApiProperty({
    description:
      'The unique identifier of the user associated with the device connection',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  @IsNotEmpty()
  userId: Types.ObjectId;

  @ApiProperty({
    description: 'The unique identifier for the device',
    example: 'device12345',
  })
  @IsString()
  @IsNotEmpty()
  deviceId: string;

  @ApiProperty({
    description: 'The unique identifier for the device',
    example: 'device12345',
  })
  @IsString()
  @IsNotEmpty()
  serialId: string;

  @ApiProperty({
    description: 'The status of the device connection',
    example: 'active',
    enum: DEVICE_CONNECTION_STATUS,
  })
  @IsEnum(DEVICE_CONNECTION_STATUS)
  @IsNotEmpty()
  connectionStatus: DEVICE_CONNECTION_STATUS;

  // Transform logic
  static transform(object: UserDeviceConnections): UserDeviceConnectionsDTO {
    const transformedObj = new UserDeviceConnectionsDTO();
    transformedObj.userId = object.userId;
    transformedObj.deviceId = object.deviceId;
    transformedObj.connectionStatus =
      object.connectionStatus as DEVICE_CONNECTION_STATUS;

    return transformedObj;
  }
}
