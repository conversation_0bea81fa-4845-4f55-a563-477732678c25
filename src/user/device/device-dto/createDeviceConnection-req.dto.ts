import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsString, IsNotEmpty } from 'class-validator';

export class CreateDeviceConnectionReqDTO {
  @ApiProperty({
    description: 'The unique identifier for the device',
    example: '1234567890',
  })
  @IsString()
  @IsNotEmpty()
  deviceId: string;

  @ApiProperty({
    description: 'The name of the device',
    example: 'SNB Device 1',
  })
  @IsString()
  @IsNotEmpty()
  deviceName: string;

  @ApiProperty({
    description: 'The local name of the device',
    example: "<PERSON><PERSON>'s Bottle",
  })
  @IsString()
  @IsNotEmpty()
  localName: string;
}
