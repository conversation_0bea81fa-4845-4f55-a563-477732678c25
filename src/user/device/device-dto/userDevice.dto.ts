import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsUrl, IsDate } from 'class-validator';

export class DeviceUserDTO {
  @ApiProperty({ description: 'Unique identifier for the device' })
  @IsString()
  id: string;

  @ApiProperty({ type: String, description: 'Name of the device' })
  name: string;

  @ApiProperty({ description: 'Creation timestamp of the device' })
  @IsDate()
  createdAt: Date;

  static transform(object): DeviceUserDTO {
    const transformedObj = new DeviceUserDTO();
    transformedObj.id = object._id.toString();
    transformedObj.name = object.name;
    transformedObj.createdAt = (object as any).createdAt;
    return transformedObj;
  }
}
