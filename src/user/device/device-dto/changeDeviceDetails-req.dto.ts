
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class ChangeDeviceDetailsReqDTO {
    @ApiProperty({
    description: 'The new name for the device',
    example: 'SNB Device 1',
    })
    @IsString()
    @IsNotEmpty()
    localName: string;

    @ApiProperty({
    description: 'The unique identifier for the device',
    example: 'SNB Device 1',
    })
    @IsString()
    @IsNotEmpty()
    deviceId: string;
}