
import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export class ChangeDeviceDetailsResDTO extends BaseResponse {
  @ApiProperty({
    description: 'Message indicating the status of the device details change process',
    example: 'Device details updated successfully!',
  })
  msg: string;

  static transform(message: string): ChangeDeviceDetailsResDTO {
    const transformedObj = new ChangeDeviceDetailsResDTO();
    transformedObj.msg = message;
    return transformedObj;
  }
  
}
