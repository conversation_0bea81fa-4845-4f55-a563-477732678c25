import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { DispenseTabletResDTO } from './dispense-dto/DispenseTabletRes.dto';
import { DispensedTablet } from 'models/dispense/dispensedTablet.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Cartridge } from 'models/cartridge/cartridge.schema';
import { UserCartridgeStockModal } from 'models/cartridge/userCartidgeStock.schema';
import { DeleteDispenseRecordResDTO, DispenseHistoryResDTO } from './dispense-dto';
import { DispenseHistoryRecordDTO } from './dispense-dto/DispenseHistoryRecord.dto';
import { CartridgeDetailsDTO, UserCartridgeResDTO } from '../cartridge/cartridge-dto';
import { CartridgeModal } from 'models/cartridge/cartridgeModal.schema';
import { CartridgeWithPopulatedNutrients } from 'models/cartridge/types';
import { getDateInTimezone } from 'src/utils/dateConvertors/convertDateToUserTime';
import { DailyDose } from 'models/dispense/dailyDose.schema';
import { DailyDoseTakenResponseDTO } from './dispense-dto/DailyDoseTakenResponse.dto';

@Injectable()
export class DispenseService {
    constructor(
        @InjectModel(DispensedTablet.name)
        private readonly dispensedTabletModel: Model<DispensedTablet>,

        @InjectModel(Cartridge.name)
        private readonly cartridgeModel: Model<Cartridge>,

        @InjectModel(CartridgeModal.name)
        private readonly cartridgeModalModel: Model<CartridgeModal>,

        @InjectModel(UserCartridgeStockModal.name)
        private readonly userCartridgeStockModal: Model<UserCartridgeStockModal>,

        @InjectModel(DailyDose.name)
        private readonly dailyDoseModel: Model<DailyDose>,
    ){}

    async getDispenseHistory(userId: Types.ObjectId, page: number = 0): Promise<DispenseHistoryResDTO> {
        const totalRecords = await this.dispensedTabletModel.countDocuments({ dispensedBy: userId,isDeleted: false }).lean();

        const dispenseHistory = await this.dispensedTabletModel.find({ dispensedBy: userId, isDeleted: false }).populate('cartridgeId').sort({ dispensedAt: -1 }).skip(page * 10).limit(10).lean();

        const totalCurrentRecords = dispenseHistory.length;

        const transformedHistory = await Promise.all(
            dispenseHistory.map(async (item) => {
                const record = new DispenseHistoryRecordDTO();
                record.id = item._id;

                const cartridgeDetails = await this.cartridgeModalModel.findById(item.cartridgeModalId).populate('nutrients.nutrientId').lean<CartridgeWithPopulatedNutrients>();
                
                const cartridge = new CartridgeDetailsDTO();
                cartridge.healthName = cartridgeDetails.healthName;
                cartridge.healthArea = cartridgeDetails.healthArea;
                cartridge.flavour = cartridgeDetails.flavour;
                cartridge.colour = cartridgeDetails.colour;
                cartridge.nutrients = cartridgeDetails.nutrients.map(nutrient => ({
                    name: nutrient.nutrientId.name,
                    rda: nutrient.rda
                }));
                
                record.cartridge = cartridge;
                record.dispenseDate = item.dispensedAt;
                record.dispenseTime = item.dispensedAt;
                return record;
            })
        );
        
        return DispenseHistoryResDTO.transform({
            dispenseHistory: transformedHistory,
            totalCurrentRecords,
            totalRecords,
        });
    }

    async dispenseTablet(cartridgeId: string, userId: Types.ObjectId,userTimeZone: string):Promise<DispenseTabletResDTO> {
        const session = await this.dispensedTabletModel.db.startSession();
        
        try {
            await session.withTransaction(async () => {
                // Find cartridge
                const cartridge = await this.cartridgeModel.findOne({ 
                    cartridgeId, 
                    isDeleted: false 
                }).session(session);
                
                if (!cartridge) {
                    throw new NotFoundException('Cartridge not found.Please use another cartridge.');
                }
                
                // if (cartridge.tablets <= 0) {
                //     throw new BadRequestException('No tablets available in cartridge.Please use another cartridge.');
                // }

                // Find user stock
                // const userStock = await this.userCartridgeStockModal.findOne({
                //     userId,
                //     cartridgeId: cartridge.cartridgeModalId,
                // }).session(session);

                // if (!userStock || userStock.quantity <= 0) {
                //     throw new BadRequestException('Please add more tablets to your inventory');
                // }

                // Create dispensed tablet entry
                await this.dispensedTabletModel.create([{
                    cartridgeId: cartridge._id,
                    cartridgeModalId: cartridge.cartridgeModalId,
                    dispensedAt: new Date(),
                    dispensedBy: userId
                }], { session });

                // Decrease cartridge tablets and handle currentCount reset logic
                await this.cartridgeModel.updateOne(
                    { _id: cartridge._id },
                    [
                        {
                            $set: {
                                tablets: { $subtract: ['$tablets', 1] },
                                currentCount: {
                                    $cond: {
                                        if: { $lte: [{ $subtract: ['$currentCount', 1] }, 0] },
                                        then: 5,
                                        else: { $subtract: ['$currentCount', 1] }
                                    }
                                }
                            }
                        }
                    ],
                    { session }
                );

                // Decrease user stock
                // await this.userCartridgeStockModal.updateOne(
                //     { _id: userStock._id },
                //     { $inc: { quantity: -1 } },
                //     { session }
                // );
            });

            const todayStr = getDateInTimezone(new Date(),userTimeZone);
            
            await this.dailyDoseModel.updateOne(
                { userId, dateLocal: todayStr },
                { $inc: { tabletsTaken: 1 } },
                { upsert: true }
            );

            // Fetch the updated cartridge and cartridge modal details
            const updatedCartridge = await this.cartridgeModel.findOne({ 
                cartridgeId, 
                isDeleted: false 
            }).session(session);
            
            const cartridgeModal = await this.cartridgeModalModel.findById(updatedCartridge.cartridgeModalId).populate('nutrients.nutrientId').lean<CartridgeWithPopulatedNutrients>().session(session);

            return DispenseTabletResDTO.transform(updatedCartridge, cartridgeModal);
        } finally {
            await session.endSession();
        }
    }

    async deleteDispenserecord(dispenseId: string, userId: Types.ObjectId):Promise<DeleteDispenseRecordResDTO> {
        if(!dispenseId || !Types.ObjectId.isValid(dispenseId)) {
            throw new BadRequestException('Please provide valid dispense id');
        }

        const dispenseRecord = await this.dispensedTabletModel.findOne({ _id: dispenseId, dispensedBy: userId });
        if (!dispenseRecord) {
            throw new NotFoundException('Dispense record not found');
        }

        dispenseRecord.isDeleted = true;
        await dispenseRecord.save();

        return DeleteDispenseRecordResDTO.transform('Record deleted successfully');
    }

    async hasTakenToday(userId: string,userTimeZone: string): Promise<DailyDoseTakenResponseDTO> {
        const todayStr = getDateInTimezone(new Date(),userTimeZone);

        const dose = await this.dailyDoseModel.findOne({ userId, dateLocal: todayStr });

        const tabletsTakenToday = dose?.tabletsTaken ?? 0;

        return DailyDoseTakenResponseDTO.transform({ tabletsTakenToday, hasTakenToday: tabletsTakenToday > 0 });
  };
}
