
import { ApiProperty } from '@nestjs/swagger';
import { CartridgeDetailsDTO } from '../../cartridge/cartridge-dto/cartridgeModalDetails.dto';
import { BaseResponse } from 'src/utils/responses';

export class DispenseTabletResDTO extends BaseResponse {
    @ApiProperty({
        description: 'Dispense tablet response data',
        example: {
            cartridgeId: 'CART001',
            tablets: 30,
            cartridgeDetails: {
                id: '507f1f77bcf86cd799439012',
                healthName: 'Vitamin D3',
                healthArea: 'Bone Health',
                flavour: 'Natural',
                colour: {
                    cartridge: 'Blue',
                    tablet: 'White'
                },
                nutrients: [
                    {
                        name: 'Vitamin D3',
                        rda: {
                            quantity: 1000,
                            unit: 'IU',
                            percentage: 125
                        }
                    }
                ]
            },
            currentCount: 5
        }
    })
    data: {
        cartridgeId: string;
        tablets: number;
        cartridgeDetails: CartridgeDetailsDTO;
        currentCount: number;
    };

    static transform(cartridge: any, cartridgeModal: any): DispenseTabletResDTO {
        const response = new DispenseTabletResDTO();
        response.data = {
            cartridgeId: cartridge.cartridgeId,
            tablets: cartridge.tablets,
            currentCount: cartridge.currentCount,
            cartridgeDetails: CartridgeDetailsDTO.transform(cartridgeModal)
        };
        return response;
    }
}
