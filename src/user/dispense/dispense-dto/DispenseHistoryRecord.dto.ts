
import { ApiProperty } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { CartridgeDetailsDTO } from 'src/user/cartridge/cartridge-dto';

export class DispenseHistoryRecordDTO {
    @ApiProperty({
        type: Types.ObjectId,
        description: 'Dispense Id',
    })
    id: Types.ObjectId;   

    @ApiProperty({
        type: CartridgeDetailsDTO,
        description: 'Cartridge Details',
    })
    cartridge: CartridgeDetailsDTO;

    @ApiProperty({
        type: Date,
        description: 'Dispense Date',
    })
    dispenseDate: Date;

    @ApiProperty({
        type: Date,
        description: 'Dispense Time',
    })
    dispenseTime: Date;
}
