import { BaseResponse } from "src/utils/responses";

export class DailyDoseTakenResponseDTO extends BaseResponse {
  data :{
    tabletsTakenToday: number;
    hasTakenToday: boolean;
  }

  static transform(data: any): DailyDoseTakenResponseDTO {
    const transformedObj = new DailyDoseTakenResponseDTO();
    transformedObj.data = {
        tabletsTakenToday: data.tabletsTakenToday,
        hasTakenToday: data.hasTakenToday,
    };
    return transformedObj;
  }
}