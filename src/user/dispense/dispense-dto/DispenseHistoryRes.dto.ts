
import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { DispenseHistoryRecordDTO } from './DispenseHistoryRecord.dto';

class DataDTO {
  @ApiProperty({
    type: [DispenseHistoryRecordDTO],
    description: 'Dispense History',
  })
  dispenseHistory: DispenseHistoryRecordDTO[];

  @ApiProperty({
    type: Number,
    description: 'Total current records',
  })
  totalCurrentRecords: number;

  @ApiProperty({
    type: Number,
    description: 'Total records',
  })
  totalRecords: number;
}

export class DispenseHistoryResDTO extends BaseResponse {

    @ApiProperty({
        type: DataDTO,
        description: 'Data',
    })
    data: DataDTO;

    static transform(data: any): DispenseHistoryResDTO {
        const transformedObj = new DispenseHistoryResDTO();
        transformedObj.data = {
            dispenseHistory: data.dispenseHistory,
            totalCurrentRecords: data.totalCurrentRecords,
            totalRecords: data.totalRecords
        };
        
        return transformedObj;
    }
}
