
import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export class DeleteDispenseRecordResDTO extends BaseResponse {
  @ApiProperty({
    type: String,
    description: 'Message indicating the status of the dispense operation',
    example: 'Record deleted successfully',
  })
  message: string;

  static transform(message: string): DeleteDispenseRecordResDTO {
    const transformedObj = new DeleteDispenseRecordResDTO();
    transformedObj.message = message;
    return transformedObj;
  }
}
