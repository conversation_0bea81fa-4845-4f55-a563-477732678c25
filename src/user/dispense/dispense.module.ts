import { Module } from '@nestjs/common';
import { DispenseService } from './dispense.service';
import { DispenseController } from './dispense.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { DispensedTablet, DispensedTabletSchema } from 'models/dispense/dispensedTablet.schema';
import { Cartridge, CartridgeSchema } from 'models/cartridge/cartridge.schema';
import { UserCartridgeStockModal, UserCartridgeStockSchema } from 'models/cartridge/userCartidgeStock.schema';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RedisModule } from 'src/redis/redis.module';
import { RepoModule } from 'src/repo/repo.module';
import { CartridgeModal, CartridgeModalSchema } from 'models/cartridge/cartridgeModal.schema';
import { DailyDose, DailyDoseSchema } from 'models/dispense/dailyDose.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: DispensedTablet.name, schema: DispensedTabletSchema },
      { name: Cartridge.name, schema: CartridgeSchema },
      { name: CartridgeModal.name, schema: CartridgeModalSchema },
      { name: UserCartridgeStockModal.name, schema: UserCartridgeStockSchema },
      { name: DailyDose.name, schema: DailyDoseSchema },
    ]),
    CommonModule,
    AuthModule,
    RedisModule,
    RepoModule,
  ],
  controllers: [DispenseController],
  providers: [DispenseService],
})
export class DispenseModule {}
