import { Body, Controller, Delete, Get, Param, Post, Put, Request, UseGuards } from '@nestjs/common';
import { DispenseService } from './dispense.service';
import { DispenseTabletResDTO } from './dispense-dto/DispenseTabletRes.dto';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/middlewares';
import { ErrorResponse } from 'src/utils/responses';
import { DeleteDispenseRecordResDTO, DispenseHistoryResDTO } from './dispense-dto';
import { DailyDoseTakenResponseDTO } from './dispense-dto/DailyDoseTakenResponse.dto';


@ApiTags('Dispense')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('dispense')
export class DispenseController {
  constructor(private readonly dispenseService: DispenseService) {}

  @ApiOperation({ summary: 'Get Dispense history' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the dispense history.',
    type: DispenseHistoryResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/history')
  async getDispenseHistory(@Request() req, @Body() filterQueries: {page?: number}): Promise<DispenseHistoryResDTO> {
    return this.dispenseService.getDispenseHistory(req.user._id, filterQueries?.page);
  }

  @ApiOperation({ summary: 'Dispense a tablet from a cartridge' })
  @ApiResponse({
    status: 200,
    description: 'Successfully dispensed the tablet.',
    type: DispenseTabletResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('/tablet/:cartridgeId')
  async dispenseTablet(
    @Request() req,
    @Param('cartridgeId') cartridgeId: string
  ):Promise<DispenseTabletResDTO> {
    return this.dispenseService.dispenseTablet(cartridgeId,req.user._id,req.user.timeZone);
  }

  @ApiOperation({ summary: 'Delete dispense record' })
  @ApiResponse({
    status: 200,
    description: 'Successfully deleted the dispense record.',
    type: DeleteDispenseRecordResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put('/record/:dispenseId')
  async deleteDispenserecord(
    @Request() req,
    @Param('dispenseId') dispenseId: string
  ):Promise<DeleteDispenseRecordResDTO> {
    return this.dispenseService.deleteDispenserecord(dispenseId,req.user._id);
  }

  @Get('has-taken-today')
  @ApiOperation({ summary: 'Check if user has taken dose today' })
  @ApiResponse({
    status: 200,
    description: 'Number of tablets taken today',
    schema: {
      example: { tabletsTakenToday: 2, hasTakenToday: true },
    },
  })
  async hasTakenToday(
    @Request() req,
  ):Promise<DailyDoseTakenResponseDTO> {
    return this.dispenseService.hasTakenToday(req.user._id,req.user.timeZone);
  }
}
