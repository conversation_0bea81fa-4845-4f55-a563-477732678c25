import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsString } from 'class-validator';
import { Faq } from 'models/help';

export class FaqDto {
  @ApiProperty({ example: '67a20c327b4206cda53747cd' })
  id: string;

  @ApiProperty({ example: 'How to reset my password?' })
  @IsString()
  question: string;

  @ApiProperty({
    example:
      'To reset your password, go to settings and select reset password.',
  })
  @IsString()
  answer: string;

  @ApiProperty({ example: false })
  @IsBoolean()
  isDeleted: boolean;

  @ApiProperty({ example: '2025-02-04T12:46:43.003Z' })
  @IsString()
  createdAt: string;

  static transform(object: Faq): FaqDto {
    const transformedObj = new FaqDto();
    transformedObj.id = object._id.toString();
    transformedObj.question = object.question;
    transformedObj.answer = object.answer;
    transformedObj.isDeleted = object.isDeleted;
    transformedObj.createdAt = (object as any).createdAt;

    return transformedObj;
  }
}
