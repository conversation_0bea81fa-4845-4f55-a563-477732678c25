import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { FaqDto } from './faq.dto';
import { IsArray } from 'class-validator';

class FAQ_data {
  @ApiProperty({ example: 100, description: 'Total number of activity videos' })
  total: number;

  @ApiProperty({
    example: 50,
    description: 'Number of hits matching the query',
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of helps',
    type: [FaqDto],
  })
  @IsArray()
  faqs: FaqDto[];
} 

export class GetAllUserFaqResDto extends BaseResponse {
  
  data:FAQ_data;

  static transform(data: any): GetAllUserFaqResDto {
    const transformedObj = new GetAllUserFaqResDto();
    transformedObj.data = data;
    return transformedObj;
  }
}
