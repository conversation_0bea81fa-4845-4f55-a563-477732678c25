
import { ApiProperty } from '@nestjs/swagger';
import { CartridgeDetailsDTO } from './cartridgeModalDetails.dto';


export class UserCartridgeResDTO {
    @ApiProperty({
        type: CartridgeDetailsDTO,
        description: 'Cartridge Details',
        example: {
            healthName: 'Cartridge 1',
            healthArea: 'Health Area 1',
            flavour: 'Flavour 1',
            colour: {
                cartridge: 'Cartridge Colour 1',
                tablet: 'Tablet Colour 1',
            },
            nutrients: [
                {
                    name: 'Nutrient 1',
                    rda: {
                        quantity: 100,
                        unit: 'mg',
                        percentage: 10,
                    },
                },
            ],
        },
    })
    cartridge:CartridgeDetailsDTO

    @ApiProperty()
    quantity: number;
}
