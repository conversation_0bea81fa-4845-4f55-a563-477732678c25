import { ApiProperty } from '@nestjs/swagger';
import { CartridgeDetailsDTO } from './cartridgeModalDetails.dto';

export class CartridgeDetailsResDTO {
    @ApiProperty({
        description: 'Cartridge ID',
        example: 'CART001'
    })
    cartridgeId: string;

    @ApiProperty({
        description: 'Number of tablets in the cartridge',
        example: 30
    })
    tablets: number;

    @ApiProperty({
        type: CartridgeDetailsDTO,
        description: 'Cartridge modal details including health information, nutrients, etc.',
        example: {
            id: '507f1f77bcf86cd799439012',
            healthName: 'Vitamin D3',
            healthArea: 'Bone Health',
            flavour: 'Natural',
            colour: {
                cartridge: 'Blue',
                tablet: 'White'
            },
            nutrients: [
                {
                    name: 'Vitamin D3',
                    rda: {
                        quantity: 1000,
                        unit: 'IU',
                        percentage: 125
                    }
                }
            ]
        }
    })
    cartridgeDetails: CartridgeDetailsDTO;

    @ApiProperty({
        description: 'Current count of tablets in the cartridge',
        example: 5
    })
    currentCount: number;

    static transform(cartridge: any, cartridgeModal: any): CartridgeDetailsResDTO {
        const response = new CartridgeDetailsResDTO();
        response.cartridgeId = cartridge.cartridgeId;
        response.tablets = cartridge.tablets;
        response.currentCount = cartridge.currentCount;
        response.cartridgeDetails = CartridgeDetailsDTO.transform(cartridgeModal);
        return response;
    }
}
