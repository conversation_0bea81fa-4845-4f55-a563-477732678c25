

export class CartridgeDetailsDTO {
    id:string;
    healthName: string;
    healthArea: string;
    flavour: string;
    colour: {
        cartridge: string;
        tablet: string;
    };
    nutrients: {
        name: string;
        rda: {
            quantity: number;
            unit: string;
            percentage: number | null;
        };
    }[];

    static transform(cartridge: any): CartridgeDetailsDTO {
        const transformedObj = new CartridgeDetailsDTO();
        transformedObj.id = cartridge._id.toString();
        transformedObj.healthName = cartridge.healthName;
        transformedObj.healthArea = cartridge.healthArea;
        transformedObj.flavour = cartridge.flavour;
        transformedObj.colour = cartridge.colour;
        transformedObj.nutrients = cartridge.nutrients.map(nutrient => ({
            name: nutrient.nutrientId.name,
            rda: nutrient.rda
        }))?.sort((a, b) => a.name.localeCompare(b.name));
        return transformedObj;
    }
}