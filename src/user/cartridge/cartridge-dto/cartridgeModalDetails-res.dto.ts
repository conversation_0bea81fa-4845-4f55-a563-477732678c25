import { ApiProperty } from '@nestjs/swagger';
import { CartridgeDetailsDTO } from './cartridgeModalDetails.dto';
import { BaseResponse } from 'src/utils/responses';

export class CartridgeModalDataDTO {
  @ApiProperty({
    type: CartridgeDetailsDTO,
    description: 'Full cartridge details',
    // you can also put an example here if you want
  })
  cartridge: CartridgeDetailsDTO;

  @ApiProperty({ example: 2, description: 'Number of cartridges selected' })
  quantity: number;
}

export class CartridgeModalDetailsResDTO extends BaseResponse {
  @ApiProperty({
    type: CartridgeModalDataDTO,
    description: 'Cartridge modal details including health information, nutrients, etc.',
    example: {
      cartridge: {
        id: '507f1f77bcf86cd799439012',
        healthName: 'Vitamin D3',
        healthArea: 'Bone Health',
        flavour: 'Natural',
        colour: { cartridge: 'Blue', tablet: 'White' },
        nutrients: [
          {
            name: 'Vitamin D3',
            rda: { quantity: 1000, unit: 'IU', percentage: 125 },
          },
        ],
      },
      quantity: 2,
    },
  })
  data: CartridgeModalDataDTO;

  static transform(cartridgeModal: any, quantity: number): CartridgeModalDetailsResDTO {
    const response = new CartridgeModalDetailsResDTO();
    const data = new CartridgeModalDataDTO();

    // CartridgeDetailsDTO.transform(...) should return the DTO (or a plain object matching it)
    data.cartridge = CartridgeDetailsDTO.transform(cartridgeModal);
    data.quantity = quantity;

    response.data = data;
    return response;
  }
}
