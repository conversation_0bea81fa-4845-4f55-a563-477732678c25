import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { UserCartridgeResDTO, CartridgeDetailsResDTO } from './cartridge-dto';
import { CartridgeModal } from 'models/cartridge/cartridgeModal.schema';
import { UserCartridgeStockModal } from 'models/cartridge/userCartidgeStock.schema';
import { CartridgeWithPopulatedNutrients } from 'models/cartridge/types';
import { Cartridge } from 'models/cartridge/cartridge.schema';
import { CartridgeModalDetailsResDTO } from './cartridge-dto/cartridgeModalDetails-res.dto';

@Injectable()
export class CartridgeService {
    constructor(
        @InjectModel(Cartridge.name)
        private readonly cartridgeModel: Model<Cartridge>,

        @InjectModel(CartridgeModal.name)
        private readonly cartridgeModelModel: Model<CartridgeModal>,

        @InjectModel(UserCartridgeStockModal.name)
        private readonly userCartridgeStockModel: Model<UserCartridgeStockModal>,
    ) {}

    async getAllCartridges(userId: Types.ObjectId): Promise<UserCartridgeResDTO[]> {
        // Get all available cartridges
        const allCartridges = await this.cartridgeModelModel
        .find()
        .populate('nutrients.nutrientId')
        .lean<CartridgeWithPopulatedNutrients[]>();
                
        // Get user's existing cartridge stock
        const userCartridgeStocks = await this.userCartridgeStockModel.find({ userId });
        
        // Create a map for quick lookup
        const stockMap = new Map(
            userCartridgeStocks.map(stock => [stock.cartridgeId.toString(), stock.quantity])
        );
        
        // Process each cartridge
        const userCartridges: UserCartridgeResDTO[] = [];
        
        for (const cartridge of allCartridges) {
            const cartridgeId = cartridge._id.toString();
            let quantity = stockMap.get(cartridgeId);
            
            // If cartridge doesn't exist for user, create it with default quantity 0
            if (quantity === undefined) {
                await this.userCartridgeStockModel.create({
                    userId,
                    cartridgeId: cartridge._id,
                    quantity: 0
                });
                quantity = 0;
            }
            
            userCartridges.push({
                cartridge: {
                    id:cartridge._id.toString(),
                    healthName: cartridge.healthName,
                    healthArea: cartridge.healthArea,
                    flavour: cartridge.flavour,
                    colour: cartridge.colour,
                    nutrients: cartridge.nutrients.map(nutrient => ({
                        name: nutrient.nutrientId.name,
                        rda: nutrient.rda
                    }))
                },
                quantity
            });
        }
        
        return userCartridges;
    }

    async getCartridgeModalDetails(
        userId: Types.ObjectId,
        cartridgeId: Types.ObjectId,
    ): Promise<CartridgeModalDetailsResDTO> {
        const cartridge = await this.cartridgeModelModel
            .findById(cartridgeId)
            .populate('nutrients.nutrientId')
            .lean<CartridgeWithPopulatedNutrients>();

        if(!cartridge){
            throw new NotFoundException('Cartridge modal not found');
        }

        const userStock = await this.userCartridgeStockModel.findOne({
            userId,
            cartridgeId,
        });
        const quantity = userStock ? userStock.quantity : 0;

        return CartridgeModalDetailsResDTO.transform(cartridge, quantity);
    }

    async updateCartridgeQuantity(
        userId: Types.ObjectId,
        cartridgeId: Types.ObjectId,
        updateCartridgeData: { quantity: number },
    ):Promise<UserCartridgeResDTO> {

        const cartridge = await this.cartridgeModelModel
            .findById(cartridgeId)
            .populate('nutrients.nutrientId')
            .lean<CartridgeWithPopulatedNutrients>();

        if(!cartridge){
            throw new NotFoundException('Cartridge modal not found');
        }

        const { quantity } = updateCartridgeData;
        const existingStock = await this.userCartridgeStockModel.findOne({
            userId,
            cartridgeId,
        });

        if (existingStock) {
            existingStock.quantity = quantity;
            await existingStock.save();
        } else {
            await this.userCartridgeStockModel.create({
                userId,
                cartridgeId,
                quantity,
            });
        }

        return {
            cartridge: {
                id:cartridge._id.toString(),
                healthName: cartridge.healthName,
                healthArea: cartridge.healthArea,
                flavour: cartridge.flavour,
                colour: cartridge.colour,
                nutrients: cartridge.nutrients.map(nutrient => ({
                    name: nutrient.nutrientId.name,
                    rda: nutrient.rda
                }))
            },
            quantity,
        };
    }

    async getCartridgeDetails(cartridgeId: string): Promise<CartridgeDetailsResDTO> {
        // Find the cartridge by cartridgeId field (string)
        const cartridge = await this.cartridgeModel
            .findOne({ cartridgeId, isDeleted: false })
            .lean();
        
        if (!cartridge) {
            throw new NotFoundException(`Cartridge with ID ${cartridgeId} not found`);
        }

        // Find the cartridge modal details with populated nutrients
        const cartridgeModal = await this.cartridgeModelModel
            .findById(cartridge.cartridgeModalId)
            .populate('nutrients.nutrientId')
            .lean<CartridgeWithPopulatedNutrients>();

        if (!cartridgeModal) {
            throw new NotFoundException('Cartridge modal not found');
        }

        return CartridgeDetailsResDTO.transform(cartridge, cartridgeModal);
    }
}
