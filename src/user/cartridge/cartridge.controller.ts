import { Body, Controller, Get, Param, Put, Request, UseGuards } from '@nestjs/common';
import { CartridgeService } from './cartridge.service';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UpdateCartridgeQuantityReqDTO, UserCartridgeResDTO } from './cartridge-dto';
import { ErrorResponse } from 'src/utils/responses';
import { AuthGuard } from 'src/middlewares';
import { Types } from 'mongoose';
import { CartridgeModalDetailsResDTO } from './cartridge-dto/cartridgeModalDetails-res.dto';


@ApiTags('Cartridge')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('cartridges')
export class CartridgeController {
  constructor(private readonly cartridgeService: CartridgeService) {}

  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all cartridges.',
    type: [UserCartridgeResDTO],
  })
  @Get('/')
  async getAllCartridges(@Request() req): Promise<UserCartridgeResDTO[]> {
    return this.cartridgeService.getAllCartridges(req.user._id);
  }

  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved cartridge modal details.',
    type: UserCartridgeResDTO,
  })
  @Get('/modal/:cartridgeId')
  async getCartridgeModalDetails(
    @Request() req,
    @Param('cartridgeId') cartridgeId: string,
  ): Promise<CartridgeModalDetailsResDTO> {
    return this.cartridgeService.getCartridgeModalDetails(
      req.user._id,
      new Types.ObjectId(cartridgeId),
    );
  }

  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully updated cartridge quantity.',
    type: UserCartridgeResDTO,
  })
  @Put('/:cartridgeId')
  async updateCartridgeQuantity(
    @Request() req,
    @Param('cartridgeId') cartridgeId: string,
    @Body() updateCartridgeData: UpdateCartridgeQuantityReqDTO,
  ) {
    return this.cartridgeService.updateCartridgeQuantity(
      req.user._id,
      new Types.ObjectId(cartridgeId),
      updateCartridgeData,
    );
  }

  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved cartridge  details.',
    type: UserCartridgeResDTO,
  })
  @Get('/:cartridgeId')
  async getCartridgeDetails(
    @Request() req,
    @Param('cartridgeId') cartridgeId: string,
  ) {
    return this.cartridgeService.getCartridgeDetails(
      cartridgeId,
    );
  }
}
