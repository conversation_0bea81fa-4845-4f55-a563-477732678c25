import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CartridgeService } from './cartridge.service';
import { CartridgeController } from './cartridge.controller';
import { CartridgeModal, CartridgeModalSchema } from 'models/cartridge/cartridgeModal.schema';
import { UserCartridgeStockModal, UserCartridgeStockSchema } from 'models/cartridge/userCartidgeStock.schema';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RedisModule } from 'src/redis/redis.module';
import { RepoModule } from 'src/repo/repo.module';
import { Nutrient, NutrientSchema } from 'models/cartridge/nutrient.schema';
import { Cartridge, CartridgeSchema } from 'models/cartridge/cartridge.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Cartridge.name, schema: CartridgeSchema },
      { name: CartridgeModal.name, schema: CartridgeModalSchema },
      { name: UserCartridgeStockModal.name, schema: UserCartridgeStockSchema },
      { name: Nutrient.name, schema: NutrientSchema },
    ]),
    CommonModule,
    AuthModule,
    RedisModule,
    RepoModule,
  ],
  controllers: [CartridgeController],
  providers: [CartridgeService],
})
export class CartridgeModule {}
