import { Body, Controller, Get, Put, Req, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ErrorResponse } from 'src/utils/responses';
import { UserService } from './user.service';
import { Request } from 'express';
import { GetUserProfileResDTO } from './user-dto';
import { AuthGuard } from 'src/middlewares';
import { CompleteUserProfileReqDTO } from './user-dto';
import { CompleteUserProfileResDTO } from './user-dto';
import { UpdateUserProfileReqDTO } from './user-dto/updateUserProfile-req.dto';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('User')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved the user profile.',
    type: GetUserProfileResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/profile')
  async getUserProfile(@Req() req: Request): Promise<GetUserProfileResDTO> {
    const user = req['user'];
    return this.userService.getUserProfile(user);
  }
  
  @ApiResponse({
    status: 200,
    description: 'Successfully completed the user profile.',
    type: CompleteUserProfileResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Put('/profile/complete')
  async completeUserProfile(
    @Body() completeUserProfileData: CompleteUserProfileReqDTO,
    @Req() req: Request,
  ) {
    const user = req['user'];

    return this.userService.completeUserProfile(user, completeUserProfileData);
  }

  @ApiResponse({
    status: 200,
    description: 'Successfully updated the user profile.',
    type: GetUserProfileResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @UseInterceptors(FileInterceptor('profilePic'))
  @Put('/profile/update')
  async updateUserProfile(
    @Req() req: Request,
    @Body() updateUserProfileData: UpdateUserProfileReqDTO,
    @UploadedFile() profilePic?: Express.Multer.File,
  ):Promise<GetUserProfileResDTO> {
    const user = req['user'];

    return this.userService.updateUserProfile(user, updateUserProfileData,req, profilePic);
  }

}
