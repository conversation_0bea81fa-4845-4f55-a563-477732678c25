import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { User, UserSchema } from 'models/user';
import { MongooseModule } from '@nestjs/mongoose';
import { CommonModule } from 'src/common/common.module';
import { AuthModule } from 'src/auth/auth.module';
import { RedisModule } from 'src/redis/redis.module';
import { RepoModule } from 'src/repo/repo.module';
import { UserDeviceModule } from '../device/device.module';
import { FileUploadModule } from 'src/file-upload/file-upload.module';
// import { DeviceModule } from '../device/device.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
    ]),
    CommonModule,
    AuthModule,
    RedisModule,
    RepoModule,
    UserDeviceModule,
    FileUploadModule,
  ],
  providers: [
    UserService,
  ],
  controllers: [UserController],
})
export class UserModule {}
