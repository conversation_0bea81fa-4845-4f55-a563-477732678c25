import { BadRequestException, HttpStatus, Injectable, UploadedFile } from '@nestjs/common';
import { GetUserProfileResDTO, UserProfileDto } from './user-dto';
import { Connection, Model } from 'mongoose';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { User } from 'models/user';
import { CompleteUserProfileReqDTO } from './user-dto/complete_profile-req.dto';
import { CompleteUserProfileResDTO } from './user-dto/completeUserProfile-res.dto';
import { plainToInstance } from 'class-transformer';
import { CreateDeviceConnectionReqDTO } from '../device/device-dto';
import { validate } from 'class-validator';
import { UserRepoService } from 'src/repo/user-repo.service';
import { UserDeviceService } from '../device/device.service';
import { UpdateUserProfileReqDTO } from './user-dto/updateUserProfile-req.dto';
import { FileUploadService } from 'src/file-upload/file-upload.service';
import { UserPopulatedProfileDTO } from './user-dto/userPopulateProfile.dto';
import { CredentialsAuthUtilsService } from 'src/auth/credentials-auth/credentials-auth-utils.service';
import { Request } from 'express';
@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name)
    private readonly userModel: Model<User>,

    private readonly userRepo: UserRepoService,
    private readonly userdeviceService: UserDeviceService,
    private readonly fileUploadService: FileUploadService,
    private readonly credentialsAuthUtilsService: CredentialsAuthUtilsService,

    @InjectConnection() private readonly connection: Connection,
  ) {}

  async getUserProfile(user: User): Promise<GetUserProfileResDTO> {

    const profilePic = await this.userRepo.getUserProfilePic(user.profilePic);
    const userResp = UserPopulatedProfileDTO.transform(user,profilePic);

    return GetUserProfileResDTO.transform(userResp);
  }

  async completeUserProfile(
    user: User,
    completeUserProfileData: CompleteUserProfileReqDTO,
  ): Promise<CompleteUserProfileResDTO> {
    try {
      const session = await this.connection.startSession();
      session.startTransaction();
      
      if (user.isAccountCompleted) {
        throw new BadRequestException('Account is already completed.');
      }

      const {
        dob,
        gender,
        height,
        weight,
        city,
        state,
        country,
        activityLevel,
        deviceData,
        timeZone,
      } = completeUserProfileData;

      const decimalPlaces = (weight.toString().split('.')[1] || '').length;
      if (decimalPlaces > 4) {
        throw new BadRequestException(
          'Weight can have maximum 4 decimal places',
        );
      }

      let connectionresp = null;

      if (deviceData) {
        const deviceDtoInstance = plainToInstance(
          CreateDeviceConnectionReqDTO,
          deviceData,
        );

        // Validate the instance
        const errors = await validate(deviceDtoInstance);

        if (errors.length > 0) {
          throw new BadRequestException(
            `${Object.values(errors[0].constraints)}`,
          );
        }

        // Call createDeviceConnection service
        connectionresp = await this.userdeviceService.createDeviceConnection(
          user,
          deviceData,
          session, 
        );
      }

      // Prepare the update data
      const completeData: any = {
        dob,
        gender,
        height,
        weight,
        city,
        state,
        country,
        activityLevel,
        isAccountCompleted: true,
        timeZone,
      };
      
      const completedUser = await this.userRepo.findUserByIdAndUpdate(
        user._id,
        completeData,
        session,
      );

      await session.commitTransaction();
      session.endSession();

      const userResp = UserProfileDto.transform(completedUser);

      if (connectionresp != null) {
        return {
          error: false,
          statusCode: HttpStatus.OK,
          msg: 'User Profile Completed !!',
          user: userResp,
        };
      }

      return {
        error: false,
        statusCode: HttpStatus.OK,
        msg: 'User Profile Completed !!',
        user: userResp,
      };
    } catch (error) {
      throw error;
    }
  }

  async updateUserProfile(
    user: User,
    updateUserProfileData: UpdateUserProfileReqDTO,
    req: Request,
    @UploadedFile() profilePic?: Express.Multer.File,
  ): Promise<GetUserProfileResDTO> {
    if (!user.isAccountCompleted) {
      throw new BadRequestException('Complete your profile first.');
    }

    try {
      const {
        firstName,
        lastName,
        dob,
        email: newEmail,
        gender,
        height,
        weight,
        city,
        state,
        country,
        activityLevel,
      } = updateUserProfileData;

      const updateData: any = {};
      let responseMessage = 'User updated successfully';
      let isEmailChanged = false;

      // ✅ Update normal fields
      if (firstName) updateData.firstName = firstName;
      if (lastName) updateData.lastName = lastName;
      if (dob) updateData.dob = dob;
      if (gender) updateData.gender = gender;
      if (height) updateData.height = height;
      if (weight) {
        const decimalPlaces = (weight.toString().split('.')[1] || '').length;
        if (decimalPlaces > 4) {
          throw new BadRequestException('Weight can have maximum 4 decimal places');
        }
        updateData.weight = weight;
      }
      if (city) updateData.city = city;
      if (state) updateData.state = state;
      if (country) updateData.country = country;
      if (activityLevel) updateData.activityLevel = activityLevel;

      // ✅ Email change request (don’t overwrite immediately)
      if (newEmail && newEmail !== user.email) {
        const existingUser = await this.userRepo.findUserByEmail(newEmail);
        if (existingUser) {
          throw new BadRequestException('Email cannot be updated as this email is already in use.');
        }

        isEmailChanged = true;
        responseMessage =
          'User updated successfully, A verification link has been sent to your email.';

        // Save it in pendingEmail instead of email
        updateData.pendingEmail = newEmail;
      }

      // ✅ Profile picture upload
      if (profilePic) {
        const type = profilePic.mimetype.split('/')[0];
        const uploadedFile = await this.fileUploadService.uploadAndStoreFile(profilePic, 'profile-pic');

        updateData.profilePic = uploadedFile._id;
      }

      // ✅ Update user record
      const updatedUser = await this.userRepo.findUserByIdAndUpdate(user._id, updateData);

      // ✅ Fetch profile pic URL
      const profilePicURL = await this.userRepo.getUserProfilePic(updatedUser.profilePic);
      const userResp = UserPopulatedProfileDTO.transform(updatedUser, profilePicURL);

      // ✅ Send verification email if email changed
      if (isEmailChanged && newEmail) {
        await this.credentialsAuthUtilsService.sendEmailUpdateEmail(user, req, newEmail);
      }

      return GetUserProfileResDTO.transform(userResp);
    } catch (error) {
      throw error;
    }
  }

  
}
