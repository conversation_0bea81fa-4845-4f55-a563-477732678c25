import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  Min,
  Max,
  IsNotEmpty,
  IsEnum,
  IsOptional,
  IsNumber,
  ValidateIf,
} from 'class-validator';
import { GENDER_TYPES } from 'models/user';
import { ActivityLevelEnum } from 'models/user/user.schema';
import { CreateDeviceConnectionReqDTO } from 'src/user/device/device-dto';
import { IsDateFormat } from '../validators/date-format-validator';

export class CompleteUserProfileReqDTO {
  @ApiProperty({
    description: 'The date of birth of the user in DD-MM-YYYY format',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  @IsDateFormat('DD-MM-YYYY', { message: 'Date of birth must be in DD-MM-YYYY format' })
  dob: string;

  @ApiProperty({
    description: 'The gender of the user (Male, Female, Other)',
    enum: GENDER_TYPES,
  })
  @IsEnum(GENDER_TYPES, {
    message: 'Invalid input for gender. Accepted values are male, female, other.',
  })
  @IsNotEmpty()
  gender: GENDER_TYPES;

  @ApiProperty({
    description: 'Country of the user',
    type: String,
    nullable: true,
  })
  @IsOptional()
  @IsString()
  country: string | null;

  @ApiProperty({
    description: 'City of the user',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiProperty({
    description: 'State of the user',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  state: string;

  @ApiProperty({
    description: 'The height of the user in centimeters',
    example: 170,
    type: Number,
  })
  @IsNumber()
  @Min(100, { message: 'Height must be greater than and equal to 100cm !!' })
  @Max(200, { message: 'Height must be less than or equal to 200cm !!' })
  @IsNotEmpty()
  height: number;

  @ApiProperty({
    description: 'The weight of the user in kilograms',
    example: 70,
    type: Number,
  })
  @IsNumber()
  @Min(30, { message: 'Weight must be greater than and equal to 30 !!' })
  @Max(300, { message: 'Weight must not exceed 300 kg.' })
  @IsNotEmpty()
  weight: number;

  @ApiProperty({
    description: 'Activity level of the user',
    enum: ActivityLevelEnum,
  })
  @IsEnum(ActivityLevelEnum, {
    message:
      'Invalid input for activity level. Accepted values: sedentary, lightlyActive, moderatelyActive, veryActive, extremelyActive.',
  })
  @IsNotEmpty()
  activityLevel: ActivityLevelEnum;

  @ApiProperty({
    description: 'Device connection details',
    type: () => CreateDeviceConnectionReqDTO,
    required: false,
  })
  @IsOptional()
  deviceData: CreateDeviceConnectionReqDTO;

  @ApiProperty({
    description: 'The time zone of the user',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  timeZone: string;
}
