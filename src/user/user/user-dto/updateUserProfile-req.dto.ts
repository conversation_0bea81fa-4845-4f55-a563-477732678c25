import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  <PERSON><PERSON><PERSON>ber,
  IsO<PERSON>al,
  IsString,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  MinLength,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { GENDER_TYPES } from 'models/user';
import { ActivityLevelEnum } from 'models/user/user.schema';
import { IsDateFormat } from '../validators/date-format-validator';

export class UpdateUserProfileReqDTO {
  @ApiProperty({
    example: 'John',
  })
  @IsString()
  @MinLength(3, { message: 'First name must be of atleast 3 letters !!' })
  @IsOptional()
  firstName: string;

  @ApiProperty({
    example: 'Doe',
  })
  @IsString()
  @MinLength(3, { message: 'Last name must be of atleast 3 letters !!' })
  @IsOptional()
  lastName: string;

  @ApiProperty({
    description: 'The date of birth of the user in DD-MM-YYYY format',
    type: String,
  })
  @IsString()
  @IsOptional()
  @IsDateFormat('DD-MM-YYYY', { message: 'Date of birth must be in DD-MM-YYYY format' })
  dob: string;

  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsOptional()
  email: string;

  @ApiProperty({
    description: 'The gender of the user (male, female, other)',
    enum: GENDER_TYPES,
  })
  @IsEnum(GENDER_TYPES, {
    message:
      'Invalid input for gender. Accepted values are male, female, other.',
  })
  @IsOptional()
  gender: GENDER_TYPES;
  

  @ApiProperty({
    description: 'Country of the user',
    type: String,
    nullable: true,
  })
  @IsOptional()
  @IsString()
  country?: string | null;

  @ApiProperty({
    description: 'City of the user',
    type: String,
  })
  @IsString()
  @IsOptional()
  city: string;

  @ApiProperty({
    description: 'State of the user',
    type: String,
  })
  @IsString()
  @IsOptional()
  state: string;
  

  @ApiProperty({
    description: 'The height of the user in centimeters',
    example: 170,
    type: Number,
  })
  @Transform(({ value }) => {
    if (value === '' || value === null || value === undefined) return undefined;
    return Number(value);
  })
  @IsNumber()
  @Min(100, { message: 'Height must be greater than and equal to 100cm !!' })
  @Max(200, { message: 'Height must be less than or equal to 200cm !!' })
  @IsOptional()
  height: number;

  @ApiProperty({
    description: 'The weight of the user in kilograms',
    example: 70,
    type: Number,
  })
  @Transform(({ value }) => {
    if (value === '' || value === null || value === undefined) return undefined;
    return Number(value);
  })
  @IsNumber()
  @Min(30, { message: 'Weight must be greater than and equal to 30 !!' })
  @Max(300, { message: 'Weight must not exceed 300 kg.' })
  @IsOptional()
  weight: number;

  @ApiProperty({
    description: 'Activity level of the user',
    enum: ActivityLevelEnum,
  })
  @IsEnum(ActivityLevelEnum, {
    message: 'Invalid input for activity level. Accepted values: sedentary, lightlyActive, moderatelyActive, veryActive, extremelyActive.',
  })
  @IsOptional()
  activityLevel: ActivityLevelEnum;
}
