import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';import { UserPopulatedProfileDTO } from './userPopulateProfile.dto';
;

export class GetUserProfileResDTO extends BaseResponse {
  @ApiProperty({
    type: String,
    description: 'success message',
    example: 'User Profile Completed !!',
  })
  msg: string;

  @ApiProperty({
    type: UserPopulatedProfileDTO,
  })
  data: UserPopulatedProfileDTO;

  static transform(user: UserPopulatedProfileDTO): GetUserProfileResDTO {
    const transformedObj = new GetUserProfileResDTO();
    transformedObj.data = user;
    return transformedObj;
  }
}
