import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsO<PERSON>al,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { ActivityLevelEnum, User } from 'models/user/user.schema';

export class UserProfileDto {
  @ApiProperty({
    example: '123',
  })
  id: string;

  @ApiProperty({
    example: 'John',
  })
  firstName: string;

  @ApiProperty({
    example: 'John',
  })
  lastName: string;

  @ApiProperty({
    example: 'https://example.com/profile-pics/profile-pic-123.jpg',
  })
  @IsOptional()
  profilePic: string | null;

  @ApiProperty({
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isEmailVerified: boolean;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isDeleted: boolean;

  @ApiProperty({
    example: true,
  })
  @IsBoolean()
  isAccountCompleted: boolean;

  @ApiProperty({
    example: '01-01-2021',
  })
  @IsOptional()
  dob: string;

  @ApiProperty({
    example: 'male',
  })
  @IsOptional()
  gender: string;

  @ApiProperty({
    example: '100',
  })
  @IsOptional()
  @Min(100)
  @Max(200)
  height: number;

  @ApiProperty({
    example: '100',
  })
  @IsOptional()
  @Min(30)
  @Max(300)
  weight: number;

  @ApiProperty({
    example: 'New York',
  })
  @IsOptional()
  city: string;

  @ApiProperty({
    example: 'New York',
  })
  @IsOptional()
  state: string;

  @ApiProperty({
    example: 'USA',
  })
  @IsOptional()
  country: string | null;

  @ApiProperty({
    example:ActivityLevelEnum.SEDENTARY,
  })
  @IsOptional()
  activityLevel: ActivityLevelEnum;

  static transform(object: User): UserProfileDto {
    const transformedObj: UserProfileDto = new UserProfileDto();

    transformedObj.id = object._id.toString();
    transformedObj.email = object.email;
    transformedObj.firstName = object.firstName;
    transformedObj.lastName = object.lastName;
    transformedObj.profilePic = (object.profilePic as any as {
      s3Url: string;
    })?.s3Url;
    transformedObj.isEmailVerified = object.isEmailVerified;
    transformedObj.isAccountCompleted = object.isAccountCompleted;
    transformedObj.isDeleted = object.isDeleted;
    transformedObj.dob = object.dob;
    transformedObj.gender = object.gender;
    transformedObj.height = object.height;
    transformedObj.weight = object.weight;
    transformedObj.city = object.city;
    transformedObj.state = object.state;
    transformedObj.country = object.country;
    transformedObj.activityLevel = object.activityLevel;

    return transformedObj;
  }
}
