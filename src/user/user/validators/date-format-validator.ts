import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import * as moment from 'moment';

export function IsDateFormat(format: string, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isDateFormat',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          return moment(value, format, true).isValid();
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid date in ${format} format`;
        },
      },
    });
  };
}
