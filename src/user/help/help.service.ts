import { HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Help } from 'models/help';
import { GetAllHelpQueryInterface } from './interfaces';
import { GetAllHelpResDto, GetSingleHelpByIdResDto, HelpDto } from './dto';
import { UtilsService } from 'src/common/services';

@Injectable()
export class HelpService {
  constructor(
    @InjectModel(Help.name)
    private readonly helpModel: Model<Help>,
    private readonly utilsService: UtilsService,
  ) {}

  async getAllHelps(
    queryFilters: GetAllHelpQueryInterface,
  ): Promise<GetAllHelpResDto> {
    const { page, title, description } = queryFilters;

    const { limit, offset } =
      this.utilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const query: any = { isDeleted: false };

    // Build array of conditions for $or operator
    const orConditions = [];

    // Ensure title is a valid string before applying regex
    if (typeof title === 'string') {
      const trimmedTitle = title.trim();

      if (trimmedTitle.length > 0) {
        try {
          orConditions.push({
            title: { $regex: new RegExp(trimmedTitle, 'i') },
          });
        } catch (error) {
          console.error('Regex Error for title:', error);
        }
      }
    }

    // Add description search capability
    if (typeof description === 'string') {
      const trimmedDescription = description.trim();
      if (trimmedDescription.length > 0) {
        try {
          orConditions.push({
            description: {
              $regex: new RegExp(trimmedDescription, 'i'),
            },
          });
        } catch (error) {
          console.error('Regex Error for description:', error);
        }
      }
    }

    // Add the $or conditions to the query if any exist
    if (orConditions.length > 0) {
      if (orConditions.length === 1) {
        // If only one condition, no need for $or
        Object.assign(query, orConditions[0]);
      } else {
        query.$or = orConditions;
      }
    }

    const helpEntries = await this.helpModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const total = await this.helpModel.countDocuments(query);
    const helpResponses = helpEntries.map((item) => HelpDto.transform(item));

    return GetAllHelpResDto.transform({
      total,
      nbHits: helpResponses.length,
      helps: helpResponses,
    });
  }

  async getHelpById(id: string): Promise<GetSingleHelpByIdResDto> {
    const help = await this.helpModel.findById(id).exec();

    if (!help || help.isDeleted) {
      throw new NotFoundException(`Help entry with ID ${id} not found`);
    }

    return {
      error: false,
      statusCode: 200,
      help: HelpDto.transform(help),
    };
  }
}
