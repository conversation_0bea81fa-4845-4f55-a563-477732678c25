import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';
import { HelpDto } from './help.dto';
import { IsArray } from 'class-validator';

class Help_data {
  @ApiProperty({ example: 100, description: 'Total number of activity videos' })
  total: number;

  @ApiProperty({
    example: 50,
    description: 'Number of hits matching the query',
  })
  nbHits: number;

  @ApiProperty({
    description: 'List of helps',
    type: [HelpDto],
  })
  @IsArray()
  helps: HelpDto[];
}
export class GetAllHelpResDto extends BaseResponse {
  @ApiProperty({
    description: 'List of helps',
    type: Help_data,
  })
  data: Help_data;

  static transform(data: any): GetAllHelpResDto {
    const transformedObj = new GetAllHelpResDto();
    transformedObj.data = data;
    return transformedObj;
  }
}
