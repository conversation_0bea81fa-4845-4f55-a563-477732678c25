export const isPrivateRoute = (url: any) => {
  if (typeof url !== 'string' || !url?.length) {
    return false;
  }

  const data = url.split('/');

  const routeName = data[3]?.toLowerCase();

  const privateRoutes = [
    'home',
    'profile',
    'caretaker-info',
    'inventory',
    'tablet-history',
    'tablet-tracking',
    'software-update',
    'privacy',
    'help-support',
    'summary',
    'shopping',
  ];

  return privateRoutes.includes(routeName);
};