import * as Linking from 'expo-linking';
import { RootStackParamList } from '../RootStack';
import { LinkingOptions } from '@react-navigation/native';

const prefix = Linking.createURL('/');

export const linkingConfig: LinkingOptions<RootStackParamList> = {
    prefixes: [prefix],
    config: {
        screens: {
            // Root Stack Navigator
            Splash: 'splash',
            Auth: {
                screens: {
                    Login: 'login',
                    Register: 'register',
                    ForgotPassword: 'forgot-password',
                    ResendVerificationEmail: 'resend-verification',
                }
            },
            Onboarding: 'onboarding',
            Main: {
                screens: {
                    // Bottom Tab Navigator
                    Summary: 'summary',
                    HomeStack: {
                        screens: {
                            // Main Stack screens
                            Home: 'home',
                            'Personal Information': 'profile',
                            'Caretaker Info': 'caretaker-info',
                            Inventory: 'inventory',
                            TabletHistory: 'tablet-history',
                            TabletTracking: 'tablet-tracking',
                            'Software Update': 'software-update',
                            Privacy: 'privacy',
                            'Help & Support': 'help-support',
                        }
                    },
                    Shopping: 'shopping',
                }
            },
        },
    },
};

const routeMapping: Record<string, string>  = {
    // Root Stack
    'splash': 'Splash',
    'no-internet': 'NoInternet',
    'no-server': 'NoServer',
    'onboarding': 'Onboarding',
    
    // Auth Stack
    'login': 'Login',
    'register': 'Register',
    'forgot-password': 'ForgotPassword',
    'resend-verification': 'ResendVerificationEmail',
    
    // Main Stack (nested in HomeStack tab)
    'home': 'Home',
    'profile': 'Personal Information',
    'caretaker-info': 'Caretaker Info',
    'inventory': 'Inventory',
    'tablet-history': 'TabletHistory',
    'tablet-tracking': 'TabletTracking',
    'software-update': 'Software Update',
    'privacy': 'Privacy',
    'help-support': 'Help & Support',
    
    // Bottom Tab screens
    'summary': 'Summary',
    // 'shopping': 'Shopping',
};

const linkToAppRouteName = (url: string) => {
    const routeName = url.split('://')[1];
    return routeMapping[routeName] || 'Home';
};

export { linkToAppRouteName };


// import {Linking} from 'react-native';
// import {isPrivateRoute} from '../utils/isPrivateRoute';
// import { LinkingOptions } from '@react-navigation/native';
// import { RootStackParamList } from '../RootStack';

// const linkingConfig: LinkingOptions<RootStackParamList> = {
//   prefixes: ['nouriq://app'],
  
//   config: {
//     screens: {
//       Splash: 'splash',
//       Auth: {
//         screens: {
//           Login: 'login',
//           Register: 'register',
//           ForgotPassword: 'forgot-password',
//           ResendVerificationEmail: 'resend-verification',
//         }
//       } ,
//       Onboarding: 'onboarding',
//       Main: {
//         screens: {
//           Summary: 'summary',
//           HomeStack: {
//             screens: {
//               Home: 'home',
//               'Personal Information': 'profile',
//               'Caretaker Info': 'caretaker-info',
//               Inventory: 'inventory',
//               TabletHistory: 'tablet-history',
//               TabletTracking: 'tablet-tracking',
//               'Software Update': 'software-update',
//               Privacy: 'privacy',
//               'Help & Support': 'help-support',
//             }
//           },
//           Shopping: 'shopping',
//         }
//       } as any,
//     },
//   },
  
//   async getInitialURL() {
//     try {
//       const url = await Linking.getInitialURL();
      
//       if (isPrivateRoute(url || '')) {
//         return null;
//       }
      
//       return url;
//     } catch (error) {
//       return null;
//     }
//   },
  
//   subscribe(listener: any) {
//     const linkingSubscription = Linking.addEventListener('url', ({url}) => {
//       try {
//         if (isPrivateRoute(url || '')) {
//           return null;
//         }
        
//         listener(url);
//       } catch (error) {
//         return null;
//       }
//     });
    
//     return () => {
//       linkingSubscription.remove();
//     };
//   },
// };

// export default linkingConfig;
