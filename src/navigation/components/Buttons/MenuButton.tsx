import { StyleSheet, Text, TouchableOpacity, View, Animated } from 'react-native'
import React, { useState, useEffect, useRef } from 'react'
import { useTheme } from '../../../context/ThemeContext';
import { AppFonts } from '../../../constants/theme/fonts/fonts';

type MenuButtonProps = {
    label: string,
    routeName: string,
    onPress: () => void,
    ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => React.ReactNode,
    currentRouteName: string,
}

const MenuButton = ({
    label,
    routeName,
    onPress,
    ActiveIcon,
    currentRouteName
}: MenuButtonProps) => {
    const { theme } = useTheme();
    const isActive = routeName == currentRouteName;

    const [pressedIn, setPressedIn] = useState(false);
    
    // Animation values
    const backgroundColorAnim = useRef(new Animated.Value(isActive ? 1 : 0)).current;
    const textColorAnim = useRef(new Animated.Value(isActive ? 1 : 0)).current;
    const IconOpacity = useRef(new Animated.Value(isActive ? 1 : 0)).current;

    // Animation duration
    const animationDuration = 200;

    // Update animations when state changes
    useEffect(() => {
        const targetValue = (isActive || pressedIn) ? 1 : 0;
        
        Animated.parallel([
            Animated.timing(backgroundColorAnim, {
                toValue: targetValue,
                duration: animationDuration,
                useNativeDriver: false,
            }),
            Animated.timing(textColorAnim, {
                toValue: targetValue,
                duration: animationDuration,
                useNativeDriver: false,
            }),
            Animated.timing(IconOpacity, {
                toValue: targetValue,
                duration: animationDuration,
                useNativeDriver: false,
            })
        ]).start();
    }, [isActive, pressedIn]);

    // Interpolate colors
    const animatedBackgroundColor = backgroundColorAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [theme.button.secondary, theme.button.primary]
    });

    const animatedTextColor = textColorAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [theme.text.primary, theme.text.secondary]
    });

    const animatedIconOpacity = IconOpacity.interpolate({
        inputRange: [0, 1],
        outputRange: [0, 1]
    });

    return (
        <View style={styles.menuItemWrapper}>
            <ActiveIcon opacity={animatedIconOpacity}/>
            <TouchableOpacity
                activeOpacity={.8}
                style={styles.touchableArea}
                onPress={onPress}
                onPressIn={() => setPressedIn(true)}
                onPressOut={() => setPressedIn(false)}
            >
                <Animated.View style={[
                    styles.menuItem,
                    { backgroundColor: animatedBackgroundColor }
                ]}>
                    <Animated.Text style={[
                        styles.menuItemText,
                        { color: animatedTextColor }
                    ]}>
                        {label}
                    </Animated.Text>
                </Animated.View>
            </TouchableOpacity>
        </View>
    )
}

export default MenuButton

const styles = StyleSheet.create({
    menuItemWrapper: {
        marginBottom: 16,
    },
    touchableArea: {
        // Separate touchable area to avoid style conflicts
    },
    menuItem: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 8,
        borderRadius: 100,
        elevation: 1,
    },
    menuItemText: {
        fontSize: 18,
        flex: 1,
        fontFamily: AppFonts.HelixaBold,
        textAlign: "right",
    },
})