import { <PERSON><PERSON>, BackHandler, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { memo, useCallback, useEffect, useState } from 'react'
import { useTheme } from '../../../context/ThemeContext';
import { useNavigation, useRoute } from '@react-navigation/native';
import BackBtn from '../../../components/Buttons/BackBtn';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import IconBtn from '../../../components/Buttons/IconBtn';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { AppFonts } from '../../../constants/theme/fonts/fonts';
import useBluetoothStore from '../../../store/BluetoothStore';
import LocationPermissionModal from '../../../components/Modals/Bluetooth/LocationPermissionModal';
import BluetoothPermissionModal from '../../../components/Modals/Bluetooth/BluetoothPermissionModal';
import BluetoothDeviceModal from '../../../components/Modals/Bluetooth/BluetoothDeviceModal';
import MenuDrawer from '../Drawer/MenuDrawer';
import useAppStateStore from '../../../store/AppStateStore';
import { BlurView } from '@react-native-community/blur';
import resetNavigate from '../../utils/resetNavigate';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { MainStackParamList } from '../../MainStack';

const AppLogo = require('../../../../assets/app_logo_resized.png')

type MenuDrawerNavigationProp = NativeStackNavigationProp<MainStackParamList>;

const AppLayout = ({ children }: { children: React.ReactNode }) => {
  const { theme } = useTheme();
  const navigation = useNavigation<MenuDrawerNavigationProp>();
  const insets = useSafeAreaInsets();
  const route = useRoute();
  const isConnected = useBluetoothStore(state => state.isConnected);
  
  const [ showBluetoothConnectionModal, setShowBluetoothConnectionModal ] = useState(false);

  const showMenu = useAppStateStore(state=>state.showMenu);
  const setShowMenu = useAppStateStore(state=>state.setShowMenu);

  const handleMenuPress = () => {
    setShowMenu(true);
  }
  const handleBottleConnection = () => {
    setShowBluetoothConnectionModal(true);
  }

  const handleBackPress = () => {
    if(showMenu){
      setShowMenu(false);
      return true;
    }

    const currentRoute = navigation.getState().routes[navigation.getState().index].name;

    if (navigation.canGoBack() && currentRoute !== 'Home') {
        navigation.goBack();
        return true;
    }
    else if (!navigation.canGoBack() && currentRoute !== 'Home') {
        resetNavigate(navigation, 'Home');
        return true;
    }
    else {
      Alert.alert(
        "Exit App",
        "Are you sure you want to exit?",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Exit", onPress: () => BackHandler.exitApp() }
        ]
      );
      return true;
    }
  }

  useEffect(() => {
    const backHandler = BackHandler.addEventListener("hardwareBackPress", handleBackPress);

    return () => backHandler.remove();
  }, [handleBackPress]);
        

  const handleOutsidePress = () => {
    setShowMenu(false);
  }

  const handleGetHeaderName = useCallback(() => {
    const mapping = {
      "Tablet All Details": "Tablet Tracking",
    }

    return mapping?.[route.name as keyof typeof mapping] || route.name;
  },[route.name]);

  return (
      <View style={styles.wrapper}>
        <View style={[styles.container, {
          paddingTop: insets.top + 16,
          // paddingBottom: insets.bottom + 16,
          paddingLeft: insets.left + 16,
          paddingRight: insets.right + 16,
          backgroundColor: theme.background.secondary
        }]}>
            <View style={styles.headerContainer}>
            {
              route.name != "Home" ? <BackBtn onPress={handleBackPress} containerStyle={{ left: 0, right: "auto" }} /> : (
                <IconBtn
                  icon={
                    <Feather name="bluetooth" size={18} color={theme.icon.tertiary} />
                  }
                  onPress={handleBottleConnection}
                  containerStyle={{
                    left: 0, right: "auto", padding: 14, paddingHorizontal: 14,
                  }}
                  containerBgColor={isConnected ? 'primary' : "quaternary"}
                />
              )
            }
            <Image source={AppLogo} style={styles.appLogoImage} resizeMode='contain' />
            <Text style={[styles.screenNameText, { color: theme.text.quaternary }]}>{
              handleGetHeaderName()
            }</Text>
            <IconBtn
              icon={<MaterialIcons name="menu" size={24} color={theme.icon.secondary} />}
              onPress={() => handleMenuPress()}
              containerStyle={{ right: 0, left: "auto", padding: 10, }}
            />
            </View>
            {children}
            <BluetoothDeviceModal visible={showBluetoothConnectionModal} onClose={() => setShowBluetoothConnectionModal(false)}/>
            <BluetoothPermissionModal />
            <LocationPermissionModal />
          </View>

          {(showMenu) && (
            <TouchableOpacity
              style={styles.overlay}
              activeOpacity={1}
              onPress={handleOutsidePress}
            >
              <BlurView
                style={styles.backgroundBlurBg}
                blurType="light"   
                blurAmount={2}
                reducedTransparencyFallbackColor={theme.background.secondary} 
              />
            </TouchableOpacity>
          )}

        <MenuDrawer/>
      </View>
  )
}

export default memo(AppLayout)

const styles = StyleSheet.create({
  wrapper:{
    flex:1,
  },
  container: {
    flex: 1
  },
  headerContainer: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center"
  },
  appLogoImage: {
    width: '30%',
    height: "auto",
    aspectRatio: 16 / 5
  },
  screenNameText: {
    fontSize: 20,
    fontFamily: AppFonts.HelixaBold,
    textAlign: "center",
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.3)',
    zIndex:10
  },
  backgroundBlurBg:{
    width:"100%",
    height:"100%",
    zIndex:1
  }
})