import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme } from "../../../context/ThemeContext";
import { Animated, StyleSheet, Text, View } from "react-native";
import IconBtn from "../../../components/Buttons/IconBtn";
import { AntDesign, Entypo, FontAwesome6, Ionicons } from "@expo/vector-icons";
import UpgradeIcon from "../../../components/Icons/MenuIconSvgs/UpgradeIcon";
import PrivacyIcon from "../../../components/Icons/MenuIconSvgs/PrivacyIcon";
import BackBtn from "../../../components/Buttons/BackBtn";
import { DrawerContentScrollView } from "@react-navigation/drawer";
import MenuButton from "../Buttons/MenuButton";
import { AppFonts } from "../../../constants/theme/fonts/fonts";
import Toast from "react-native-toast-message";

function CustomDrawerContent(props: any) {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const currentRouteName = props.state.routes[props.state.index].name;

  const menuItems = [
    {
      label: 'Profile', routeName: "Personal Information", onPress: () => props.navigation.navigate('Personal Information'), 
      ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View 
          style={[styles.animatedIconContainer,
            {opacity:opacity}
          ]}
        >
          <IconBtn
            icon={<FontAwesome6 name="hand-holding-heart" size={16} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
          />
        </Animated.View>
      )
    },
    {
      label: 'Caretaker', routeName: "Caretaker Info", onPress: () => props.navigation.navigate('Caretaker Info'), 
      ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
          ]}
        >
          <IconBtn
            icon={<FontAwesome6 name="hand-holding-heart" size={16} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
          />
        </Animated.View>
      )
    },
    {
      label: 'Inventory', routeName: "Inventory", onPress: () => props.navigation.navigate('Inventory'), 
       ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
          ]}
        >
          <IconBtn
            icon={<Ionicons name="cube" size={18} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
          />
        </Animated.View>
       )
    },
    {
      label: 'Tablet History', routeName: "TabletHistory", onPress: () => props.navigation.navigate('TabletHistory'), 
      ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
          ]}
        >
          <IconBtn
            icon={<FontAwesome6 name="hand-holding-heart" size={16} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
          />
        </Animated.View>
      )
    },
    {
      label: 'Software upgrade', routeName: "Software Update", onPress: () => props.navigation.navigate('Software Update'), ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
          ]}
        >
          <IconBtn
            icon={<UpgradeIcon width={18} height={18} fill={"primary"} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
          />
        </Animated.View>
      )
    },
    {
      label: 'Privacy', routeName: "Privacy", onPress: () => props.navigation.navigate('Privacy'), ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
          ]}
        >
          <IconBtn
            icon={<PrivacyIcon width={19} height={19} fill={"primary"} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
          />
        </Animated.View>
      )
    },
    {
      label: 'Your Orders', routeName: "YourOrders", onPress: () => Toast.show({
        type: 'error',
        text1: 'Coming Soon',
        text2: "This feature is coming soon",
        position: 'bottom'
      }), ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
          ]}
        >
          <IconBtn
            icon={<Entypo name="shopping-cart" size={18} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
          />
        </Animated.View>
      )
    },
    {
      label: 'Help', routeName: "Help & Support", onPress: () => props.navigation.navigate('Help & Support'), 
      ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
          ]}
        >
          <IconBtn
            icon={<AntDesign name="questioncircleo" size={20} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
          />
        </Animated.View>
      )
    },
  ];

  return (
    <View style={[styles.container,
    {
      paddingTop: insets.top + 16,
      paddingBottom: insets.bottom + 16,
      paddingLeft: insets.left + 16,
      paddingRight: insets.right + 16,
      backgroundColor: theme.background.secondary
    }
    ]}>
      <View style={styles.header}>
        <BackBtn onPress={props.navigation.closeDrawer} containerStyle={{ left: 0, right: "auto" }} />
        <Text style={styles.headerTitle}>Menu</Text>
      </View>

      {/* Menu Items */}
      <DrawerContentScrollView
        {...props}
        style={styles.scrollView}
        contentContainerStyle={{ paddingTop: 24, paddingLeft: 0, paddingRight: 0, paddingBottom: 0, paddingStart: 0, paddingEnd: 0 }}
      >
        {menuItems.map((item, index) => (
          <MenuButton
            key={index}
            label={item.label}
            routeName={item.routeName}
            onPress={item.onPress}
            ActiveIcon={item.ActiveIcon}
            currentRouteName={currentRouteName}
          />
        ))}
      </DrawerContentScrollView>
    </View>
  );
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
  },
  headerTitle: {
    flex: 1,
    fontSize: 36,
    textAlign: 'right',
    fontFamily: AppFonts.HelixaBold
  },
  scrollView: {
    flex: 1,
  },
  animatedIconContainer: {
    position: "absolute",
    top: "50%",
    left: 4,
    transform: [{ translateY: '-50%' }],
    zIndex:10,
    borderRadius:50,
  },
  activeIcon:{ 
    position:"relative",
    top: "auto",
    left: 0,
    transform: [{ translateY: '0%' }],
    zIndex:10,
    elevation:0,
    borderRadius:50,
    padding: 6,
    paddingHorizontal: 6 
  }
});


export default CustomDrawerContent;