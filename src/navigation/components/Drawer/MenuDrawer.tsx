import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme } from "../../../context/ThemeContext";
import { Animated, Dimensions, ScrollView, StyleSheet, Text, View } from "react-native";
import IconBtn from "../../../components/Buttons/IconBtn";
import { AntDesign, Entypo, FontAwesome6, Ionicons } from "@expo/vector-icons";
import UpgradeIcon from "../../../components/Icons/MenuIconSvgs/UpgradeIcon";
import PrivacyIcon from "../../../components/Icons/MenuIconSvgs/PrivacyIcon";
import BackBtn from "../../../components/Buttons/BackBtn";
import MenuButton from "../Buttons/MenuButton";
import { AppFonts } from "../../../constants/theme/fonts/fonts";
import { useNavigation } from "@react-navigation/native";
import { MainStackParamList } from "../../MainStack";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import useAppStateStore from "../../../store/AppStateStore";
import resetNavigate from "../../utils/resetNavigate";
import { memo, useCallback, useEffect, useState } from "react";
import Toast from "react-native-toast-message";
import DarkLightToggleBtn from "../../../components/Buttons/DarkLightToggleBtn";

type MenuDrawerNavigationProp = NativeStackNavigationProp<MainStackParamList>;

const MenuDrawer = () => {

    const navigation = useNavigation<MenuDrawerNavigationProp>();
    const { theme } = useTheme();
    const insets = useSafeAreaInsets();
    const currentRouteName = navigation.getState().routes[navigation.getState().index].name;
    const setShowMenu = useAppStateStore(state=>state.setShowMenu);
    const showMenu = useAppStateStore(state=>state.showMenu);

    const [menuSlideAnim] = useState(new Animated.Value(Dimensions.get('window').width));

    const handleMenuNavigation = useCallback((screen:string) => {
        requestAnimationFrame(() => {
            setShowMenu(false);

            const state = navigation.getState();
            const currentRoute = state.routes[state.index];

            if (currentRoute.name === screen) {
                return;
            }

            setTimeout(() => {
                resetNavigate(navigation, screen);
            }, 300);
        });
    }, [navigation, setShowMenu]);


    useEffect(() => {
        Animated.timing(menuSlideAnim, {
            toValue: showMenu ? 0 : Dimensions.get('window').width,
            duration: 300,
            useNativeDriver: false,
        }).start();
    }, [showMenu]);
    

    const menuItems = [
    {
        label: 'Profile', routeName: "Personal Information", onPress: () => handleMenuNavigation("Personal Information"), 
        ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View 
            style={[styles.animatedIconContainer,
            {opacity:opacity}
            ]}
        >
            <IconBtn
            icon={<FontAwesome6 name="hand-holding-heart" size={16} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
            />
        </Animated.View>
        )
    },
    {
        label: 'Caretaker', routeName: "Caretaker Info", onPress: () => handleMenuNavigation("Caretaker Info"), 
        ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
            ]}
        >
            <IconBtn
            icon={<FontAwesome6 name="hand-holding-heart" size={16} color={theme.icon.primary} />}
                containerStyle={styles.activeIcon}
                containerBgColor='secondary'
                onPress={() => { }}
                disabled
            />
        </Animated.View>
        )
    },
    {
        label: 'Inventory', routeName: "Inventory", onPress: () => handleMenuNavigation('Inventory'), 
        ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
            ]}
        >
            <IconBtn
            icon={<Ionicons name="cube" size={18} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
            />
        </Animated.View>
        )
    },
    {
        label: 'Tablet History', routeName: "Tablet History", onPress: () => handleMenuNavigation('Tablet History'), 
        ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
            ]}
        >
            <IconBtn
            icon={<FontAwesome6 name="hand-holding-heart" size={16} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
            />
        </Animated.View>
        )
    },
    {
        label: 'Nutrient Summary', routeName: "Nutrient Summary", onPress: () => handleMenuNavigation('Nutrient Summary'), 
        ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
            ]}
        >
            <IconBtn
            icon={<FontAwesome6 name="hand-holding-heart" size={16} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
            />
        </Animated.View>
        )
    },
    {
        label: 'Software upgrade', routeName: "Software Update", onPress: () => handleMenuNavigation('Software Update'), ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
            ]}
        >
            <IconBtn
            icon={<UpgradeIcon width={18} height={18} fill={"primary"} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
            />
        </Animated.View>
        )
    },
    {
        label: 'Privacy', routeName: "Privacy", onPress: () => handleMenuNavigation('Privacy'), ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
            ]}
        >
            <IconBtn
            icon={<PrivacyIcon width={19} height={19} fill={"primary"} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => {}}
            disabled
            />
        </Animated.View>
        )
    },
    {
        label: 'Your Orders', routeName: "YourOrders", onPress: () => {
                Toast.show({
                    type: 'error',
                    text1: 'Coming Soon',
                    text2: "This feature is coming soon",
                    position: 'bottom'
                });
        }, ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
            ]}
        >
            <IconBtn
            icon={<Entypo name="shopping-cart" size={18} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
            />
        </Animated.View>
        )
    },
    {
        label: 'Help', routeName: "Help & Support", onPress: () => handleMenuNavigation('Help & Support'), 
        ActiveIcon: ({opacity}:{opacity:Animated.AnimatedInterpolation<string | number>}) => (
        <Animated.View style={[styles.animatedIconContainer,
            {opacity:opacity}
            ]}
        >
            <IconBtn
            icon={<AntDesign name="questioncircleo" size={20} color={theme.icon.primary} />}
            containerStyle={styles.activeIcon}
            containerBgColor='secondary'
            onPress={() => { }}
            disabled
            />
        </Animated.View>
        )
    },
    ];
    
  return (
    <Animated.View style={[styles.container,
    {
      paddingTop: insets.top + 16,
      paddingBottom: insets.bottom + 16,
      paddingLeft: insets.left + 16,
      paddingRight: insets.right + 16,
      backgroundColor: theme.background.secondary,
      transform: [{ translateX: menuSlideAnim }]
    }
    ]}>
        <View style={styles.header}>
            <BackBtn onPress={() => setShowMenu(false)} containerStyle={{ left: 0, right: "auto" }} />
            <Text style={styles.headerTitle}>Menu</Text>
        </View>

        {/* <DarkLightToggleBtn/> */}

        {/* Menu Items */}
        <ScrollView
            style={styles.scrollView}
            contentContainerStyle={{ paddingTop: 24, paddingLeft: 0, paddingRight: 0, paddingBottom: 0, paddingStart: 0, paddingEnd: 0 }}
        >
            {menuItems.map((item, index) => (
            <MenuButton
                key={index}
                label={item.label}
                routeName={item.routeName}
                onPress={item.onPress}
                ActiveIcon={item.ActiveIcon}
                currentRouteName={currentRouteName}
            />
            ))}
        </ScrollView>
    </Animated.View>
  )
}

export default memo(MenuDrawer);


const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    top: 0,
    bottom: 0,
    right:0,
    width: Dimensions.get('window').width*.82,
    zIndex: 20,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
        width: 5,
        height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    borderTopStartRadius: 25,
    borderBottomLeftRadius: 25,
  },
  header: {
    flexDirection: 'row',
  },
  headerTitle: {
    flex: 1,
    fontSize: 40,
    textAlign: 'right',
    fontFamily: AppFonts.HelixaBold
  },
  scrollView: {
    flex: 1,
  },
  animatedIconContainer: {
    position: "absolute",
    top: "50%",
    left: 4,
    transform: [{ translateY: '-50%' }],
    zIndex:10,
    borderRadius:50,
  },
  activeIcon:{ 
    position:"relative",
    top: "auto",
    left: 0,
    transform: [{ translateY: '0%' }],
    zIndex:10,
    elevation:0,
    borderRadius:50,
    padding: 6,
    paddingHorizontal: 6 
  }
});
