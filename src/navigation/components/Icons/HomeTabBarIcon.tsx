import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { useTheme } from '../../../context/ThemeContext';
import { MaterialIcons } from '@expo/vector-icons';

const HomeTabBarIcon = ({focused}:{focused:boolean}) => {
    const { theme } = useTheme();
  return (
    <View style={[styles.container,{backgroundColor:theme.background.secondary}]}>
        <MaterialIcons name="home" size={36} color={focused?theme.icon.secondary:theme.icon.quaternary} />
    </View>
  )
}

export default HomeTabBarIcon

const styles = StyleSheet.create({
    container:{
        padding:6,
        borderRadius:100,
        elevation:1
    }
})