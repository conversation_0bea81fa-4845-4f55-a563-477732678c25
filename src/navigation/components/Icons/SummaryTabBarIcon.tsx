import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { useTheme } from '../../../context/ThemeContext';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const SummaryTabBarIcon = ({focused}:{focused:boolean}) => {
    const { theme } = useTheme();
  return (
    <View style={[styles.container,{backgroundColor:theme.background.secondary}]}>
        <MaterialCommunityIcons name="signal-cellular-3" size={36} color={focused?theme.icon.secondary:theme.icon.quaternary} />
    </View>
  )
}

export default SummaryTabBarIcon

const styles = StyleSheet.create({
    container:{
        padding:6,
        borderRadius:100,
        elevation:1
    }
})