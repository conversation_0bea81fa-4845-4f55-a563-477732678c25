import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { useCallback, memo } from "react";
import { StyleSheet, View } from "react-native";
import { PlatformPressable } from "@react-navigation/elements";
import SummaryScreen from "../screens/Summary/SummaryScreen";
import MainStack from "./MainStack";
import SummaryTabBarIcon from "./components/Icons/SummaryTabBarIcon";
import InventoryTabBarIcon from "./components/Icons/InventoryTabBarIcon";
import HomeTabBarIcon from "./components/Icons/HomeTabBarIcon";
import useAppStateStore from "../store/AppStateStore";
import Toast from "react-native-toast-message";
import { useTheme } from "../context/ThemeContext";
import { navigationRef, navigate, getCurrentRoute } from "./navigationRef"; // generic ref
import resetNavigate from "./utils/resetNavigate";
import { useNavigation } from "@react-navigation/native";

const Tab = createBottomTabNavigator();

const ShoppingScreen = () => null;

const MyTabBar = memo(function MyTabBar({ state, descriptors, navigation }: any) {
  const { theme } = useTheme();

  return (
    <View style={[styles.tabBarStyleWrapper]}>
      <View style={[styles.tabBarStyleContainer, { backgroundColor: theme.background.primary }]}>
        {state.routes.map((route: any, index: any) => {
          const { options } = descriptors[route.key];
          const tabBarIcon = options.tabBarIcon;
          const isFocused = state.index === index;

          const onPress = () => {
            const event = navigation.emit({
              type: "tabPress",
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name, route.params);
            }
          };

          const onLongPress = () => {
            navigation.emit({
              type: "tabLongPress",
              target: route.key,
            });
          };

          return (
            <PlatformPressable
              key={route.key}
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarButtonTestID}
              onPress={onPress}
              onLongPress={onLongPress}
              android_ripple={{ color: "transparent" }}
            >
              {tabBarIcon({ focused: isFocused })}
            </PlatformPressable>
          );
        })}
      </View>
    </View>
  );
});

const MainBottomNav = () => {
  const showMenu = useAppStateStore((state) => state.showMenu);
  const setShowMenu = useAppStateStore((state) => state.setShowMenu);
  const navigation = useNavigation<any>();

  const handleTabBarPressed = useCallback((e: any) => {
    e.preventDefault();

    if (showMenu) setShowMenu(false);

    const currentRouteName = getCurrentRoute()?.name;

    if (currentRouteName !== "HomeStack") {
      navigate("HomeStack", { screen: "Home" });
      // resetNavigate(navigation, "Home");
    }
  }, [showMenu, setShowMenu]);

  return (
    <Tab.Navigator
      initialRouteName="HomeStack"
      screenOptions={{
        headerShown: false,
        tabBarHideOnKeyboard: true,
        lazy: true,
        freezeOnBlur: true,
      }}
      backBehavior="none"
      tabBar={(props) => <MyTabBar {...props} />}
    >
      <Tab.Screen
        name="Summary"
        component={SummaryScreen}
        options={{
          tabBarShowLabel: false,
          tabBarIcon: ({ focused }) => <SummaryTabBarIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="HomeStack"
        component={MainStack}
        options={{
          tabBarShowLabel: false,
          tabBarIcon: ({ focused }) => <HomeTabBarIcon focused={focused} />,
        }}
        listeners={{
          tabPress: (e) => handleTabBarPressed(e),
        }}
      />
      <Tab.Screen
        name="Shopping"
        component={ShoppingScreen}
        options={{
          tabBarShowLabel: false,
          tabBarIcon: ({ focused }) => <InventoryTabBarIcon focused={focused} />,
        }}
        listeners={{
          tabPress: (e) => {
            e.preventDefault();
            Toast.show({
              type: "error",
              text1: "Coming Soon",
              text2: "This feature is coming soon",
              position: "bottom",
            });
          },
        }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBarStyleWrapper: {
    position: "absolute",
    bottom: 10,
    left: 16,
    right: 16,
  },
  tabBarStyleContainer: {
    flexDirection: "row",
    justifyContent: "space-evenly",
    alignItems: "center",
    paddingVertical: 8,
    width: "100%",
    elevation: 2,
    borderRadius: 100,
  },
});

export default MainBottomNav;
