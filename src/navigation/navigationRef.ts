import { createNavigationContainerRef, CommonActions } from '@react-navigation/native';

// Generic navigation ref
export const navigationRef = createNavigationContainerRef<any>();

// Navigate to a screen
export function navigate(name: string, params?: object) {
  if (navigationRef.isReady() && navigationRef.current) {
    // Use `as any` to bypass TS
    (navigationRef.current as any).navigate(name, params);
  } else {
    console.warn('Navigation attempted before navigator was ready');
  }
}

// Reset navigation stack
export function reset(name: string, params?: object) {
  if (navigationRef.isReady() && navigationRef.current) {
    (navigationRef.current as any).dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name, params }],
      })
    );
  }
}

// Go back
export function goBack() {
  if (navigationRef.isReady() && navigationRef.current?.canGoBack()) {
    (navigationRef.current as any).goBack();
  }
}

// Get current route
export function getCurrentRoute() {
  return navigationRef.isReady() && navigationRef.current
    ? (navigationRef.current as any).getCurrentRoute()
    : null;
}
