import { createNativeStackNavigator } from "@react-navigation/native-stack";

import AuthStack, { AuthStackParamList } from "./AuthStack";
import OnboardingScreen from "../screens/Onboarding/OnboardingScreen";
import MainBottomNav, { MainTabParamList } from "./MainBottomNav";
import { useTheme } from "../context/ThemeContext";
import AppBoot from "./AppBoot";
import ConnectivityWrapper from "../components/Wrapper/ConnectivityWrapper";
import { NavigatorScreenParams } from "@react-navigation/native";
import useUserStore from "../store/UserStore";
import useAppStateStore from "../store/AppStateStore";

export type RootStackParamList = {
  Splash: undefined;
  Onboarding: undefined;
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Main: NavigatorScreenParams<MainTabParamList>;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const NavigationWrapper = () => {
  const { theme } = useTheme();

  const isAuthenticated = useUserStore((state) => state.isAuthenticated);
  const isAccountCompleted = useUserStore((state) => state.isAccountCompleted);
  const splashDone = useAppStateStore((state) => state.splashDone);

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "fade",
        contentStyle: { backgroundColor: theme.background.primary },
      }}
      initialRouteName="Splash"
    >
      {!splashDone && (
        <Stack.Screen name="Splash" component={AppBoot} />
      )}
      {!isAuthenticated && (
        <Stack.Screen name="Auth" component={AuthStack} />
      )}
      {(isAuthenticated && !isAccountCompleted) && (
        <Stack.Screen name="Onboarding" component={OnboardingScreen} />
      )}
      {
        (isAuthenticated && isAccountCompleted) && (
           <Stack.Screen name="Main" component={
            ()=>(
              <ConnectivityWrapper>
                <MainBottomNav/>
              </ConnectivityWrapper>
            )
          } />
        )
      }
    </Stack.Navigator>
  );
};

export { NavigationWrapper as RootStack };
