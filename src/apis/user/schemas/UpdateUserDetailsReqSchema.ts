
import { z, infer as zInfer } from 'zod';

const updateUserDetailsReqSchema = z.object({
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    dob: z.string().optional(),
    email: z.string().optional(),
    gender: z.string().optional(),
    height: z.number().optional(),
    weight: z.number().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    country: z.string().nullable().optional(),
    activityLevel: z.string().optional(),
    profilePic: z.instanceof(File).optional(),
});

export type UpdateUserDetailsReqType = zInfer<typeof updateUserDetailsReqSchema>;