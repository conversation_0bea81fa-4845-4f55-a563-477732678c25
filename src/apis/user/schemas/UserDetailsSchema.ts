
import { z, infer as zInfer } from 'zod';

const userDetailsResSchema = z.object({
    id: z.string(),
    email: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    isEmailVerified: z.boolean(),
    isAccountCompleted: z.boolean(),
    profilePic: z.string().nullable(),
    isDeleted: z.boolean(),
    dob: z.string().optional(),
    gender: z.string().optional(),
    height: z.number().optional(),
    weight: z.number().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    country: z.string().nullable(),
    activityLevel: z.string().optional(),
});

export default userDetailsResSchema;

export type UserDetailsResType = zInfer<typeof userDetailsResSchema>;