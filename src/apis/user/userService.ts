import { ResponseErrorType } from "../../types/ResponseErrorType";
import { ResponseSuccessType } from "../../types/ResponseSuccessType";
import apiClient, { createErrorResponse } from "../axios/axiosInstance";
import { UpdateUserDetailsReqType } from "./schemas/UpdateUserDetailsReqSchema";
import updateUserDetailsResSchema, { UpdateUserDetailsResType } from "./schemas/UpdateUserDetailsResSchema";
import userDetailsResSchema, { UserDetailsResType } from "./schemas/UserDetailsSchema";


const userService = {
    getUserProfile: async (): Promise<ResponseSuccessType<UserDetailsResType> | ResponseErrorType> => {
        try {
            const response = await apiClient.get(`/profile`);

            const getUserProfileRes = userDetailsResSchema.safeParse(response.data?.data);

            if (!getUserProfileRes.success) {
                throw new Error("Unable to fetch user profile. Please try again later.");
            }

            return {
                success: true,
                data: getUserProfileRes.data
            };

        } catch (error) {
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    }
    ,
    updateUserProfile: async (updateUserProfileData:UpdateUserDetailsReqType):Promise<ResponseSuccessType<UpdateUserDetailsResType> | ResponseErrorType> => {
        try {
            // Convert the typed object to FormData
            const formData = new FormData();
            
            // Add text fields
            if (updateUserProfileData.firstName) formData.append('firstName', updateUserProfileData.firstName);
            if (updateUserProfileData.lastName) formData.append('lastName', updateUserProfileData.lastName);
            if (updateUserProfileData.email) formData.append('email', updateUserProfileData.email);
            if (updateUserProfileData.dob) formData.append('dob', updateUserProfileData.dob);
            if (updateUserProfileData.gender) formData.append('gender', updateUserProfileData.gender);
            if (updateUserProfileData.city) formData.append('city', updateUserProfileData.city);
            if (updateUserProfileData.state) formData.append('state', updateUserProfileData.state);
            if (updateUserProfileData.country) formData.append('country', updateUserProfileData.country);
            if (updateUserProfileData.height) formData.append('height', updateUserProfileData.height.toString());
            if (updateUserProfileData.weight) formData.append('weight', updateUserProfileData.weight.toString());
            if (updateUserProfileData.activityLevel) formData.append('activityLevel', updateUserProfileData.activityLevel);

            // Add profile picture if provided
            if (updateUserProfileData.profilePic) {
                formData.append('profilePic', updateUserProfileData.profilePic);
            }

            const response = await apiClient.put(`/profile/update`, formData, {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
            });
            
            const updateUserDetailsRes = updateUserDetailsResSchema.safeParse(response.data?.data);

            if (!updateUserDetailsRes.success) {
                throw new Error("Unable to update user profile. Please try again later.");
            }

            return {
                success: true,
                data: updateUserDetailsRes.data
            };
        } catch (error) {
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    }
};

export default userService;
