import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import apiClient, { createErrorResponse } from '../axios/axiosInstance';
import loginResponseSchema, { loginResponseSchemaType } from '../../screens/Auth/schemas/loginResponseSchema';
import refreshAccessTokenResponseSchema, { refreshAccessTokenResponseSchemaType } from '../../screens/Auth/schemas/refreshAccessTokenSchema';
import { ResponseSuccessType } from '../../types/ResponseSuccessType';
import { ResponseErrorType } from '../../types/ResponseErrorType';
import completeUserDetailsResSchema, { CompleteUserDetailsResType } from '../../screens/Onboarding/schemas/completeUserDetailsResSchema';
import { CompleteUserDetailsReqType } from '../../screens/Onboarding/schemas/CompleteUserDetailsReqSchema';
import { USER_STORAGE_KEY } from '../../store/constants/store_keys';

const apiUrl = process.env.EXPO_PUBLIC_API_URL;

const authService = {
    login: async ({ email, password }: { email: string, password: string }):Promise<ResponseSuccessType<loginResponseSchemaType> | ResponseErrorType> => {
        try {
            const response = await apiClient.post(`/login`, { email, password });

            const loginResponse = loginResponseSchema.safeParse(response.data);

            if (!loginResponse.success) {
                throw new Error("Unable to login. Please try again later.");
            }

            apiClient.defaults.headers.common['Authorization'] = `Bearer ${loginResponse.data.accessToken}`;

            return {
                success: true,
                data: loginResponse.data
            };

        } catch (error) {
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    register: async ({ firstName, lastName, email, password, acceptTerms, timeZone }: { firstName: string, lastName: string, email: string, password: string, acceptTerms: boolean, timeZone: string }):Promise<ResponseSuccessType<null> | ResponseErrorType> => {
        try {
            await apiClient.post(`/register`, {
                firstName, lastName, email, password, confirmPassword: password, acceptTerms, timeZone 
            });

            return {
                success: true,
                data: null
            };
        } catch (error) {
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    forgotPassword: async ({email}: {email:string}):Promise<ResponseSuccessType<string> | ResponseErrorType> => {
        try {
            await apiClient.put(`/forget_password`, { email });
            return { success: true, data: "Password reset email sent!" };
        } catch (error) {
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    logout: async ({ deviceId }: { deviceId: string }) => {
        try {
            await apiClient.get(`/logout/${deviceId}`);
            delete apiClient.defaults.headers.common['Authorization'];

            return {
                success: true
            };
        } catch (error) {
            return {
                success: false
            };
        }
    },

    resendVerificationLink: async (email: string):Promise<ResponseSuccessType<null> | ResponseErrorType> => {
        try {
            await apiClient.put(`/resend_verification_email`, { email });
            return { success: true, data: null };
        } catch (error) {
            return { success: false, error: createErrorResponse(error) };
        }
    },

    // updateUserTimeZone: async () => {
    //     try {
    //         await apiClient.put(`/timezone_update`, { timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone });
    //         return {
    //             success: true
    //         };
    //     }
    //     catch (error) {
    //         return {
    //             success: false,
    //             error: error?.message || 'Error updating timezone'
    //         };
    //     }
    // },

    refreshAccessToken: async ():Promise<ResponseSuccessType<refreshAccessTokenResponseSchemaType> | ResponseErrorType> => {
        try {
            const userData = await SecureStore.getItemAsync(USER_STORAGE_KEY);

            if (!userData) {
                throw new Error("Session expired. Please login again.");
            }

            const refreshToken = JSON.parse(userData)?.state?.refreshToken;

            const response = await axios.put(`${apiUrl}/token/refresh`, {
                refreshToken
            });

            const refreshAccessTokenResponse = refreshAccessTokenResponseSchema.safeParse(response?.data);

            if (!refreshAccessTokenResponse.success) {
                throw new Error("Unable to refresh access token. Please try again later.");
            }

            return {
                success: true,
                data: refreshAccessTokenResponse.data
            };
        } catch (error) {
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    completeUserProfile: async (completeUserProfileData:CompleteUserDetailsReqType ):Promise<ResponseSuccessType<CompleteUserDetailsResType> | ResponseErrorType> => {
        try {
            const response = await apiClient.put(`/profile/complete`, completeUserProfileData);
            
            const completeUserDetailsRes = completeUserDetailsResSchema.safeParse(response.data);

            if (!completeUserDetailsRes.success) {
                throw new Error("Unable to complete user profile. Please try again later.");
            }

            return {
                success: true,
                data: completeUserDetailsRes.data
            };
        } catch (error) {
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    }

};

export default authService;
