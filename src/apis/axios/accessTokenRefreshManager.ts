import authService from '../auth/authService';
import * as SecureStore from 'expo-secure-store';
import userStore from '../../store/UserStore';

class TokenRefreshManager {
    private static instance: TokenRefreshManager | null = null;
    private isRefreshing = false;
    private failedQueue: { resolve: (token: string) => void; reject: (err: any) => void }[] = [];

    constructor() {
        if (TokenRefreshManager.instance) {
            return TokenRefreshManager.instance;
        }
        
        this.isRefreshing = false;
        this.failedQueue = [];
        
        TokenRefreshManager.instance = this;
    }

    static getInstance() {
        if (!TokenRefreshManager.instance) {
            TokenRefreshManager.instance = new TokenRefreshManager();
        }
        return TokenRefreshManager.instance;
    }

    processQueue(error: any, token: string) {
        this.failedQueue.forEach(({ resolve, reject }) => {
            if (error) {
                reject(error);
            } else {
                resolve(token);
            }
        });
        this.failedQueue = [];
    }

    async refreshToken() {
        if (this.isRefreshing) {
            return new Promise((resolve, reject) => {
                this.failedQueue.push({ resolve, reject });
            });
        }

        this.isRefreshing = true;

        try {
            const refreshResponse = await authService.refreshAccessToken();
            
            if (refreshResponse.success) {
                const newToken = refreshResponse.data.accessToken;
                
                // Update stored auth data - Use the same key as the interceptor
                const userDataString = await SecureStore.getItemAsync('user-store'); // Changed from 'auth-storage'
                if (userDataString) {
                    const userData = JSON.parse(userDataString);
                    const updatedUserData = {
                        ...userData,
                        state: {
                            ...userData.state,
                            userToken: newToken,
                            userTokenExpiry: refreshResponse.data.expiry
                        }
                    };
                    await SecureStore.setItemAsync('user-store', JSON.stringify(updatedUserData)); // Changed from 'auth-storage'
                }
                
                this.processQueue(null, newToken);
                return newToken;
            } else {
                throw new Error('Token refresh failed');
            }
        } catch (error) {
            this.processQueue(error, '');
            userStore.getState().clearUserData();
            throw error;
        } finally {
            this.isRefreshing = false;
        }
    }

    // Method to reset state (useful for testing or logout)
    reset() {
        this.isRefreshing = false;
        this.failedQueue = [];
    }

    // Method to clear singleton (useful for testing)
    static clearInstance() {
        TokenRefreshManager.instance = null;
    }
}

export default TokenRefreshManager;