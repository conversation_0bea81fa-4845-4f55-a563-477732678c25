import axios from "axios";

// process.env.SERVER_URL

const apiClient = axios.create({
  baseURL: `${process.env.EXPO_PUBLIC_API_URL}`,
  timeout: 15000,
  headers: {
    "Content-Type": "application/json",
  },
});

export type ErrorResponse = {
  message: string;
  code: string;
  status: number;
  data?: unknown;
};

export function createErrorResponse(
  error: unknown,
  customCode: string | null = null
): ErrorResponse {
  if (axios.isAxiosError(error)) {
    if (error.response) {
      // Server responded with error status
      const errorData = error.response.data || {};
      return {
        message: (errorData as any).message || `HTTP Error ${error.response.status}`,
        status: error.response.status,
        code: customCode || (errorData as any).code || "HTTP_ERROR",
        data: errorData,
      };
    } else if (error.request) {
      // Network error
      return {
        message: "Network error. Please check your internet connection.",
        code: customCode || "NETWORK_ERROR",
        status: 0,
      };
    }
  }

  // Other errors (JS / React Native)
  if (error instanceof Error) {
    return {
      message: error.message || "An unexpected error occurred.",
      code: customCode || "UNKNOWN_ERROR",
      status: 0,
    };
  }

  // Fallback (if error is not an instance of Error)
  return {
    message: "An unexpected error occurred.",
    code: customCode || "UNKNOWN_ERROR",
    status: 0,
  };
}

export default apiClient;
