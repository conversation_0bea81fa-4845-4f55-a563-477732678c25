import * as SecureStore from 'expo-secure-store';
import TokenRefreshManager from './accessTokenRefreshManager';
import userStore from '../../store/UserStore';
import apiClient from './axiosInstance';
import { USER_STORAGE_KEY } from '../../store/constants/store_keys';
const tokenManager = TokenRefreshManager.getInstance();

let requestInterceptor: number | null = null;
let responseInterceptor: number | null = null;

const setAxiosInterceptors = () => {
    // Prevent adding multiple interceptors
    if (requestInterceptor !== null) apiClient.interceptors.request.eject(requestInterceptor);
    if (responseInterceptor !== null) apiClient.interceptors.response.eject(responseInterceptor);

    // Request Interceptor
    requestInterceptor = apiClient.interceptors.request.use(
        async (config) => {
            try {
                const existingAuth = config.headers?.Authorization || config.headers?.authorization;

                if (!existingAuth) {
                    const userData = await SecureStore.getItemAsync(USER_STORAGE_KEY);
                    if (userData) {
                        const parsedData = JSON.parse(userData);
                        const accessToken = parsedData?.state?.userToken;

                        if (accessToken) {
                            config.headers.set("Authorization", `Bearer ${accessToken}`);
                        }
                    }
                }
            } catch (error) { }
            return config;
        },
        (error) => {
            return Promise.reject(error);
        }
    );

    // Response Interceptor
    responseInterceptor = apiClient.interceptors.response.use(
        (response) => response,
        async (error) => {
            const originalRequest = error.config;
            const isAuthenticated = userStore.getState().isAuthenticated;
            
            if (error.response?.status === 401 && !originalRequest._retry && isAuthenticated) {
                originalRequest._retry = true;

                try {
                    const newToken = await tokenManager.refreshToken();
                    originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
                    return apiClient(originalRequest);
                } catch (refreshError) {
                    return Promise.reject(refreshError);
                }
            }

            return Promise.reject(error);
        }
    );
};

export default setAxiosInterceptors;