import apiClient, { createErrorResponse } from '../axios/axiosInstance';
import { ResponseSuccessType } from '../../types/ResponseSuccessType';
import { ResponseErrorType } from '../../types/ResponseErrorType';
import dispenseResponseSchema, { DispenseResponseType } from './schema/dispenseResponseSchema';
import { DispenseHistoryResponseType } from './schema/dispenseHIstoryResponseSchema';
import { getTodayDoseStatusResSchema, GetTodayDoseStatusResType } from './schema/getTodayDoseStatusResSchema';

const dispenseService = {
    dispenseTablet: async (cartridgeId: string): Promise<ResponseSuccessType<DispenseResponseType> | ResponseErrorType> => {
        try {
            const response = await apiClient.post(`/dispense/tablet/${cartridgeId}`);

            const dispenseResponse = dispenseResponseSchema.safeParse(response.data?.data);

            if (!dispenseResponse.success) {
                throw new Error("Unable to dispense tablet. Please try again later.");
            }

            return {
                success: true,
                data: dispenseResponse.data
            };

        } catch (error) {
            console.log(error);
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    getDispenseHistory: async (page: number = 0): Promise<ResponseSuccessType<DispenseHistoryResponseType> | ResponseErrorType> => {
        try {
            const response = await apiClient.get(`/dispense/history?page=${page}`);

            return {
                success: true,
                data: response.data?.data
            };

        } catch (error) {
            console.log(error);
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    deleteDispenseRecord: async (dispenseId: string): Promise<ResponseSuccessType<string> | ResponseErrorType> => {
        try {
            await apiClient.put(`/dispense/record/${dispenseId}`);

            return {
                success: true,
                data: 'Record deleted successfully'
            };

        } catch (error) {
            console.log(error);
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    getTodayDoseStatus: async (): Promise<ResponseSuccessType<GetTodayDoseStatusResType> | ResponseErrorType> => {
        try {
            const response = await apiClient.get(`/dispense/has-taken-today`);

            const hasTakenTodayResponse = getTodayDoseStatusResSchema.safeParse(response.data?.data);

            if (!hasTakenTodayResponse.success) {
                throw new Error("Unable to check if tablet has been taken today. Please try again later.");
            }

            return {
                success: true,
                data: hasTakenTodayResponse.data
            };

        } catch (error) {
            console.log(error);
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    }
};

export default dispenseService;