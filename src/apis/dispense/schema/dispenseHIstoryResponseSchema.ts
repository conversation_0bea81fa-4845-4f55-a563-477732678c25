import { z } from "zod";

export const RdaSchema = z.object({
  quantity: z.number(),
  unit: z.string(),
  percentage: z.number().nullable(),
});

export const NutrientSchema = z.object({
  name: z.string(),
  rda: RdaSchema,
});

export const ColourSchema = z.object({
  cartridge: z.string(),
  tablet: z.string(),
});

export const CartridgeSchema = z.object({
  healthName: z.string(),
  healthArea: z.string(),
  flavour: z.string(),
  colour: ColourSchema,
  nutrients: z.array(NutrientSchema),
});

export const DispenseHistoryRecordSchema = z.object({
  id: z.string(),
  cartridge: CartridgeSchema,
  dispenseDate: z.date(),
  dispenseTime: z.date(),
});

export const DispenseHistoryResponseSchema = z.object({
  dispenseHistory: z.array(DispenseHistoryRecordSchema),
  totalCurrentRecords: z.number(),
  totalRecords: z.number(),
});

export type DispenseHistoryRecordType = z.infer<typeof DispenseHistoryRecordSchema>;
export type DispenseHistoryResponseType = z.infer<typeof DispenseHistoryResponseSchema>;