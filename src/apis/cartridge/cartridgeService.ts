import apiClient, { createErrorResponse } from '../axios/axiosInstance';
import { ResponseSuccessType } from '../../types/ResponseSuccessType';
import { ResponseErrorType } from '../../types/ResponseErrorType';
import cartridgeDetailsResponseSchema, { CartridgeDetailsResponseType } from './schema/cartridgeDetailsResponseSchema';
import userStockResponseSchema, { UserStockResponseType } from './schema/userStockResponseSchema';
import cartridgeModalDetailsResponseSchema, { CartridgeModalDetailsResponseType } from './schema/cartridgeModalDetailsResponseSchema';

const cartridgeService = {
    getCartridgeDetails: async (cartridgeId: string): Promise<ResponseSuccessType<CartridgeDetailsResponseType> | ResponseErrorType> => {
        try {
            const response = await apiClient.get(`/cartridges/${cartridgeId}`);

            const cartridgeDetailsResponse = cartridgeDetailsResponseSchema.safeParse(response.data);

            if (!cartridgeDetailsResponse.success) {
                throw new Error("Unable to fetch cartridge details. Please try again later.");
            }

            return {
                success: true,
                data: cartridgeDetailsResponse.data
            };

        } catch (error) {
            console.log(error);
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    getUserStock: async (): Promise<ResponseSuccessType<UserStockResponseType> | ResponseErrorType> => {
        try {
            const response = await apiClient.get(`/cartridges/`);

            const userStockResponse = userStockResponseSchema.safeParse(response.data);

            if (!userStockResponse.success) {
                throw new Error("Unable to fetch user stock. Please try again later.");
            }

            return {
                success: true,
                data: userStockResponse.data
            };

        } catch (error) {
            console.log(error);
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    updateUserStock: async (cartridgeId: string, quantity: number): Promise<ResponseSuccessType<null> | ResponseErrorType> => {
        try {
            await apiClient.put(`/cartridges/${cartridgeId}`, { quantity });

            return {
                success: true,
                data: null
            };

        } catch (error) {
            console.log(error);
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    },

    getCartridgeModalDetails: async (cartridgeModalId: string): Promise<ResponseSuccessType<CartridgeModalDetailsResponseType> | ResponseErrorType> => {
        try {
            const response = await apiClient.get(`/cartridges/modal/${cartridgeModalId}`);

            const cartridgeDetailsResponse = cartridgeModalDetailsResponseSchema.safeParse(response?.data?.data);

            if (!cartridgeDetailsResponse.success) {
                throw new Error("Unable to fetch cartridge details. Please try again later.");
            }

            return {
                success: true,
                data: cartridgeDetailsResponse.data
            };

        } catch (error) {
            console.log(error);
            return {
                success: false,
                error: createErrorResponse(error)
            };
        }
    }
};

export default cartridgeService;