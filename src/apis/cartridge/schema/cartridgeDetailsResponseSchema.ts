import { z, infer as zInfer } from 'zod';

const rdaSchema = z.object({
    quantity: z.number(),
    unit: z.string(),
    percentage: z.number().nullable(),
});

const nutrientSchema = z.object({
    name: z.string(),
    rda: rdaSchema,
});

const colourSchema = z.object({
    cartridge: z.string(),
    tablet: z.string(),
});

const cartridgeDetailsSchema = z.object({
    id: z.string(),
    healthName: z.string(),
    healthArea: z.string(),
    flavour: z.string(),
    colour: colourSchema,
    nutrients: z.array(nutrientSchema),
});

const cartridgeDetailsResponseSchema = z.object({
    cartridgeId: z.string(),
    tablets: z.number(),
    cartridgeDetails: cartridgeDetailsSchema,
    currentCount: z.number(),
});

export default cartridgeDetailsResponseSchema;

export type CartridgeDetailsResponseType = zInfer<typeof cartridgeDetailsResponseSchema>;
export type CartridgeDetailsType = zInfer<typeof cartridgeDetailsSchema>;
export type NutrientType = zInfer<typeof nutrientSchema>;
export type RDAType = zInfer<typeof rdaSchema>;
export type ColourType = zInfer<typeof colourSchema>;