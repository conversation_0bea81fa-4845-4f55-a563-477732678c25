import { z, infer as zInfer } from 'zod';

const rdaSchema = z.object({
  quantity: z.number(),
  unit: z.string(),
  percentage: z.number().nullable(),
});

const nutrientSchema = z.object({
  name: z.string(),
  rda: rdaSchema,
});

const colourSchema = z.object({
  cartridge: z.string(),
  tablet: z.string(),
});

const cartridgeDetailsSchema = z.object({
  id: z.string(),
  healthName: z.string(),
  healthArea: z.string(),
  flavour: z.string(),
  colour: colourSchema,
  nutrients: z.array(nutrientSchema),
});

const cartridgeModalDetailsResponseSchema = z.object({
  cartridge: cartridgeDetailsSchema,
  quantity: z.number(),
});

export default cartridgeModalDetailsResponseSchema;

export type CartridgeModalDetailsResponseType = zInfer<typeof cartridgeModalDetailsResponseSchema>;
