import { PIECE_QUANTITY_TYPE, STANDARD_QUANTITY_TYPE } from './enum';

export interface IngredientData {
  id: string;
  name: string;
  ingredients: string[];
  thumbnailUrl: string;
  ingredientNutritionByQuantity: ingredientNutritionByQuantity[];
  isDeleted: boolean;
}

export interface ingredientNutritionByQuantity {
  quantity: STANDARD_QUANTITY_TYPE | PIECE_QUANTITY_TYPE;
  protein: number;
  calories: number;
  fats: number;
  fiber: number;
  carbs: number;
}
