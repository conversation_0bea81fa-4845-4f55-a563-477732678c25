import { QUERY_CATEGORY, QUERY_STATUS } from './enum';

export interface ContactQueryData {
  id: string;
  category: string;
  description: string;
  status: QUERY_STATUS;
  user?: {
    id: string;
    email: string;
    name: string;
  };
  attachments?: [
    {
      url: string;
      type: string;
    },
  ];
}

export const categoryColorMap: Record<QUERY_CATEGORY, string> = {
  [QUERY_CATEGORY.DEVICE_PROBLEM]: 'blue', // Blue for "Application"
  [QUERY_CATEGORY.DATA_INACCURACIES]: 'orange', // Green for "Account"
  [QUERY_CATEGORY.APP_SUGGESTIONS]: 'red', // Yellow for "Device"
  [QUERY_CATEGORY.LAGGING_AND_GLITCHES]: 'purple', // Purple for "Feedback"
};
