import { HUNGER_LEVELS, MOOD_TYPES } from './enum';

export interface MoodVideoData {
  id: string;
  title: string;
  description: string;
  moodType: string;
  hungerLevel: string;
  isEmbeddedUrl: boolean;
  thumbnailUrl: string;
  videoUrl: string;
  isPublished: boolean;
  isRecommended: boolean;
  isDeleted: boolean;
}

export const MOOD_COLORS: Record<MOOD_TYPES, string> = {
  [MOOD_TYPES.HAPPY]: 'blue',
  [MOOD_TYPES.MODERATELY_HAPPY]: 'blue',
  [MOOD_TYPES.IRRITATED]: 'yellow',
  [MOOD_TYPES.ANXIOUS]: 'orange',
  [MOOD_TYPES.SAD]: 'red',
};

export const HUNGER_COLORS: Record<HUNGER_LEVELS, string> = {
  [HUNGER_LEVELS.MILD]: 'cyan',
  [HUNGER_LEVELS.BARELY]: 'blue',
  [HUNGER_LEVELS.MODERATE]: 'orange',
  [HUNGER_LEVELS.HIGH]: 'red',
};
