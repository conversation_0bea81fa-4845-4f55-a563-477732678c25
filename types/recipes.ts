import {
  SERVING_NUTRITION_QUANTITY,
  SLICE_NUTRITION_QUANTITY,
  PIECE_NUTRITION_QUANTITY,
} from './enum';

export interface RecipeData {
  id: string;
  title: string;
  ingredients: string[];
  directions: string;
  timeToPrep: number; // always in minutes
  numOfServings: number;
  thumbnailUrl: string;
  totalCalories: number;
  mealType: string;
  category: string;
  nutritionByQuantity: RecipesNutritionByQuantityData[];
  isPublished: boolean;
  isDeleted: boolean;
  author?: string;
}

export interface RecipesNutritionByQuantityData {
  quantity: SERVING_NUTRITION_QUANTITY | SLICE_NUTRITION_QUANTITY | PIECE_NUTRITION_QUANTITY;
  protein: number;
  calories: number;
  fats: number;
  fiber: number;
  carbs: number;
}
