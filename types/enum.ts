export enum BasicStatus {
  DISABLE,
  ENABLE,
}

export enum ResultEnum {
  SUCCESS = 0,
  ERROR = -1,
  TIMEOUT = 401,
}

export enum StorageEnum {
  User = 'user',
  Token = 'token',
  Permissions = 'permissions',
  Roles = 'roles',
  Settings = 'settings',
  I18N = 'i18nextLng',
  OTPAccessToken = 'otpAccessToken',
}

export enum ThemeMode {
  Light = 'light',
  Dark = 'dark',
}

export enum ThemeLayout {
  Vertical = 'vertical',
  Horizontal = 'horizontal',
  Mini = 'mini',
}

export enum ThemeColorPresets {
  Default = 'default',
  Secondary = 'secondary',
  Cyan = 'cyan',
  Purple = 'purple',
  Blue = 'blue',
  Orange = 'orange',
  Red = 'red',
  Darkblue = 'darkblue',
  lightBlue = 'lightBlue',
  VerylightBlue = 'verylightBlue',
  veryDarkBlue = 'veryDarkBlue',
  Slate = 'slate',
  White = 'white',
}

export enum LocalEnum {
  en_US = 'en_US',
}

export enum MultiTabOperation {
  FULLSCREEN = 'fullscreen',
  REFRESH = 'refresh',
  CLOSE = 'close',
  CLOSEOTHERS = 'closeOthers',
  CLOSEALL = 'closeAll',
  CLOSELEFT = 'closeLeft',
  CLOSERIGHT = 'closeRight',
}

export enum PermissionType {
  CATALOGUE,
  MENU,
  BUTTON,
}

export enum StatusColor {
  Started = 'processing',
  Closed = 'success',
  Upcoming = 'warning',
  Default = 'default',
}

export enum GENDER_TYPES {
  MALE = 'male',
  FEMALE = 'female',
  OTHERS = 'others',
}

export enum QUERY_STATUS {
  PENDING = 'pending',
  RESOLVED = 'resolved',
}

export enum QUERY_CATEGORY {
  DEVICE_PROBLEM = 'Device problem',
  DATA_INACCURACIES = 'Data inaccuracies ',
  APP_SUGGESTIONS = 'App suggestions',
  LAGGING_AND_GLITCHES = 'Lagging and glitches',
}
