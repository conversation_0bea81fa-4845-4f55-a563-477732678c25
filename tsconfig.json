{"compilerOptions": {"target": "ESNext", "module": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "useDefineForClassFields": true, "skipLibCheck": true, "allowJs": true, "moduleResolution": "bundler", "sourceMap": true, "declaration": true, "preserveWatchOutput": true, "removeComments": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "esModuleInterop": true, "strict": true, "strictNullChecks": true, "noImplicitAny": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "useUnknownInCatchVariables": false, "types": ["vite/client"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "#/*": ["types/*"]}}, "include": ["src", "types"], "exclude": ["node_modules", "dist"]}