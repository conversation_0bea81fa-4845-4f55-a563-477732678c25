{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "nouriq", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/adaptive-icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "scheme": "nouriq", "ios": {"icon": "./assets/ios-icon.png", "supportsTablet": true, "bundleIdentifier": "com.nouriq", "infoPlist": {"NSBluetoothAlwaysUsageDescription": "Allow $(PRODUCT_NAME) to use bluetooth to connect to your bottle."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.nouriq", "softwareKeyboardLayoutMode": "pan", "permissions": ["ACCESS_FINE_LOCATION", "BLUETOOTH_SCAN", "BLUETOOTH_CONNECT", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"]}, "plugins": ["expo-asset", ["expo-splash-screen", {"backgroundColor": "#ffffff", "image": "./assets/adaptive-icon.png", "resizeMode": "cover", "imageWidth": 200, "dark": {"backgroundColor": "#ffffff", "image": "./assets/adaptive-icon.png", "resizeMode": "cover", "imageWidth": 200}}], "react-native-edge-to-edge", ["expo-font", {"fonts": ["./assets/fonts/helixa/Helixa-Black.ttf", "./assets/fonts/helixa/Helixa-BlackItalic.ttf", "./assets/fonts/helixa/Helixa-Bold.ttf", "./assets/fonts/helixa/Helixa-BoldItalic.ttf", "./assets/fonts/helixa/Helixa-Book.ttf", "./assets/fonts/helixa/Helixa-BookItalic.ttf", "./assets/fonts/helixa/Helixa-Italic.ttf", "./assets/fonts/helixa/Helixa-Light.ttf", "./assets/fonts/helixa/Helixa-LightItalic.ttf", "./assets/fonts/helixa/Helixa-Regular.ttf", "./assets/fonts/helixa/Helixa-Thin.ttf", "./assets/fonts/helixa/Helixa-ThinItalic.ttf", "./assets/fonts/chemina/Chemian-Italic.ttf", "./assets/fonts/chemina/Chemian-Regular.ttf"]}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."}]], "runtimeVersion": "1.0.0", "extra": {"eas": {"projectId": "9f401f32-4324-49a4-a9dd-a1e19614659d"}}}}